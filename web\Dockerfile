FROM kc-manage-storage.manascloud.com:34480/manas-library/nginx:alpine
LABEL maintainer="manascloud"

# 设置容器内的时区为东八区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone

# 创建目录
RUN mkdir -p /home/<USER>/www/dist

# 指定路径
WORKDIR /home/<USER>/www/dist

# 复制conf文件到路径
COPY conf/nginx.conf /etc/nginx/conf.d/default.conf


# 复制html文件到dist中存放前端项目的打包文件
COPY dist /home/<USER>/www/dist

EXPOSE 80
