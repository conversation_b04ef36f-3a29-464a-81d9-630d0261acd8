



    server {
        listen       80;
        server_name  localhost;
        client_max_body_size 100m;


         location /websocket/ {
            proxy_read_timeout 300s;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_pass http://sparkchain-app:8080/websocket/;
        }


      location / {
            root   /home/<USER>/www/dist;
            try_files $uri $uri/ /index.html;
            index  index.html index.htm;
        }


      location /prod-api/{
         proxy_set_header Host $http_host;
         proxy_set_header X-Real-IP $remote_addr;
         proxy_set_header REMOTE-HOST $remote_addr;
         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
         #自己服务器地址：没改的话就是默认8080, 要与后端容器进行link，别名为app
         proxy_pass http://sparkchain-app:8080/;
      }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }





