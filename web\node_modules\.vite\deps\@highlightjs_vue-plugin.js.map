{"version": 3, "sources": ["../../highlight.js/es/core.js", "../../@highlightjs/vue-plugin/dist/highlightjs-vue.esm.min.js"], "sourcesContent": ["// https://nodejs.org/api/packages.html#packages_writing_dual_packages_while_avoiding_or_minimizing_hazards\nimport HighlightJS from '../lib/core.js';\nexport { HighlightJS };\nexport default HighlightJS;\n", "import{defineComponent as e,ref as l,watch as a,computed as t,h as n}from\"vue\";import u from\"highlight.js/lib/core\";var r=e({props:{code:{type:String,required:!0},language:{type:String,default:\"\"},autodetect:{type:Boolean,default:!0},ignoreIllegals:{type:Boolean,default:!0}},setup:function(e){var n=l(e.language);a((function(){return e.language}),(function(e){n.value=e}));var r=t((function(){return e.autodetect||!n.value})),o=t((function(){return!r.value&&!u.getLanguage(n.value)}));return{className:t((function(){return o.value?\"\":\"hljs \"+n.value})),highlightedCode:t((function(){var l;if(o.value)return console.warn('The language \"'+n.value+'\" you specified could not be found.'),e.code.replace(/&/g,\"&amp;\").replace(/</g,\"&lt;\").replace(/>/g,\"&gt;\").replace(/\"/g,\"&quot;\").replace(/'/g,\"&#x27;\");if(r.value){var a=u.highlightAuto(e.code);return n.value=null!==(l=a.language)&&void 0!==l?l:\"\",a.value}return(a=u.highlight(e.code,{language:n.value,ignoreIllegals:e.ignoreIllegals})).value}))}},render:function(){return n(\"pre\",{},[n(\"code\",{class:this.className,innerHTML:this.highlightedCode})])}}),o={install:function(e){e.component(\"highlightjs\",r)},component:r};export default o;\n"], "mappings": ";;;;;;;;;;;;;;;AACA,kBAAwB;AAExB,IAAO,eAAQ,YAAAA;;;ACHqG,IAAI,IAAE,gBAAE,EAAC,OAAM,EAAC,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,UAAS,EAAC,MAAK,QAAO,SAAQ,GAAE,GAAE,YAAW,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,gBAAe,EAAC,MAAK,SAAQ,SAAQ,KAAE,EAAC,GAAE,OAAM,SAAS,GAAE;AAAC,MAAI,IAAE,IAAE,EAAE,QAAQ;AAAE,QAAG,WAAU;AAAC,WAAO,EAAE;AAAA,EAAQ,GAAI,SAASC,IAAE;AAAC,MAAE,QAAMA;AAAA,EAAC,CAAE;AAAE,MAAIC,KAAE,SAAG,WAAU;AAAC,WAAO,EAAE,cAAY,CAAC,EAAE;AAAA,EAAK,CAAE,GAAEC,KAAE,SAAG,WAAU;AAAC,WAAM,CAACD,GAAE,SAAO,CAAC,aAAE,YAAY,EAAE,KAAK;AAAA,EAAC,CAAE;AAAE,SAAM,EAAC,WAAU,SAAG,WAAU;AAAC,WAAOC,GAAE,QAAM,KAAG,UAAQ,EAAE;AAAA,EAAK,CAAE,GAAE,iBAAgB,SAAG,WAAU;AAAC,QAAI;AAAE,QAAGA,GAAE,MAAM,QAAO,QAAQ,KAAK,mBAAiB,EAAE,QAAM,qCAAqC,GAAE,EAAE,KAAK,QAAQ,MAAK,OAAO,EAAE,QAAQ,MAAK,MAAM,EAAE,QAAQ,MAAK,MAAM,EAAE,QAAQ,MAAK,QAAQ,EAAE,QAAQ,MAAK,QAAQ;AAAE,QAAGD,GAAE,OAAM;AAAC,UAAI,IAAE,aAAE,cAAc,EAAE,IAAI;AAAE,aAAO,EAAE,QAAM,UAAQ,IAAE,EAAE,aAAW,WAAS,IAAE,IAAE,IAAG,EAAE;AAAA,IAAK;AAAC,YAAO,IAAE,aAAE,UAAU,EAAE,MAAK,EAAC,UAAS,EAAE,OAAM,gBAAe,EAAE,eAAc,CAAC,GAAG;AAAA,EAAK,CAAE,EAAC;AAAC,GAAE,QAAO,WAAU;AAAC,SAAO,EAAE,OAAM,CAAC,GAAE,CAAC,EAAE,QAAO,EAAC,OAAM,KAAK,WAAU,WAAU,KAAK,gBAAe,CAAC,CAAC,CAAC;AAAC,EAAC,CAAC;AAA19B,IAA49B,IAAE,EAAC,SAAQ,SAAS,GAAE;AAAC,IAAE,UAAU,eAAc,CAAC;AAAC,GAAE,WAAU,EAAC;AAAE,IAAO,kCAAQ;", "names": ["HighlightJS", "e", "r", "o"]}