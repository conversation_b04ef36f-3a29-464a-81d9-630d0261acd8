{"version": 3, "sources": ["../../await-to-js/src/await-to-js.ts"], "sourcesContent": ["/**\n * @param { Promise } promise\n * @param { Object= } errorExt - Additional Information you can pass to the err object\n * @return { Promise }\n */\nexport function to<T, U = Error> (\n  promise: Promise<T>,\n  errorExt?: object\n): Promise<[U, undefined] | [null, T]> {\n  return promise\n    .then<[null, T]>((data: T) => [null, data])\n    .catch<[U, undefined]>((err: U) => {\n      if (errorExt) {\n        Object.assign(err, errorExt);\n      }\n\n      return [err, undefined];\n    });\n}\n\nexport default to;\n"], "mappings": ";;;AAKA,SAAA,GACE,SACA,UAAiB;AAEjB,SAAO,QACJ,KAAgB,SAAC,MAAO;AAAK,WAAA,CAAC,MAAM,IAAI;EAAC,CAAA,EACzC,MAAsB,SAAC,KAAM;AAC5B,QAAI,UAAU;AACZ,aAAO,OAAO,KAAK,QAAQ;;AAG7B,WAAO,CAAC,KAAK,MAAS;GACvB;;A", "names": []}