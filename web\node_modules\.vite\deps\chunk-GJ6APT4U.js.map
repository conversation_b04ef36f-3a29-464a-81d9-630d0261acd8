{"version": 3, "sources": ["../../highlight.js/lib/core.js"], "sourcesContent": ["/* eslint-disable no-multi-assign */\n\nfunction deepFreeze(obj) {\n  if (obj instanceof Map) {\n    obj.clear =\n      obj.delete =\n      obj.set =\n        function () {\n          throw new Error('map is read-only');\n        };\n  } else if (obj instanceof Set) {\n    obj.add =\n      obj.clear =\n      obj.delete =\n        function () {\n          throw new Error('set is read-only');\n        };\n  }\n\n  // Freeze self\n  Object.freeze(obj);\n\n  Object.getOwnPropertyNames(obj).forEach((name) => {\n    const prop = obj[name];\n    const type = typeof prop;\n\n    // Freeze prop if it is an object or function and also not already frozen\n    if ((type === 'object' || type === 'function') && !Object.isFrozen(prop)) {\n      deepFreeze(prop);\n    }\n  });\n\n  return obj;\n}\n\n/** @typedef {import('highlight.js').CallbackResponse} CallbackResponse */\n/** @typedef {import('highlight.js').CompiledMode} CompiledMode */\n/** @implements CallbackResponse */\n\nclass Response {\n  /**\n   * @param {CompiledMode} mode\n   */\n  constructor(mode) {\n    // eslint-disable-next-line no-undefined\n    if (mode.data === undefined) mode.data = {};\n\n    this.data = mode.data;\n    this.isMatchIgnored = false;\n  }\n\n  ignoreMatch() {\n    this.isMatchIgnored = true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction escapeHTML(value) {\n  return value\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;');\n}\n\n/**\n * performs a shallow merge of multiple objects into one\n *\n * @template T\n * @param {T} original\n * @param {Record<string,any>[]} objects\n * @returns {T} a single new object\n */\nfunction inherit$1(original, ...objects) {\n  /** @type Record<string,any> */\n  const result = Object.create(null);\n\n  for (const key in original) {\n    result[key] = original[key];\n  }\n  objects.forEach(function(obj) {\n    for (const key in obj) {\n      result[key] = obj[key];\n    }\n  });\n  return /** @type {T} */ (result);\n}\n\n/**\n * @typedef {object} Renderer\n * @property {(text: string) => void} addText\n * @property {(node: Node) => void} openNode\n * @property {(node: Node) => void} closeNode\n * @property {() => string} value\n */\n\n/** @typedef {{scope?: string, language?: string, sublanguage?: boolean}} Node */\n/** @typedef {{walk: (r: Renderer) => void}} Tree */\n/** */\n\nconst SPAN_CLOSE = '</span>';\n\n/**\n * Determines if a node needs to be wrapped in <span>\n *\n * @param {Node} node */\nconst emitsWrappingTags = (node) => {\n  // rarely we can have a sublanguage where language is undefined\n  // TODO: track down why\n  return !!node.scope;\n};\n\n/**\n *\n * @param {string} name\n * @param {{prefix:string}} options\n */\nconst scopeToCSSClass = (name, { prefix }) => {\n  // sub-language\n  if (name.startsWith(\"language:\")) {\n    return name.replace(\"language:\", \"language-\");\n  }\n  // tiered scope: comment.line\n  if (name.includes(\".\")) {\n    const pieces = name.split(\".\");\n    return [\n      `${prefix}${pieces.shift()}`,\n      ...(pieces.map((x, i) => `${x}${\"_\".repeat(i + 1)}`))\n    ].join(\" \");\n  }\n  // simple scope\n  return `${prefix}${name}`;\n};\n\n/** @type {Renderer} */\nclass HTMLRenderer {\n  /**\n   * Creates a new HTMLRenderer\n   *\n   * @param {Tree} parseTree - the parse tree (must support `walk` API)\n   * @param {{classPrefix: string}} options\n   */\n  constructor(parseTree, options) {\n    this.buffer = \"\";\n    this.classPrefix = options.classPrefix;\n    parseTree.walk(this);\n  }\n\n  /**\n   * Adds texts to the output stream\n   *\n   * @param {string} text */\n  addText(text) {\n    this.buffer += escapeHTML(text);\n  }\n\n  /**\n   * Adds a node open to the output stream (if needed)\n   *\n   * @param {Node} node */\n  openNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    const className = scopeToCSSClass(node.scope,\n      { prefix: this.classPrefix });\n    this.span(className);\n  }\n\n  /**\n   * Adds a node close to the output stream (if needed)\n   *\n   * @param {Node} node */\n  closeNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    this.buffer += SPAN_CLOSE;\n  }\n\n  /**\n   * returns the accumulated buffer\n  */\n  value() {\n    return this.buffer;\n  }\n\n  // helpers\n\n  /**\n   * Builds a span element\n   *\n   * @param {string} className */\n  span(className) {\n    this.buffer += `<span class=\"${className}\">`;\n  }\n}\n\n/** @typedef {{scope?: string, language?: string, children: Node[]} | string} Node */\n/** @typedef {{scope?: string, language?: string, children: Node[]} } DataNode */\n/** @typedef {import('highlight.js').Emitter} Emitter */\n/**  */\n\n/** @returns {DataNode} */\nconst newNode = (opts = {}) => {\n  /** @type DataNode */\n  const result = { children: [] };\n  Object.assign(result, opts);\n  return result;\n};\n\nclass TokenTree {\n  constructor() {\n    /** @type DataNode */\n    this.rootNode = newNode();\n    this.stack = [this.rootNode];\n  }\n\n  get top() {\n    return this.stack[this.stack.length - 1];\n  }\n\n  get root() { return this.rootNode; }\n\n  /** @param {Node} node */\n  add(node) {\n    this.top.children.push(node);\n  }\n\n  /** @param {string} scope */\n  openNode(scope) {\n    /** @type Node */\n    const node = newNode({ scope });\n    this.add(node);\n    this.stack.push(node);\n  }\n\n  closeNode() {\n    if (this.stack.length > 1) {\n      return this.stack.pop();\n    }\n    // eslint-disable-next-line no-undefined\n    return undefined;\n  }\n\n  closeAllNodes() {\n    while (this.closeNode());\n  }\n\n  toJSON() {\n    return JSON.stringify(this.rootNode, null, 4);\n  }\n\n  /**\n   * @typedef { import(\"./html_renderer\").Renderer } Renderer\n   * @param {Renderer} builder\n   */\n  walk(builder) {\n    // this does not\n    return this.constructor._walk(builder, this.rootNode);\n    // this works\n    // return TokenTree._walk(builder, this.rootNode);\n  }\n\n  /**\n   * @param {Renderer} builder\n   * @param {Node} node\n   */\n  static _walk(builder, node) {\n    if (typeof node === \"string\") {\n      builder.addText(node);\n    } else if (node.children) {\n      builder.openNode(node);\n      node.children.forEach((child) => this._walk(builder, child));\n      builder.closeNode(node);\n    }\n    return builder;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  static _collapse(node) {\n    if (typeof node === \"string\") return;\n    if (!node.children) return;\n\n    if (node.children.every(el => typeof el === \"string\")) {\n      // node.text = node.children.join(\"\");\n      // delete node.children;\n      node.children = [node.children.join(\"\")];\n    } else {\n      node.children.forEach((child) => {\n        TokenTree._collapse(child);\n      });\n    }\n  }\n}\n\n/**\n  Currently this is all private API, but this is the minimal API necessary\n  that an Emitter must implement to fully support the parser.\n\n  Minimal interface:\n\n  - addText(text)\n  - __addSublanguage(emitter, subLanguageName)\n  - startScope(scope)\n  - endScope()\n  - finalize()\n  - toHTML()\n\n*/\n\n/**\n * @implements {Emitter}\n */\nclass TokenTreeEmitter extends TokenTree {\n  /**\n   * @param {*} options\n   */\n  constructor(options) {\n    super();\n    this.options = options;\n  }\n\n  /**\n   * @param {string} text\n   */\n  addText(text) {\n    if (text === \"\") { return; }\n\n    this.add(text);\n  }\n\n  /** @param {string} scope */\n  startScope(scope) {\n    this.openNode(scope);\n  }\n\n  endScope() {\n    this.closeNode();\n  }\n\n  /**\n   * @param {Emitter & {root: DataNode}} emitter\n   * @param {string} name\n   */\n  __addSublanguage(emitter, name) {\n    /** @type DataNode */\n    const node = emitter.root;\n    if (name) node.scope = `language:${name}`;\n\n    this.add(node);\n  }\n\n  toHTML() {\n    const renderer = new HTMLRenderer(this, this.options);\n    return renderer.value();\n  }\n\n  finalize() {\n    this.closeAllNodes();\n    return true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(?:', re, ')*');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(?:', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * @param { Array<string | RegExp | Object> } args\n * @returns {object}\n */\nfunction stripOptionsFromArgs(args) {\n  const opts = args[args.length - 1];\n\n  if (typeof opts === 'object' && opts.constructor === Object) {\n    args.splice(args.length - 1, 1);\n    return opts;\n  } else {\n    return {};\n  }\n}\n\n/** @typedef { {capture?: boolean} } RegexEitherOptions */\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] | [...(RegExp | string)[], RegexEitherOptions]} args\n * @returns {string}\n */\nfunction either(...args) {\n  /** @type { object & {capture?: boolean} }  */\n  const opts = stripOptionsFromArgs(args);\n  const joined = '('\n    + (opts.capture ? \"\" : \"?:\")\n    + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/**\n * @param {RegExp | string} re\n * @returns {number}\n */\nfunction countMatchGroups(re) {\n  return (new RegExp(re.toString() + '|')).exec('').length - 1;\n}\n\n/**\n * Does lexeme start with a regular expression match at the beginning\n * @param {RegExp} re\n * @param {string} lexeme\n */\nfunction startsWith(re, lexeme) {\n  const match = re && re.exec(lexeme);\n  return match && match.index === 0;\n}\n\n// BACKREF_RE matches an open parenthesis or backreference. To avoid\n// an incorrect parse, it additionally matches the following:\n// - [...] elements, where the meaning of parentheses and escapes change\n// - other escape sequences, so we do not misparse escape sequences as\n//   interesting elements\n// - non-matching or lookahead parentheses, which do not capture. These\n//   follow the '(' with a '?'.\nconst BACKREF_RE = /\\[(?:[^\\\\\\]]|\\\\.)*\\]|\\(\\??|\\\\([1-9][0-9]*)|\\\\./;\n\n// **INTERNAL** Not intended for outside usage\n// join logically computes regexps.join(separator), but fixes the\n// backreferences so they continue to match.\n// it also places each individual regular expression into it's own\n// match group, keeping track of the sequencing of those match groups\n// is currently an exercise for the caller. :-)\n/**\n * @param {(string | RegExp)[]} regexps\n * @param {{joinWith: string}} opts\n * @returns {string}\n */\nfunction _rewriteBackreferences(regexps, { joinWith }) {\n  let numCaptures = 0;\n\n  return regexps.map((regex) => {\n    numCaptures += 1;\n    const offset = numCaptures;\n    let re = source(regex);\n    let out = '';\n\n    while (re.length > 0) {\n      const match = BACKREF_RE.exec(re);\n      if (!match) {\n        out += re;\n        break;\n      }\n      out += re.substring(0, match.index);\n      re = re.substring(match.index + match[0].length);\n      if (match[0][0] === '\\\\' && match[1]) {\n        // Adjust the backreference.\n        out += '\\\\' + String(Number(match[1]) + offset);\n      } else {\n        out += match[0];\n        if (match[0] === '(') {\n          numCaptures++;\n        }\n      }\n    }\n    return out;\n  }).map(re => `(${re})`).join(joinWith);\n}\n\n/** @typedef {import('highlight.js').Mode} Mode */\n/** @typedef {import('highlight.js').ModeCallback} ModeCallback */\n\n// Common regexps\nconst MATCH_NOTHING_RE = /\\b\\B/;\nconst IDENT_RE = '[a-zA-Z]\\\\w*';\nconst UNDERSCORE_IDENT_RE = '[a-zA-Z_]\\\\w*';\nconst NUMBER_RE = '\\\\b\\\\d+(\\\\.\\\\d+)?';\nconst C_NUMBER_RE = '(-?)(\\\\b0[xX][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)'; // 0x..., 0..., decimal, float\nconst BINARY_NUMBER_RE = '\\\\b(0b[01]+)'; // 0b...\nconst RE_STARTERS_RE = '!|!=|!==|%|%=|&|&&|&=|\\\\*|\\\\*=|\\\\+|\\\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\\\?|\\\\[|\\\\{|\\\\(|\\\\^|\\\\^=|\\\\||\\\\|=|\\\\|\\\\||~';\n\n/**\n* @param { Partial<Mode> & {binary?: string | RegExp} } opts\n*/\nconst SHEBANG = (opts = {}) => {\n  const beginShebang = /^#![ ]*\\//;\n  if (opts.binary) {\n    opts.begin = concat(\n      beginShebang,\n      /.*\\b/,\n      opts.binary,\n      /\\b.*/);\n  }\n  return inherit$1({\n    scope: 'meta',\n    begin: beginShebang,\n    end: /$/,\n    relevance: 0,\n    /** @type {ModeCallback} */\n    \"on:begin\": (m, resp) => {\n      if (m.index !== 0) resp.ignoreMatch();\n    }\n  }, opts);\n};\n\n// Common modes\nconst BACKSLASH_ESCAPE = {\n  begin: '\\\\\\\\[\\\\s\\\\S]', relevance: 0\n};\nconst APOS_STRING_MODE = {\n  scope: 'string',\n  begin: '\\'',\n  end: '\\'',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst QUOTE_STRING_MODE = {\n  scope: 'string',\n  begin: '\"',\n  end: '\"',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst PHRASAL_WORDS_MODE = {\n  begin: /\\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\\b/\n};\n/**\n * Creates a comment mode\n *\n * @param {string | RegExp} begin\n * @param {string | RegExp} end\n * @param {Mode | {}} [modeOptions]\n * @returns {Partial<Mode>}\n */\nconst COMMENT = function(begin, end, modeOptions = {}) {\n  const mode = inherit$1(\n    {\n      scope: 'comment',\n      begin,\n      end,\n      contains: []\n    },\n    modeOptions\n  );\n  mode.contains.push({\n    scope: 'doctag',\n    // hack to avoid the space from being included. the space is necessary to\n    // match here to prevent the plain text rule below from gobbling up doctags\n    begin: '[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)',\n    end: /(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,\n    excludeBegin: true,\n    relevance: 0\n  });\n  const ENGLISH_WORD = either(\n    // list of common 1 and 2 letter words in English\n    \"I\",\n    \"a\",\n    \"is\",\n    \"so\",\n    \"us\",\n    \"to\",\n    \"at\",\n    \"if\",\n    \"in\",\n    \"it\",\n    \"on\",\n    // note: this is not an exhaustive list of contractions, just popular ones\n    /[A-Za-z]+['](d|ve|re|ll|t|s|n)/, // contractions - can't we'd they're let's, etc\n    /[A-Za-z]+[-][a-z]+/, // `no-way`, etc.\n    /[A-Za-z][a-z]{2,}/ // allow capitalized words at beginning of sentences\n  );\n  // looking like plain text, more likely to be a comment\n  mode.contains.push(\n    {\n      // TODO: how to include \", (, ) without breaking grammars that use these for\n      // comment delimiters?\n      // begin: /[ ]+([()\"]?([A-Za-z'-]{3,}|is|a|I|so|us|[tT][oO]|at|if|in|it|on)[.]?[()\":]?([.][ ]|[ ]|\\))){3}/\n      // ---\n\n      // this tries to find sequences of 3 english words in a row (without any\n      // \"programming\" type syntax) this gives us a strong signal that we've\n      // TRULY found a comment - vs perhaps scanning with the wrong language.\n      // It's possible to find something that LOOKS like the start of the\n      // comment - but then if there is no readable text - good chance it is a\n      // false match and not a comment.\n      //\n      // for a visual example please see:\n      // https://github.com/highlightjs/highlight.js/issues/2827\n\n      begin: concat(\n        /[ ]+/, // necessary to prevent us gobbling up doctags like /* <AUTHOR> Mcgill */\n        '(',\n        ENGLISH_WORD,\n        /[.]?[:]?([.][ ]|[ ])/,\n        '){3}') // look for 3 words in a row\n    }\n  );\n  return mode;\n};\nconst C_LINE_COMMENT_MODE = COMMENT('//', '$');\nconst C_BLOCK_COMMENT_MODE = COMMENT('/\\\\*', '\\\\*/');\nconst HASH_COMMENT_MODE = COMMENT('#', '$');\nconst NUMBER_MODE = {\n  scope: 'number',\n  begin: NUMBER_RE,\n  relevance: 0\n};\nconst C_NUMBER_MODE = {\n  scope: 'number',\n  begin: C_NUMBER_RE,\n  relevance: 0\n};\nconst BINARY_NUMBER_MODE = {\n  scope: 'number',\n  begin: BINARY_NUMBER_RE,\n  relevance: 0\n};\nconst REGEXP_MODE = {\n  scope: \"regexp\",\n  begin: /\\/(?=[^/\\n]*\\/)/,\n  end: /\\/[gimuy]*/,\n  contains: [\n    BACKSLASH_ESCAPE,\n    {\n      begin: /\\[/,\n      end: /\\]/,\n      relevance: 0,\n      contains: [BACKSLASH_ESCAPE]\n    }\n  ]\n};\nconst TITLE_MODE = {\n  scope: 'title',\n  begin: IDENT_RE,\n  relevance: 0\n};\nconst UNDERSCORE_TITLE_MODE = {\n  scope: 'title',\n  begin: UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\nconst METHOD_GUARD = {\n  // excludes method names from keyword processing\n  begin: '\\\\.\\\\s*' + UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\n\n/**\n * Adds end same as begin mechanics to a mode\n *\n * Your mode must include at least a single () match group as that first match\n * group is what is used for comparison\n * @param {Partial<Mode>} mode\n */\nconst END_SAME_AS_BEGIN = function(mode) {\n  return Object.assign(mode,\n    {\n      /** @type {ModeCallback} */\n      'on:begin': (m, resp) => { resp.data._beginMatch = m[1]; },\n      /** @type {ModeCallback} */\n      'on:end': (m, resp) => { if (resp.data._beginMatch !== m[1]) resp.ignoreMatch(); }\n    });\n};\n\nvar MODES = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  APOS_STRING_MODE: APOS_STRING_MODE,\n  BACKSLASH_ESCAPE: BACKSLASH_ESCAPE,\n  BINARY_NUMBER_MODE: BINARY_NUMBER_MODE,\n  BINARY_NUMBER_RE: BINARY_NUMBER_RE,\n  COMMENT: COMMENT,\n  C_BLOCK_COMMENT_MODE: C_BLOCK_COMMENT_MODE,\n  C_LINE_COMMENT_MODE: C_LINE_COMMENT_MODE,\n  C_NUMBER_MODE: C_NUMBER_MODE,\n  C_NUMBER_RE: C_NUMBER_RE,\n  END_SAME_AS_BEGIN: END_SAME_AS_BEGIN,\n  HASH_COMMENT_MODE: HASH_COMMENT_MODE,\n  IDENT_RE: IDENT_RE,\n  MATCH_NOTHING_RE: MATCH_NOTHING_RE,\n  METHOD_GUARD: METHOD_GUARD,\n  NUMBER_MODE: NUMBER_MODE,\n  NUMBER_RE: NUMBER_RE,\n  PHRASAL_WORDS_MODE: PHRASAL_WORDS_MODE,\n  QUOTE_STRING_MODE: QUOTE_STRING_MODE,\n  REGEXP_MODE: REGEXP_MODE,\n  RE_STARTERS_RE: RE_STARTERS_RE,\n  SHEBANG: SHEBANG,\n  TITLE_MODE: TITLE_MODE,\n  UNDERSCORE_IDENT_RE: UNDERSCORE_IDENT_RE,\n  UNDERSCORE_TITLE_MODE: UNDERSCORE_TITLE_MODE\n});\n\n/**\n@typedef {import('highlight.js').CallbackResponse} CallbackResponse\n@typedef {import('highlight.js').CompilerExt} CompilerExt\n*/\n\n// Grammar extensions / plugins\n// See: https://github.com/highlightjs/highlight.js/issues/2833\n\n// Grammar extensions allow \"syntactic sugar\" to be added to the grammar modes\n// without requiring any underlying changes to the compiler internals.\n\n// `compileMatch` being the perfect small example of now allowing a grammar\n// author to write `match` when they desire to match a single expression rather\n// than being forced to use `begin`.  The extension then just moves `match` into\n// `begin` when it runs.  Ie, no features have been added, but we've just made\n// the experience of writing (and reading grammars) a little bit nicer.\n\n// ------\n\n// TODO: We need negative look-behind support to do this properly\n/**\n * Skip a match if it has a preceding dot\n *\n * This is used for `beginKeywords` to prevent matching expressions such as\n * `bob.keyword.do()`. The mode compiler automatically wires this up as a\n * special _internal_ 'on:begin' callback for modes with `beginKeywords`\n * @param {RegExpMatchArray} match\n * @param {CallbackResponse} response\n */\nfunction skipIfHasPrecedingDot(match, response) {\n  const before = match.input[match.index - 1];\n  if (before === \".\") {\n    response.ignoreMatch();\n  }\n}\n\n/**\n *\n * @type {CompilerExt}\n */\nfunction scopeClassName(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.className !== undefined) {\n    mode.scope = mode.className;\n    delete mode.className;\n  }\n}\n\n/**\n * `beginKeywords` syntactic sugar\n * @type {CompilerExt}\n */\nfunction beginKeywords(mode, parent) {\n  if (!parent) return;\n  if (!mode.beginKeywords) return;\n\n  // for languages with keywords that include non-word characters checking for\n  // a word boundary is not sufficient, so instead we check for a word boundary\n  // or whitespace - this does no harm in any case since our keyword engine\n  // doesn't allow spaces in keywords anyways and we still check for the boundary\n  // first\n  mode.begin = '\\\\b(' + mode.beginKeywords.split(' ').join('|') + ')(?!\\\\.)(?=\\\\b|\\\\s)';\n  mode.__beforeBegin = skipIfHasPrecedingDot;\n  mode.keywords = mode.keywords || mode.beginKeywords;\n  delete mode.beginKeywords;\n\n  // prevents double relevance, the keywords themselves provide\n  // relevance, the mode doesn't need to double it\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 0;\n}\n\n/**\n * Allow `illegal` to contain an array of illegal values\n * @type {CompilerExt}\n */\nfunction compileIllegal(mode, _parent) {\n  if (!Array.isArray(mode.illegal)) return;\n\n  mode.illegal = either(...mode.illegal);\n}\n\n/**\n * `match` to match a single expression for readability\n * @type {CompilerExt}\n */\nfunction compileMatch(mode, _parent) {\n  if (!mode.match) return;\n  if (mode.begin || mode.end) throw new Error(\"begin & end are not supported with match\");\n\n  mode.begin = mode.match;\n  delete mode.match;\n}\n\n/**\n * provides the default 1 relevance to all modes\n * @type {CompilerExt}\n */\nfunction compileRelevance(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 1;\n}\n\n// allow beforeMatch to act as a \"qualifier\" for the match\n// the full match begin must be [beforeMatch][begin]\nconst beforeMatchExt = (mode, parent) => {\n  if (!mode.beforeMatch) return;\n  // starts conflicts with endsParent which we need to make sure the child\n  // rule is not matched multiple times\n  if (mode.starts) throw new Error(\"beforeMatch cannot be used with starts\");\n\n  const originalMode = Object.assign({}, mode);\n  Object.keys(mode).forEach((key) => { delete mode[key]; });\n\n  mode.keywords = originalMode.keywords;\n  mode.begin = concat(originalMode.beforeMatch, lookahead(originalMode.begin));\n  mode.starts = {\n    relevance: 0,\n    contains: [\n      Object.assign(originalMode, { endsParent: true })\n    ]\n  };\n  mode.relevance = 0;\n\n  delete originalMode.beforeMatch;\n};\n\n// keywords that should have no default relevance value\nconst COMMON_KEYWORDS = [\n  'of',\n  'and',\n  'for',\n  'in',\n  'not',\n  'or',\n  'if',\n  'then',\n  'parent', // common variable name\n  'list', // common variable name\n  'value' // common variable name\n];\n\nconst DEFAULT_KEYWORD_SCOPE = \"keyword\";\n\n/**\n * Given raw keywords from a language definition, compile them.\n *\n * @param {string | Record<string,string|string[]> | Array<string>} rawKeywords\n * @param {boolean} caseInsensitive\n */\nfunction compileKeywords(rawKeywords, caseInsensitive, scopeName = DEFAULT_KEYWORD_SCOPE) {\n  /** @type {import(\"highlight.js/private\").KeywordDict} */\n  const compiledKeywords = Object.create(null);\n\n  // input can be a string of keywords, an array of keywords, or a object with\n  // named keys representing scopeName (which can then point to a string or array)\n  if (typeof rawKeywords === 'string') {\n    compileList(scopeName, rawKeywords.split(\" \"));\n  } else if (Array.isArray(rawKeywords)) {\n    compileList(scopeName, rawKeywords);\n  } else {\n    Object.keys(rawKeywords).forEach(function(scopeName) {\n      // collapse all our objects back into the parent object\n      Object.assign(\n        compiledKeywords,\n        compileKeywords(rawKeywords[scopeName], caseInsensitive, scopeName)\n      );\n    });\n  }\n  return compiledKeywords;\n\n  // ---\n\n  /**\n   * Compiles an individual list of keywords\n   *\n   * Ex: \"for if when while|5\"\n   *\n   * @param {string} scopeName\n   * @param {Array<string>} keywordList\n   */\n  function compileList(scopeName, keywordList) {\n    if (caseInsensitive) {\n      keywordList = keywordList.map(x => x.toLowerCase());\n    }\n    keywordList.forEach(function(keyword) {\n      const pair = keyword.split('|');\n      compiledKeywords[pair[0]] = [scopeName, scoreForKeyword(pair[0], pair[1])];\n    });\n  }\n}\n\n/**\n * Returns the proper score for a given keyword\n *\n * Also takes into account comment keywords, which will be scored 0 UNLESS\n * another score has been manually assigned.\n * @param {string} keyword\n * @param {string} [providedScore]\n */\nfunction scoreForKeyword(keyword, providedScore) {\n  // manual scores always win over common keywords\n  // so you can force a score of 1 if you really insist\n  if (providedScore) {\n    return Number(providedScore);\n  }\n\n  return commonKeyword(keyword) ? 0 : 1;\n}\n\n/**\n * Determines if a given keyword is common or not\n *\n * @param {string} keyword */\nfunction commonKeyword(keyword) {\n  return COMMON_KEYWORDS.includes(keyword.toLowerCase());\n}\n\n/*\n\nFor the reasoning behind this please see:\nhttps://github.com/highlightjs/highlight.js/issues/2880#issuecomment-*********\n\n*/\n\n/**\n * @type {Record<string, boolean>}\n */\nconst seenDeprecations = {};\n\n/**\n * @param {string} message\n */\nconst error = (message) => {\n  console.error(message);\n};\n\n/**\n * @param {string} message\n * @param {any} args\n */\nconst warn = (message, ...args) => {\n  console.log(`WARN: ${message}`, ...args);\n};\n\n/**\n * @param {string} version\n * @param {string} message\n */\nconst deprecated = (version, message) => {\n  if (seenDeprecations[`${version}/${message}`]) return;\n\n  console.log(`Deprecated as of ${version}. ${message}`);\n  seenDeprecations[`${version}/${message}`] = true;\n};\n\n/* eslint-disable no-throw-literal */\n\n/**\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n*/\n\nconst MultiClassError = new Error();\n\n/**\n * Renumbers labeled scope names to account for additional inner match\n * groups that otherwise would break everything.\n *\n * Lets say we 3 match scopes:\n *\n *   { 1 => ..., 2 => ..., 3 => ... }\n *\n * So what we need is a clean match like this:\n *\n *   (a)(b)(c) => [ \"a\", \"b\", \"c\" ]\n *\n * But this falls apart with inner match groups:\n *\n * (a)(((b)))(c) => [\"a\", \"b\", \"b\", \"b\", \"c\" ]\n *\n * Our scopes are now \"out of alignment\" and we're repeating `b` 3 times.\n * What needs to happen is the numbers are remapped:\n *\n *   { 1 => ..., 2 => ..., 5 => ... }\n *\n * We also need to know that the ONLY groups that should be output\n * are 1, 2, and 5.  This function handles this behavior.\n *\n * @param {CompiledMode} mode\n * @param {Array<RegExp | string>} regexes\n * @param {{key: \"beginScope\"|\"endScope\"}} opts\n */\nfunction remapScopeNames(mode, regexes, { key }) {\n  let offset = 0;\n  const scopeNames = mode[key];\n  /** @type Record<number,boolean> */\n  const emit = {};\n  /** @type Record<number,string> */\n  const positions = {};\n\n  for (let i = 1; i <= regexes.length; i++) {\n    positions[i + offset] = scopeNames[i];\n    emit[i + offset] = true;\n    offset += countMatchGroups(regexes[i - 1]);\n  }\n  // we use _emit to keep track of which match groups are \"top-level\" to avoid double\n  // output from inside match groups\n  mode[key] = positions;\n  mode[key]._emit = emit;\n  mode[key]._multi = true;\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction beginMultiClass(mode) {\n  if (!Array.isArray(mode.begin)) return;\n\n  if (mode.skip || mode.excludeBegin || mode.returnBegin) {\n    error(\"skip, excludeBegin, returnBegin not compatible with beginScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.beginScope !== \"object\" || mode.beginScope === null) {\n    error(\"beginScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.begin, { key: \"beginScope\" });\n  mode.begin = _rewriteBackreferences(mode.begin, { joinWith: \"\" });\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction endMultiClass(mode) {\n  if (!Array.isArray(mode.end)) return;\n\n  if (mode.skip || mode.excludeEnd || mode.returnEnd) {\n    error(\"skip, excludeEnd, returnEnd not compatible with endScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.endScope !== \"object\" || mode.endScope === null) {\n    error(\"endScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.end, { key: \"endScope\" });\n  mode.end = _rewriteBackreferences(mode.end, { joinWith: \"\" });\n}\n\n/**\n * this exists only to allow `scope: {}` to be used beside `match:`\n * Otherwise `beginScope` would necessary and that would look weird\n\n  {\n    match: [ /def/, /\\w+/ ]\n    scope: { 1: \"keyword\" , 2: \"title\" }\n  }\n\n * @param {CompiledMode} mode\n */\nfunction scopeSugar(mode) {\n  if (mode.scope && typeof mode.scope === \"object\" && mode.scope !== null) {\n    mode.beginScope = mode.scope;\n    delete mode.scope;\n  }\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction MultiClass(mode) {\n  scopeSugar(mode);\n\n  if (typeof mode.beginScope === \"string\") {\n    mode.beginScope = { _wrap: mode.beginScope };\n  }\n  if (typeof mode.endScope === \"string\") {\n    mode.endScope = { _wrap: mode.endScope };\n  }\n\n  beginMultiClass(mode);\n  endMultiClass(mode);\n}\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').CompiledLanguage} CompiledLanguage\n*/\n\n// compilation\n\n/**\n * Compiles a language definition result\n *\n * Given the raw result of a language definition (Language), compiles this so\n * that it is ready for highlighting code.\n * @param {Language} language\n * @returns {CompiledLanguage}\n */\nfunction compileLanguage(language) {\n  /**\n   * Builds a regex with the case sensitivity of the current language\n   *\n   * @param {RegExp | string} value\n   * @param {boolean} [global]\n   */\n  function langRe(value, global) {\n    return new RegExp(\n      source(value),\n      'm'\n      + (language.case_insensitive ? 'i' : '')\n      + (language.unicodeRegex ? 'u' : '')\n      + (global ? 'g' : '')\n    );\n  }\n\n  /**\n    Stores multiple regular expressions and allows you to quickly search for\n    them all in a string simultaneously - returning the first match.  It does\n    this by creating a huge (a|b|c) regex - each individual item wrapped with ()\n    and joined by `|` - using match groups to track position.  When a match is\n    found checking which position in the array has content allows us to figure\n    out which of the original regexes / match groups triggered the match.\n\n    The match object itself (the result of `Regex.exec`) is returned but also\n    enhanced by merging in any meta-data that was registered with the regex.\n    This is how we keep track of which mode matched, and what type of rule\n    (`illegal`, `begin`, end, etc).\n  */\n  class MultiRegex {\n    constructor() {\n      this.matchIndexes = {};\n      // @ts-ignore\n      this.regexes = [];\n      this.matchAt = 1;\n      this.position = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      opts.position = this.position++;\n      // @ts-ignore\n      this.matchIndexes[this.matchAt] = opts;\n      this.regexes.push([opts, re]);\n      this.matchAt += countMatchGroups(re) + 1;\n    }\n\n    compile() {\n      if (this.regexes.length === 0) {\n        // avoids the need to check length every time exec is called\n        // @ts-ignore\n        this.exec = () => null;\n      }\n      const terminators = this.regexes.map(el => el[1]);\n      this.matcherRe = langRe(_rewriteBackreferences(terminators, { joinWith: '|' }), true);\n      this.lastIndex = 0;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      this.matcherRe.lastIndex = this.lastIndex;\n      const match = this.matcherRe.exec(s);\n      if (!match) { return null; }\n\n      // eslint-disable-next-line no-undefined\n      const i = match.findIndex((el, i) => i > 0 && el !== undefined);\n      // @ts-ignore\n      const matchData = this.matchIndexes[i];\n      // trim off any earlier non-relevant match groups (ie, the other regex\n      // match groups that make up the multi-matcher)\n      match.splice(0, i);\n\n      return Object.assign(match, matchData);\n    }\n  }\n\n  /*\n    Created to solve the key deficiently with MultiRegex - there is no way to\n    test for multiple matches at a single location.  Why would we need to do\n    that?  In the future a more dynamic engine will allow certain matches to be\n    ignored.  An example: if we matched say the 3rd regex in a large group but\n    decided to ignore it - we'd need to started testing again at the 4th\n    regex... but MultiRegex itself gives us no real way to do that.\n\n    So what this class creates MultiRegexs on the fly for whatever search\n    position they are needed.\n\n    NOTE: These additional MultiRegex objects are created dynamically.  For most\n    grammars most of the time we will never actually need anything more than the\n    first MultiRegex - so this shouldn't have too much overhead.\n\n    Say this is our search group, and we match regex3, but wish to ignore it.\n\n      regex1 | regex2 | regex3 | regex4 | regex5    ' ie, startAt = 0\n\n    What we need is a new MultiRegex that only includes the remaining\n    possibilities:\n\n      regex4 | regex5                               ' ie, startAt = 3\n\n    This class wraps all that complexity up in a simple API... `startAt` decides\n    where in the array of expressions to start doing the matching. It\n    auto-increments, so if a match is found at position 2, then startAt will be\n    set to 3.  If the end is reached startAt will return to 0.\n\n    MOST of the time the parser will be setting startAt manually to 0.\n  */\n  class ResumableMultiRegex {\n    constructor() {\n      // @ts-ignore\n      this.rules = [];\n      // @ts-ignore\n      this.multiRegexes = [];\n      this.count = 0;\n\n      this.lastIndex = 0;\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    getMatcher(index) {\n      if (this.multiRegexes[index]) return this.multiRegexes[index];\n\n      const matcher = new MultiRegex();\n      this.rules.slice(index).forEach(([re, opts]) => matcher.addRule(re, opts));\n      matcher.compile();\n      this.multiRegexes[index] = matcher;\n      return matcher;\n    }\n\n    resumingScanAtSamePosition() {\n      return this.regexIndex !== 0;\n    }\n\n    considerAll() {\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      this.rules.push([re, opts]);\n      if (opts.type === \"begin\") this.count++;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      const m = this.getMatcher(this.regexIndex);\n      m.lastIndex = this.lastIndex;\n      let result = m.exec(s);\n\n      // The following is because we have no easy way to say \"resume scanning at the\n      // existing position but also skip the current rule ONLY\". What happens is\n      // all prior rules are also skipped which can result in matching the wrong\n      // thing. Example of matching \"booger\":\n\n      // our matcher is [string, \"booger\", number]\n      //\n      // ....booger....\n\n      // if \"booger\" is ignored then we'd really need a regex to scan from the\n      // SAME position for only: [string, number] but ignoring \"booger\" (if it\n      // was the first match), a simple resume would scan ahead who knows how\n      // far looking only for \"number\", ignoring potential string matches (or\n      // future \"booger\" matches that might be valid.)\n\n      // So what we do: We execute two matchers, one resuming at the same\n      // position, but the second full matcher starting at the position after:\n\n      //     /--- resume first regex match here (for [number])\n      //     |/---- full match here for [string, \"booger\", number]\n      //     vv\n      // ....booger....\n\n      // Which ever results in a match first is then used. So this 3-4 step\n      // process essentially allows us to say \"match at this position, excluding\n      // a prior rule that was ignored\".\n      //\n      // 1. Match \"booger\" first, ignore. Also proves that [string] does non match.\n      // 2. Resume matching for [number]\n      // 3. Match at index + 1 for [string, \"booger\", number]\n      // 4. If #2 and #3 result in matches, which came first?\n      if (this.resumingScanAtSamePosition()) {\n        if (result && result.index === this.lastIndex) ; else { // use the second matcher result\n          const m2 = this.getMatcher(0);\n          m2.lastIndex = this.lastIndex + 1;\n          result = m2.exec(s);\n        }\n      }\n\n      if (result) {\n        this.regexIndex += result.position + 1;\n        if (this.regexIndex === this.count) {\n          // wrap-around to considering all matches again\n          this.considerAll();\n        }\n      }\n\n      return result;\n    }\n  }\n\n  /**\n   * Given a mode, builds a huge ResumableMultiRegex that can be used to walk\n   * the content and find matches.\n   *\n   * @param {CompiledMode} mode\n   * @returns {ResumableMultiRegex}\n   */\n  function buildModeRegex(mode) {\n    const mm = new ResumableMultiRegex();\n\n    mode.contains.forEach(term => mm.addRule(term.begin, { rule: term, type: \"begin\" }));\n\n    if (mode.terminatorEnd) {\n      mm.addRule(mode.terminatorEnd, { type: \"end\" });\n    }\n    if (mode.illegal) {\n      mm.addRule(mode.illegal, { type: \"illegal\" });\n    }\n\n    return mm;\n  }\n\n  /** skip vs abort vs ignore\n   *\n   * @skip   - The mode is still entered and exited normally (and contains rules apply),\n   *           but all content is held and added to the parent buffer rather than being\n   *           output when the mode ends.  Mostly used with `sublanguage` to build up\n   *           a single large buffer than can be parsed by sublanguage.\n   *\n   *             - The mode begin ands ends normally.\n   *             - Content matched is added to the parent mode buffer.\n   *             - The parser cursor is moved forward normally.\n   *\n   * @abort  - A hack placeholder until we have ignore.  Aborts the mode (as if it\n   *           never matched) but DOES NOT continue to match subsequent `contains`\n   *           modes.  Abort is bad/suboptimal because it can result in modes\n   *           farther down not getting applied because an earlier rule eats the\n   *           content but then aborts.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is added to the mode buffer.\n   *             - The parser cursor is moved forward accordingly.\n   *\n   * @ignore - Ignores the mode (as if it never matched) and continues to match any\n   *           subsequent `contains` modes.  Ignore isn't technically possible with\n   *           the current parser implementation.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is ignored.\n   *             - The parser cursor is not moved forward.\n   */\n\n  /**\n   * Compiles an individual mode\n   *\n   * This can raise an error if the mode contains certain detectable known logic\n   * issues.\n   * @param {Mode} mode\n   * @param {CompiledMode | null} [parent]\n   * @returns {CompiledMode | never}\n   */\n  function compileMode(mode, parent) {\n    const cmode = /** @type CompiledMode */ (mode);\n    if (mode.isCompiled) return cmode;\n\n    [\n      scopeClassName,\n      // do this early so compiler extensions generally don't have to worry about\n      // the distinction between match/begin\n      compileMatch,\n      MultiClass,\n      beforeMatchExt\n    ].forEach(ext => ext(mode, parent));\n\n    language.compilerExtensions.forEach(ext => ext(mode, parent));\n\n    // __beforeBegin is considered private API, internal use only\n    mode.__beforeBegin = null;\n\n    [\n      beginKeywords,\n      // do this later so compiler extensions that come earlier have access to the\n      // raw array if they wanted to perhaps manipulate it, etc.\n      compileIllegal,\n      // default to 1 relevance if not specified\n      compileRelevance\n    ].forEach(ext => ext(mode, parent));\n\n    mode.isCompiled = true;\n\n    let keywordPattern = null;\n    if (typeof mode.keywords === \"object\" && mode.keywords.$pattern) {\n      // we need a copy because keywords might be compiled multiple times\n      // so we can't go deleting $pattern from the original on the first\n      // pass\n      mode.keywords = Object.assign({}, mode.keywords);\n      keywordPattern = mode.keywords.$pattern;\n      delete mode.keywords.$pattern;\n    }\n    keywordPattern = keywordPattern || /\\w+/;\n\n    if (mode.keywords) {\n      mode.keywords = compileKeywords(mode.keywords, language.case_insensitive);\n    }\n\n    cmode.keywordPatternRe = langRe(keywordPattern, true);\n\n    if (parent) {\n      if (!mode.begin) mode.begin = /\\B|\\b/;\n      cmode.beginRe = langRe(cmode.begin);\n      if (!mode.end && !mode.endsWithParent) mode.end = /\\B|\\b/;\n      if (mode.end) cmode.endRe = langRe(cmode.end);\n      cmode.terminatorEnd = source(cmode.end) || '';\n      if (mode.endsWithParent && parent.terminatorEnd) {\n        cmode.terminatorEnd += (mode.end ? '|' : '') + parent.terminatorEnd;\n      }\n    }\n    if (mode.illegal) cmode.illegalRe = langRe(/** @type {RegExp | string} */ (mode.illegal));\n    if (!mode.contains) mode.contains = [];\n\n    mode.contains = [].concat(...mode.contains.map(function(c) {\n      return expandOrCloneMode(c === 'self' ? mode : c);\n    }));\n    mode.contains.forEach(function(c) { compileMode(/** @type Mode */ (c), cmode); });\n\n    if (mode.starts) {\n      compileMode(mode.starts, parent);\n    }\n\n    cmode.matcher = buildModeRegex(cmode);\n    return cmode;\n  }\n\n  if (!language.compilerExtensions) language.compilerExtensions = [];\n\n  // self is not valid at the top-level\n  if (language.contains && language.contains.includes('self')) {\n    throw new Error(\"ERR: contains `self` is not supported at the top-level of a language.  See documentation.\");\n  }\n\n  // we need a null object, which inherit will guarantee\n  language.classNameAliases = inherit$1(language.classNameAliases || {});\n\n  return compileMode(/** @type Mode */ (language));\n}\n\n/**\n * Determines if a mode has a dependency on it's parent or not\n *\n * If a mode does have a parent dependency then often we need to clone it if\n * it's used in multiple places so that each copy points to the correct parent,\n * where-as modes without a parent can often safely be re-used at the bottom of\n * a mode chain.\n *\n * @param {Mode | null} mode\n * @returns {boolean} - is there a dependency on the parent?\n * */\nfunction dependencyOnParent(mode) {\n  if (!mode) return false;\n\n  return mode.endsWithParent || dependencyOnParent(mode.starts);\n}\n\n/**\n * Expands a mode or clones it if necessary\n *\n * This is necessary for modes with parental dependenceis (see notes on\n * `dependencyOnParent`) and for nodes that have `variants` - which must then be\n * exploded into their own individual modes at compile time.\n *\n * @param {Mode} mode\n * @returns {Mode | Mode[]}\n * */\nfunction expandOrCloneMode(mode) {\n  if (mode.variants && !mode.cachedVariants) {\n    mode.cachedVariants = mode.variants.map(function(variant) {\n      return inherit$1(mode, { variants: null }, variant);\n    });\n  }\n\n  // EXPAND\n  // if we have variants then essentially \"replace\" the mode with the variants\n  // this happens in compileMode, where this function is called from\n  if (mode.cachedVariants) {\n    return mode.cachedVariants;\n  }\n\n  // CLONE\n  // if we have dependencies on parents then we need a unique\n  // instance of ourselves, so we can be reused with many\n  // different parents without issue\n  if (dependencyOnParent(mode)) {\n    return inherit$1(mode, { starts: mode.starts ? inherit$1(mode.starts) : null });\n  }\n\n  if (Object.isFrozen(mode)) {\n    return inherit$1(mode);\n  }\n\n  // no special dependency issues, just return ourselves\n  return mode;\n}\n\nvar version = \"11.9.0\";\n\nclass HTMLInjectionError extends Error {\n  constructor(reason, html) {\n    super(reason);\n    this.name = \"HTMLInjectionError\";\n    this.html = html;\n  }\n}\n\n/*\nSyntax highlighting with language autodetection.\nhttps://highlightjs.org/\n*/\n\n\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').CompiledScope} CompiledScope\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSApi} HLJSApi\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').PluginEvent} PluginEvent\n@typedef {import('highlight.js').HLJSOptions} HLJSOptions\n@typedef {import('highlight.js').LanguageFn} LanguageFn\n@typedef {import('highlight.js').HighlightedHTMLElement} HighlightedHTMLElement\n@typedef {import('highlight.js').BeforeHighlightContext} BeforeHighlightContext\n@typedef {import('highlight.js/private').MatchType} MatchType\n@typedef {import('highlight.js/private').KeywordData} KeywordData\n@typedef {import('highlight.js/private').EnhancedMatch} EnhancedMatch\n@typedef {import('highlight.js/private').AnnotatedError} AnnotatedError\n@typedef {import('highlight.js').AutoHighlightResult} AutoHighlightResult\n@typedef {import('highlight.js').HighlightOptions} HighlightOptions\n@typedef {import('highlight.js').HighlightResult} HighlightResult\n*/\n\n\nconst escape = escapeHTML;\nconst inherit = inherit$1;\nconst NO_MATCH = Symbol(\"nomatch\");\nconst MAX_KEYWORD_HITS = 7;\n\n/**\n * @param {any} hljs - object that is extended (legacy)\n * @returns {HLJSApi}\n */\nconst HLJS = function(hljs) {\n  // Global internal variables used within the highlight.js library.\n  /** @type {Record<string, Language>} */\n  const languages = Object.create(null);\n  /** @type {Record<string, string>} */\n  const aliases = Object.create(null);\n  /** @type {HLJSPlugin[]} */\n  const plugins = [];\n\n  // safe/production mode - swallows more errors, tries to keep running\n  // even if a single syntax or parse hits a fatal error\n  let SAFE_MODE = true;\n  const LANGUAGE_NOT_FOUND = \"Could not find the language '{}', did you forget to load/include a language module?\";\n  /** @type {Language} */\n  const PLAINTEXT_LANGUAGE = { disableAutodetect: true, name: 'Plain text', contains: [] };\n\n  // Global options used when within external APIs. This is modified when\n  // calling the `hljs.configure` function.\n  /** @type HLJSOptions */\n  let options = {\n    ignoreUnescapedHTML: false,\n    throwUnescapedHTML: false,\n    noHighlightRe: /^(no-?highlight)$/i,\n    languageDetectRe: /\\blang(?:uage)?-([\\w-]+)\\b/i,\n    classPrefix: 'hljs-',\n    cssSelector: 'pre code',\n    languages: null,\n    // beta configuration options, subject to change, welcome to discuss\n    // https://github.com/highlightjs/highlight.js/issues/1086\n    __emitter: TokenTreeEmitter\n  };\n\n  /* Utility functions */\n\n  /**\n   * Tests a language name to see if highlighting should be skipped\n   * @param {string} languageName\n   */\n  function shouldNotHighlight(languageName) {\n    return options.noHighlightRe.test(languageName);\n  }\n\n  /**\n   * @param {HighlightedHTMLElement} block - the HTML element to determine language for\n   */\n  function blockLanguage(block) {\n    let classes = block.className + ' ';\n\n    classes += block.parentNode ? block.parentNode.className : '';\n\n    // language-* takes precedence over non-prefixed class names.\n    const match = options.languageDetectRe.exec(classes);\n    if (match) {\n      const language = getLanguage(match[1]);\n      if (!language) {\n        warn(LANGUAGE_NOT_FOUND.replace(\"{}\", match[1]));\n        warn(\"Falling back to no-highlight mode for this block.\", block);\n      }\n      return language ? match[1] : 'no-highlight';\n    }\n\n    return classes\n      .split(/\\s+/)\n      .find((_class) => shouldNotHighlight(_class) || getLanguage(_class));\n  }\n\n  /**\n   * Core highlighting function.\n   *\n   * OLD API\n   * highlight(lang, code, ignoreIllegals, continuation)\n   *\n   * NEW API\n   * highlight(code, {lang, ignoreIllegals})\n   *\n   * @param {string} codeOrLanguageName - the language to use for highlighting\n   * @param {string | HighlightOptions} optionsOrCode - the code to highlight\n   * @param {boolean} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   *\n   * @returns {HighlightResult} Result - an object that represents the result\n   * @property {string} language - the language name\n   * @property {number} relevance - the relevance score\n   * @property {string} value - the highlighted HTML code\n   * @property {string} code - the original raw code\n   * @property {CompiledMode} top - top of the current mode stack\n   * @property {boolean} illegal - indicates whether any illegal matches were found\n  */\n  function highlight(codeOrLanguageName, optionsOrCode, ignoreIllegals) {\n    let code = \"\";\n    let languageName = \"\";\n    if (typeof optionsOrCode === \"object\") {\n      code = codeOrLanguageName;\n      ignoreIllegals = optionsOrCode.ignoreIllegals;\n      languageName = optionsOrCode.language;\n    } else {\n      // old API\n      deprecated(\"10.7.0\", \"highlight(lang, code, ...args) has been deprecated.\");\n      deprecated(\"10.7.0\", \"Please use highlight(code, options) instead.\\nhttps://github.com/highlightjs/highlight.js/issues/2277\");\n      languageName = codeOrLanguageName;\n      code = optionsOrCode;\n    }\n\n    // https://github.com/highlightjs/highlight.js/issues/3149\n    // eslint-disable-next-line no-undefined\n    if (ignoreIllegals === undefined) { ignoreIllegals = true; }\n\n    /** @type {BeforeHighlightContext} */\n    const context = {\n      code,\n      language: languageName\n    };\n    // the plugin can change the desired language or the code to be highlighted\n    // just be changing the object it was passed\n    fire(\"before:highlight\", context);\n\n    // a before plugin can usurp the result completely by providing it's own\n    // in which case we don't even need to call highlight\n    const result = context.result\n      ? context.result\n      : _highlight(context.language, context.code, ignoreIllegals);\n\n    result.code = context.code;\n    // the plugin can change anything in result to suite it\n    fire(\"after:highlight\", result);\n\n    return result;\n  }\n\n  /**\n   * private highlight that's used internally and does not fire callbacks\n   *\n   * @param {string} languageName - the language to use for highlighting\n   * @param {string} codeToHighlight - the code to highlight\n   * @param {boolean?} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode?} [continuation] - current continuation mode, if any\n   * @returns {HighlightResult} - result of the highlight operation\n  */\n  function _highlight(languageName, codeToHighlight, ignoreIllegals, continuation) {\n    const keywordHits = Object.create(null);\n\n    /**\n     * Return keyword data if a match is a keyword\n     * @param {CompiledMode} mode - current mode\n     * @param {string} matchText - the textual match\n     * @returns {KeywordData | false}\n     */\n    function keywordData(mode, matchText) {\n      return mode.keywords[matchText];\n    }\n\n    function processKeywords() {\n      if (!top.keywords) {\n        emitter.addText(modeBuffer);\n        return;\n      }\n\n      let lastIndex = 0;\n      top.keywordPatternRe.lastIndex = 0;\n      let match = top.keywordPatternRe.exec(modeBuffer);\n      let buf = \"\";\n\n      while (match) {\n        buf += modeBuffer.substring(lastIndex, match.index);\n        const word = language.case_insensitive ? match[0].toLowerCase() : match[0];\n        const data = keywordData(top, word);\n        if (data) {\n          const [kind, keywordRelevance] = data;\n          emitter.addText(buf);\n          buf = \"\";\n\n          keywordHits[word] = (keywordHits[word] || 0) + 1;\n          if (keywordHits[word] <= MAX_KEYWORD_HITS) relevance += keywordRelevance;\n          if (kind.startsWith(\"_\")) {\n            // _ implied for relevance only, do not highlight\n            // by applying a class name\n            buf += match[0];\n          } else {\n            const cssClass = language.classNameAliases[kind] || kind;\n            emitKeyword(match[0], cssClass);\n          }\n        } else {\n          buf += match[0];\n        }\n        lastIndex = top.keywordPatternRe.lastIndex;\n        match = top.keywordPatternRe.exec(modeBuffer);\n      }\n      buf += modeBuffer.substring(lastIndex);\n      emitter.addText(buf);\n    }\n\n    function processSubLanguage() {\n      if (modeBuffer === \"\") return;\n      /** @type HighlightResult */\n      let result = null;\n\n      if (typeof top.subLanguage === 'string') {\n        if (!languages[top.subLanguage]) {\n          emitter.addText(modeBuffer);\n          return;\n        }\n        result = _highlight(top.subLanguage, modeBuffer, true, continuations[top.subLanguage]);\n        continuations[top.subLanguage] = /** @type {CompiledMode} */ (result._top);\n      } else {\n        result = highlightAuto(modeBuffer, top.subLanguage.length ? top.subLanguage : null);\n      }\n\n      // Counting embedded language score towards the host language may be disabled\n      // with zeroing the containing mode relevance. Use case in point is Markdown that\n      // allows XML everywhere and makes every XML snippet to have a much larger Markdown\n      // score.\n      if (top.relevance > 0) {\n        relevance += result.relevance;\n      }\n      emitter.__addSublanguage(result._emitter, result.language);\n    }\n\n    function processBuffer() {\n      if (top.subLanguage != null) {\n        processSubLanguage();\n      } else {\n        processKeywords();\n      }\n      modeBuffer = '';\n    }\n\n    /**\n     * @param {string} text\n     * @param {string} scope\n     */\n    function emitKeyword(keyword, scope) {\n      if (keyword === \"\") return;\n\n      emitter.startScope(scope);\n      emitter.addText(keyword);\n      emitter.endScope();\n    }\n\n    /**\n     * @param {CompiledScope} scope\n     * @param {RegExpMatchArray} match\n     */\n    function emitMultiClass(scope, match) {\n      let i = 1;\n      const max = match.length - 1;\n      while (i <= max) {\n        if (!scope._emit[i]) { i++; continue; }\n        const klass = language.classNameAliases[scope[i]] || scope[i];\n        const text = match[i];\n        if (klass) {\n          emitKeyword(text, klass);\n        } else {\n          modeBuffer = text;\n          processKeywords();\n          modeBuffer = \"\";\n        }\n        i++;\n      }\n    }\n\n    /**\n     * @param {CompiledMode} mode - new mode to start\n     * @param {RegExpMatchArray} match\n     */\n    function startNewMode(mode, match) {\n      if (mode.scope && typeof mode.scope === \"string\") {\n        emitter.openNode(language.classNameAliases[mode.scope] || mode.scope);\n      }\n      if (mode.beginScope) {\n        // beginScope just wraps the begin match itself in a scope\n        if (mode.beginScope._wrap) {\n          emitKeyword(modeBuffer, language.classNameAliases[mode.beginScope._wrap] || mode.beginScope._wrap);\n          modeBuffer = \"\";\n        } else if (mode.beginScope._multi) {\n          // at this point modeBuffer should just be the match\n          emitMultiClass(mode.beginScope, match);\n          modeBuffer = \"\";\n        }\n      }\n\n      top = Object.create(mode, { parent: { value: top } });\n      return top;\n    }\n\n    /**\n     * @param {CompiledMode } mode - the mode to potentially end\n     * @param {RegExpMatchArray} match - the latest match\n     * @param {string} matchPlusRemainder - match plus remainder of content\n     * @returns {CompiledMode | void} - the next mode, or if void continue on in current mode\n     */\n    function endOfMode(mode, match, matchPlusRemainder) {\n      let matched = startsWith(mode.endRe, matchPlusRemainder);\n\n      if (matched) {\n        if (mode[\"on:end\"]) {\n          const resp = new Response(mode);\n          mode[\"on:end\"](match, resp);\n          if (resp.isMatchIgnored) matched = false;\n        }\n\n        if (matched) {\n          while (mode.endsParent && mode.parent) {\n            mode = mode.parent;\n          }\n          return mode;\n        }\n      }\n      // even if on:end fires an `ignore` it's still possible\n      // that we might trigger the end node because of a parent mode\n      if (mode.endsWithParent) {\n        return endOfMode(mode.parent, match, matchPlusRemainder);\n      }\n    }\n\n    /**\n     * Handle matching but then ignoring a sequence of text\n     *\n     * @param {string} lexeme - string containing full match text\n     */\n    function doIgnore(lexeme) {\n      if (top.matcher.regexIndex === 0) {\n        // no more regexes to potentially match here, so we move the cursor forward one\n        // space\n        modeBuffer += lexeme[0];\n        return 1;\n      } else {\n        // no need to move the cursor, we still have additional regexes to try and\n        // match at this very spot\n        resumeScanAtSamePosition = true;\n        return 0;\n      }\n    }\n\n    /**\n     * Handle the start of a new potential mode match\n     *\n     * @param {EnhancedMatch} match - the current match\n     * @returns {number} how far to advance the parse cursor\n     */\n    function doBeginMatch(match) {\n      const lexeme = match[0];\n      const newMode = match.rule;\n\n      const resp = new Response(newMode);\n      // first internal before callbacks, then the public ones\n      const beforeCallbacks = [newMode.__beforeBegin, newMode[\"on:begin\"]];\n      for (const cb of beforeCallbacks) {\n        if (!cb) continue;\n        cb(match, resp);\n        if (resp.isMatchIgnored) return doIgnore(lexeme);\n      }\n\n      if (newMode.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (newMode.excludeBegin) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (!newMode.returnBegin && !newMode.excludeBegin) {\n          modeBuffer = lexeme;\n        }\n      }\n      startNewMode(newMode, match);\n      return newMode.returnBegin ? 0 : lexeme.length;\n    }\n\n    /**\n     * Handle the potential end of mode\n     *\n     * @param {RegExpMatchArray} match - the current match\n     */\n    function doEndMatch(match) {\n      const lexeme = match[0];\n      const matchPlusRemainder = codeToHighlight.substring(match.index);\n\n      const endMode = endOfMode(top, match, matchPlusRemainder);\n      if (!endMode) { return NO_MATCH; }\n\n      const origin = top;\n      if (top.endScope && top.endScope._wrap) {\n        processBuffer();\n        emitKeyword(lexeme, top.endScope._wrap);\n      } else if (top.endScope && top.endScope._multi) {\n        processBuffer();\n        emitMultiClass(top.endScope, match);\n      } else if (origin.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (!(origin.returnEnd || origin.excludeEnd)) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (origin.excludeEnd) {\n          modeBuffer = lexeme;\n        }\n      }\n      do {\n        if (top.scope) {\n          emitter.closeNode();\n        }\n        if (!top.skip && !top.subLanguage) {\n          relevance += top.relevance;\n        }\n        top = top.parent;\n      } while (top !== endMode.parent);\n      if (endMode.starts) {\n        startNewMode(endMode.starts, match);\n      }\n      return origin.returnEnd ? 0 : lexeme.length;\n    }\n\n    function processContinuations() {\n      const list = [];\n      for (let current = top; current !== language; current = current.parent) {\n        if (current.scope) {\n          list.unshift(current.scope);\n        }\n      }\n      list.forEach(item => emitter.openNode(item));\n    }\n\n    /** @type {{type?: MatchType, index?: number, rule?: Mode}}} */\n    let lastMatch = {};\n\n    /**\n     *  Process an individual match\n     *\n     * @param {string} textBeforeMatch - text preceding the match (since the last match)\n     * @param {EnhancedMatch} [match] - the match itself\n     */\n    function processLexeme(textBeforeMatch, match) {\n      const lexeme = match && match[0];\n\n      // add non-matched text to the current mode buffer\n      modeBuffer += textBeforeMatch;\n\n      if (lexeme == null) {\n        processBuffer();\n        return 0;\n      }\n\n      // we've found a 0 width match and we're stuck, so we need to advance\n      // this happens when we have badly behaved rules that have optional matchers to the degree that\n      // sometimes they can end up matching nothing at all\n      // Ref: https://github.com/highlightjs/highlight.js/issues/2140\n      if (lastMatch.type === \"begin\" && match.type === \"end\" && lastMatch.index === match.index && lexeme === \"\") {\n        // spit the \"skipped\" character that our regex choked on back into the output sequence\n        modeBuffer += codeToHighlight.slice(match.index, match.index + 1);\n        if (!SAFE_MODE) {\n          /** @type {AnnotatedError} */\n          const err = new Error(`0 width match regex (${languageName})`);\n          err.languageName = languageName;\n          err.badRule = lastMatch.rule;\n          throw err;\n        }\n        return 1;\n      }\n      lastMatch = match;\n\n      if (match.type === \"begin\") {\n        return doBeginMatch(match);\n      } else if (match.type === \"illegal\" && !ignoreIllegals) {\n        // illegal match, we do not continue processing\n        /** @type {AnnotatedError} */\n        const err = new Error('Illegal lexeme \"' + lexeme + '\" for mode \"' + (top.scope || '<unnamed>') + '\"');\n        err.mode = top;\n        throw err;\n      } else if (match.type === \"end\") {\n        const processed = doEndMatch(match);\n        if (processed !== NO_MATCH) {\n          return processed;\n        }\n      }\n\n      // edge case for when illegal matches $ (end of line) which is technically\n      // a 0 width match but not a begin/end match so it's not caught by the\n      // first handler (when ignoreIllegals is true)\n      if (match.type === \"illegal\" && lexeme === \"\") {\n        // advance so we aren't stuck in an infinite loop\n        return 1;\n      }\n\n      // infinite loops are BAD, this is a last ditch catch all. if we have a\n      // decent number of iterations yet our index (cursor position in our\n      // parsing) still 3x behind our index then something is very wrong\n      // so we bail\n      if (iterations > 100000 && iterations > match.index * 3) {\n        const err = new Error('potential infinite loop, way more iterations than matches');\n        throw err;\n      }\n\n      /*\n      Why might be find ourselves here?  An potential end match that was\n      triggered but could not be completed.  IE, `doEndMatch` returned NO_MATCH.\n      (this could be because a callback requests the match be ignored, etc)\n\n      This causes no real harm other than stopping a few times too many.\n      */\n\n      modeBuffer += lexeme;\n      return lexeme.length;\n    }\n\n    const language = getLanguage(languageName);\n    if (!language) {\n      error(LANGUAGE_NOT_FOUND.replace(\"{}\", languageName));\n      throw new Error('Unknown language: \"' + languageName + '\"');\n    }\n\n    const md = compileLanguage(language);\n    let result = '';\n    /** @type {CompiledMode} */\n    let top = continuation || md;\n    /** @type Record<string,CompiledMode> */\n    const continuations = {}; // keep continuations for sub-languages\n    const emitter = new options.__emitter(options);\n    processContinuations();\n    let modeBuffer = '';\n    let relevance = 0;\n    let index = 0;\n    let iterations = 0;\n    let resumeScanAtSamePosition = false;\n\n    try {\n      if (!language.__emitTokens) {\n        top.matcher.considerAll();\n\n        for (;;) {\n          iterations++;\n          if (resumeScanAtSamePosition) {\n            // only regexes not matched previously will now be\n            // considered for a potential match\n            resumeScanAtSamePosition = false;\n          } else {\n            top.matcher.considerAll();\n          }\n          top.matcher.lastIndex = index;\n\n          const match = top.matcher.exec(codeToHighlight);\n          // console.log(\"match\", match[0], match.rule && match.rule.begin)\n\n          if (!match) break;\n\n          const beforeMatch = codeToHighlight.substring(index, match.index);\n          const processedCount = processLexeme(beforeMatch, match);\n          index = match.index + processedCount;\n        }\n        processLexeme(codeToHighlight.substring(index));\n      } else {\n        language.__emitTokens(codeToHighlight, emitter);\n      }\n\n      emitter.finalize();\n      result = emitter.toHTML();\n\n      return {\n        language: languageName,\n        value: result,\n        relevance,\n        illegal: false,\n        _emitter: emitter,\n        _top: top\n      };\n    } catch (err) {\n      if (err.message && err.message.includes('Illegal')) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: true,\n          relevance: 0,\n          _illegalBy: {\n            message: err.message,\n            index,\n            context: codeToHighlight.slice(index - 100, index + 100),\n            mode: err.mode,\n            resultSoFar: result\n          },\n          _emitter: emitter\n        };\n      } else if (SAFE_MODE) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: false,\n          relevance: 0,\n          errorRaised: err,\n          _emitter: emitter,\n          _top: top\n        };\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * returns a valid highlight result, without actually doing any actual work,\n   * auto highlight starts with this and it's possible for small snippets that\n   * auto-detection may not find a better match\n   * @param {string} code\n   * @returns {HighlightResult}\n   */\n  function justTextHighlightResult(code) {\n    const result = {\n      value: escape(code),\n      illegal: false,\n      relevance: 0,\n      _top: PLAINTEXT_LANGUAGE,\n      _emitter: new options.__emitter(options)\n    };\n    result._emitter.addText(code);\n    return result;\n  }\n\n  /**\n  Highlighting with language detection. Accepts a string with the code to\n  highlight. Returns an object with the following properties:\n\n  - language (detected language)\n  - relevance (int)\n  - value (an HTML string with highlighting markup)\n  - secondBest (object with the same structure for second-best heuristically\n    detected language, may be absent)\n\n    @param {string} code\n    @param {Array<string>} [languageSubset]\n    @returns {AutoHighlightResult}\n  */\n  function highlightAuto(code, languageSubset) {\n    languageSubset = languageSubset || options.languages || Object.keys(languages);\n    const plaintext = justTextHighlightResult(code);\n\n    const results = languageSubset.filter(getLanguage).filter(autoDetection).map(name =>\n      _highlight(name, code, false)\n    );\n    results.unshift(plaintext); // plaintext is always an option\n\n    const sorted = results.sort((a, b) => {\n      // sort base on relevance\n      if (a.relevance !== b.relevance) return b.relevance - a.relevance;\n\n      // always award the tie to the base language\n      // ie if C++ and Arduino are tied, it's more likely to be C++\n      if (a.language && b.language) {\n        if (getLanguage(a.language).supersetOf === b.language) {\n          return 1;\n        } else if (getLanguage(b.language).supersetOf === a.language) {\n          return -1;\n        }\n      }\n\n      // otherwise say they are equal, which has the effect of sorting on\n      // relevance while preserving the original ordering - which is how ties\n      // have historically been settled, ie the language that comes first always\n      // wins in the case of a tie\n      return 0;\n    });\n\n    const [best, secondBest] = sorted;\n\n    /** @type {AutoHighlightResult} */\n    const result = best;\n    result.secondBest = secondBest;\n\n    return result;\n  }\n\n  /**\n   * Builds new class name for block given the language name\n   *\n   * @param {HTMLElement} element\n   * @param {string} [currentLang]\n   * @param {string} [resultLang]\n   */\n  function updateClassName(element, currentLang, resultLang) {\n    const language = (currentLang && aliases[currentLang]) || resultLang;\n\n    element.classList.add(\"hljs\");\n    element.classList.add(`language-${language}`);\n  }\n\n  /**\n   * Applies highlighting to a DOM node containing code.\n   *\n   * @param {HighlightedHTMLElement} element - the HTML element to highlight\n  */\n  function highlightElement(element) {\n    /** @type HTMLElement */\n    let node = null;\n    const language = blockLanguage(element);\n\n    if (shouldNotHighlight(language)) return;\n\n    fire(\"before:highlightElement\",\n      { el: element, language });\n\n    if (element.dataset.highlighted) {\n      console.log(\"Element previously highlighted. To highlight again, first unset `dataset.highlighted`.\", element);\n      return;\n    }\n\n    // we should be all text, no child nodes (unescaped HTML) - this is possibly\n    // an HTML injection attack - it's likely too late if this is already in\n    // production (the code has likely already done its damage by the time\n    // we're seeing it)... but we yell loudly about this so that hopefully it's\n    // more likely to be caught in development before making it to production\n    if (element.children.length > 0) {\n      if (!options.ignoreUnescapedHTML) {\n        console.warn(\"One of your code blocks includes unescaped HTML. This is a potentially serious security risk.\");\n        console.warn(\"https://github.com/highlightjs/highlight.js/wiki/security\");\n        console.warn(\"The element with unescaped HTML:\");\n        console.warn(element);\n      }\n      if (options.throwUnescapedHTML) {\n        const err = new HTMLInjectionError(\n          \"One of your code blocks includes unescaped HTML.\",\n          element.innerHTML\n        );\n        throw err;\n      }\n    }\n\n    node = element;\n    const text = node.textContent;\n    const result = language ? highlight(text, { language, ignoreIllegals: true }) : highlightAuto(text);\n\n    element.innerHTML = result.value;\n    element.dataset.highlighted = \"yes\";\n    updateClassName(element, language, result.language);\n    element.result = {\n      language: result.language,\n      // TODO: remove with version 11.0\n      re: result.relevance,\n      relevance: result.relevance\n    };\n    if (result.secondBest) {\n      element.secondBest = {\n        language: result.secondBest.language,\n        relevance: result.secondBest.relevance\n      };\n    }\n\n    fire(\"after:highlightElement\", { el: element, result, text });\n  }\n\n  /**\n   * Updates highlight.js global options with the passed options\n   *\n   * @param {Partial<HLJSOptions>} userOptions\n   */\n  function configure(userOptions) {\n    options = inherit(options, userOptions);\n  }\n\n  // TODO: remove v12, deprecated\n  const initHighlighting = () => {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlighting() deprecated.  Use highlightAll() now.\");\n  };\n\n  // TODO: remove v12, deprecated\n  function initHighlightingOnLoad() {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlightingOnLoad() deprecated.  Use highlightAll() now.\");\n  }\n\n  let wantsHighlight = false;\n\n  /**\n   * auto-highlights all pre>code elements on the page\n   */\n  function highlightAll() {\n    // if we are called too early in the loading process\n    if (document.readyState === \"loading\") {\n      wantsHighlight = true;\n      return;\n    }\n\n    const blocks = document.querySelectorAll(options.cssSelector);\n    blocks.forEach(highlightElement);\n  }\n\n  function boot() {\n    // if a highlight was requested before DOM was loaded, do now\n    if (wantsHighlight) highlightAll();\n  }\n\n  // make sure we are in the browser environment\n  if (typeof window !== 'undefined' && window.addEventListener) {\n    window.addEventListener('DOMContentLoaded', boot, false);\n  }\n\n  /**\n   * Register a language grammar module\n   *\n   * @param {string} languageName\n   * @param {LanguageFn} languageDefinition\n   */\n  function registerLanguage(languageName, languageDefinition) {\n    let lang = null;\n    try {\n      lang = languageDefinition(hljs);\n    } catch (error$1) {\n      error(\"Language definition for '{}' could not be registered.\".replace(\"{}\", languageName));\n      // hard or soft error\n      if (!SAFE_MODE) { throw error$1; } else { error(error$1); }\n      // languages that have serious errors are replaced with essentially a\n      // \"plaintext\" stand-in so that the code blocks will still get normal\n      // css classes applied to them - and one bad language won't break the\n      // entire highlighter\n      lang = PLAINTEXT_LANGUAGE;\n    }\n    // give it a temporary name if it doesn't have one in the meta-data\n    if (!lang.name) lang.name = languageName;\n    languages[languageName] = lang;\n    lang.rawDefinition = languageDefinition.bind(null, hljs);\n\n    if (lang.aliases) {\n      registerAliases(lang.aliases, { languageName });\n    }\n  }\n\n  /**\n   * Remove a language grammar module\n   *\n   * @param {string} languageName\n   */\n  function unregisterLanguage(languageName) {\n    delete languages[languageName];\n    for (const alias of Object.keys(aliases)) {\n      if (aliases[alias] === languageName) {\n        delete aliases[alias];\n      }\n    }\n  }\n\n  /**\n   * @returns {string[]} List of language internal names\n   */\n  function listLanguages() {\n    return Object.keys(languages);\n  }\n\n  /**\n   * @param {string} name - name of the language to retrieve\n   * @returns {Language | undefined}\n   */\n  function getLanguage(name) {\n    name = (name || '').toLowerCase();\n    return languages[name] || languages[aliases[name]];\n  }\n\n  /**\n   *\n   * @param {string|string[]} aliasList - single alias or list of aliases\n   * @param {{languageName: string}} opts\n   */\n  function registerAliases(aliasList, { languageName }) {\n    if (typeof aliasList === 'string') {\n      aliasList = [aliasList];\n    }\n    aliasList.forEach(alias => { aliases[alias.toLowerCase()] = languageName; });\n  }\n\n  /**\n   * Determines if a given language has auto-detection enabled\n   * @param {string} name - name of the language\n   */\n  function autoDetection(name) {\n    const lang = getLanguage(name);\n    return lang && !lang.disableAutodetect;\n  }\n\n  /**\n   * Upgrades the old highlightBlock plugins to the new\n   * highlightElement API\n   * @param {HLJSPlugin} plugin\n   */\n  function upgradePluginAPI(plugin) {\n    // TODO: remove with v12\n    if (plugin[\"before:highlightBlock\"] && !plugin[\"before:highlightElement\"]) {\n      plugin[\"before:highlightElement\"] = (data) => {\n        plugin[\"before:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n    if (plugin[\"after:highlightBlock\"] && !plugin[\"after:highlightElement\"]) {\n      plugin[\"after:highlightElement\"] = (data) => {\n        plugin[\"after:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function addPlugin(plugin) {\n    upgradePluginAPI(plugin);\n    plugins.push(plugin);\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function removePlugin(plugin) {\n    const index = plugins.indexOf(plugin);\n    if (index !== -1) {\n      plugins.splice(index, 1);\n    }\n  }\n\n  /**\n   *\n   * @param {PluginEvent} event\n   * @param {any} args\n   */\n  function fire(event, args) {\n    const cb = event;\n    plugins.forEach(function(plugin) {\n      if (plugin[cb]) {\n        plugin[cb](args);\n      }\n    });\n  }\n\n  /**\n   * DEPRECATED\n   * @param {HighlightedHTMLElement} el\n   */\n  function deprecateHighlightBlock(el) {\n    deprecated(\"10.7.0\", \"highlightBlock will be removed entirely in v12.0\");\n    deprecated(\"10.7.0\", \"Please use highlightElement now.\");\n\n    return highlightElement(el);\n  }\n\n  /* Interface definition */\n  Object.assign(hljs, {\n    highlight,\n    highlightAuto,\n    highlightAll,\n    highlightElement,\n    // TODO: Remove with v12 API\n    highlightBlock: deprecateHighlightBlock,\n    configure,\n    initHighlighting,\n    initHighlightingOnLoad,\n    registerLanguage,\n    unregisterLanguage,\n    listLanguages,\n    getLanguage,\n    registerAliases,\n    autoDetection,\n    inherit,\n    addPlugin,\n    removePlugin\n  });\n\n  hljs.debugMode = function() { SAFE_MODE = false; };\n  hljs.safeMode = function() { SAFE_MODE = true; };\n  hljs.versionString = version;\n\n  hljs.regex = {\n    concat: concat,\n    lookahead: lookahead,\n    either: either,\n    optional: optional,\n    anyNumberOfTimes: anyNumberOfTimes\n  };\n\n  for (const key in MODES) {\n    // @ts-ignore\n    if (typeof MODES[key] === \"object\") {\n      // @ts-ignore\n      deepFreeze(MODES[key]);\n    }\n  }\n\n  // merge all the modes/regexes into our main object\n  Object.assign(hljs, MODES);\n\n  return hljs;\n};\n\n// Other names for the variable may break build script\nconst highlight = HLJS({});\n\n// returns a new instance of the highlighter to be used for extensions\n// check https://github.com/wooorm/lowlight/issues/47\nhighlight.newInstance = () => HLJS({});\n\nmodule.exports = highlight;\nhighlight.HighlightJS = highlight;\nhighlight.default = highlight;\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,aAAS,WAAW,KAAK;AACvB,UAAI,eAAe,KAAK;AACtB,YAAI,QACF,IAAI,SACJ,IAAI,MACF,WAAY;AACV,gBAAM,IAAI,MAAM,kBAAkB;AAAA,QACpC;AAAA,MACN,WAAW,eAAe,KAAK;AAC7B,YAAI,MACF,IAAI,QACJ,IAAI,SACF,WAAY;AACV,gBAAM,IAAI,MAAM,kBAAkB;AAAA,QACpC;AAAA,MACN;AAGA,aAAO,OAAO,GAAG;AAEjB,aAAO,oBAAoB,GAAG,EAAE,QAAQ,CAAC,SAAS;AAChD,cAAM,OAAO,IAAI,IAAI;AACrB,cAAM,OAAO,OAAO;AAGpB,aAAK,SAAS,YAAY,SAAS,eAAe,CAAC,OAAO,SAAS,IAAI,GAAG;AACxE,qBAAW,IAAI;AAAA,QACjB;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAMA,QAAM,WAAN,MAAe;AAAA;AAAA;AAAA;AAAA,MAIb,YAAY,MAAM;AAEhB,YAAI,KAAK,SAAS,OAAW,MAAK,OAAO,CAAC;AAE1C,aAAK,OAAO,KAAK;AACjB,aAAK,iBAAiB;AAAA,MACxB;AAAA,MAEA,cAAc;AACZ,aAAK,iBAAiB;AAAA,MACxB;AAAA,IACF;AAMA,aAAS,WAAW,OAAO;AACzB,aAAO,MACJ,QAAQ,MAAM,OAAO,EACrB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,QAAQ;AAAA,IAC3B;AAUA,aAAS,UAAU,aAAa,SAAS;AAEvC,YAAM,SAAS,uBAAO,OAAO,IAAI;AAEjC,iBAAW,OAAO,UAAU;AAC1B,eAAO,GAAG,IAAI,SAAS,GAAG;AAAA,MAC5B;AACA,cAAQ,QAAQ,SAAS,KAAK;AAC5B,mBAAW,OAAO,KAAK;AACrB,iBAAO,GAAG,IAAI,IAAI,GAAG;AAAA,QACvB;AAAA,MACF,CAAC;AACD;AAAA;AAAA,QAAyB;AAAA;AAAA,IAC3B;AAcA,QAAM,aAAa;AAMnB,QAAM,oBAAoB,CAAC,SAAS;AAGlC,aAAO,CAAC,CAAC,KAAK;AAAA,IAChB;AAOA,QAAM,kBAAkB,CAAC,MAAM,EAAE,OAAO,MAAM;AAE5C,UAAI,KAAK,WAAW,WAAW,GAAG;AAChC,eAAO,KAAK,QAAQ,aAAa,WAAW;AAAA,MAC9C;AAEA,UAAI,KAAK,SAAS,GAAG,GAAG;AACtB,cAAM,SAAS,KAAK,MAAM,GAAG;AAC7B,eAAO;AAAA,UACL,GAAG,MAAM,GAAG,OAAO,MAAM,CAAC;AAAA,UAC1B,GAAI,OAAO,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,IAAI,OAAO,IAAI,CAAC,CAAC,EAAE;AAAA,QACrD,EAAE,KAAK,GAAG;AAAA,MACZ;AAEA,aAAO,GAAG,MAAM,GAAG,IAAI;AAAA,IACzB;AAGA,QAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOjB,YAAY,WAAW,SAAS;AAC9B,aAAK,SAAS;AACd,aAAK,cAAc,QAAQ;AAC3B,kBAAU,KAAK,IAAI;AAAA,MACrB;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,QAAQ,MAAM;AACZ,aAAK,UAAU,WAAW,IAAI;AAAA,MAChC;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,SAAS,MAAM;AACb,YAAI,CAAC,kBAAkB,IAAI,EAAG;AAE9B,cAAM,YAAY;AAAA,UAAgB,KAAK;AAAA,UACrC,EAAE,QAAQ,KAAK,YAAY;AAAA,QAAC;AAC9B,aAAK,KAAK,SAAS;AAAA,MACrB;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,UAAU,MAAM;AACd,YAAI,CAAC,kBAAkB,IAAI,EAAG;AAE9B,aAAK,UAAU;AAAA,MACjB;AAAA;AAAA;AAAA;AAAA,MAKA,QAAQ;AACN,eAAO,KAAK;AAAA,MACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,KAAK,WAAW;AACd,aAAK,UAAU,gBAAgB,SAAS;AAAA,MAC1C;AAAA,IACF;AAQA,QAAM,UAAU,CAAC,OAAO,CAAC,MAAM;AAE7B,YAAM,SAAS,EAAE,UAAU,CAAC,EAAE;AAC9B,aAAO,OAAO,QAAQ,IAAI;AAC1B,aAAO;AAAA,IACT;AAEA,QAAM,YAAN,MAAM,WAAU;AAAA,MACd,cAAc;AAEZ,aAAK,WAAW,QAAQ;AACxB,aAAK,QAAQ,CAAC,KAAK,QAAQ;AAAA,MAC7B;AAAA,MAEA,IAAI,MAAM;AACR,eAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAAA,MACzC;AAAA,MAEA,IAAI,OAAO;AAAE,eAAO,KAAK;AAAA,MAAU;AAAA;AAAA,MAGnC,IAAI,MAAM;AACR,aAAK,IAAI,SAAS,KAAK,IAAI;AAAA,MAC7B;AAAA;AAAA,MAGA,SAAS,OAAO;AAEd,cAAM,OAAO,QAAQ,EAAE,MAAM,CAAC;AAC9B,aAAK,IAAI,IAAI;AACb,aAAK,MAAM,KAAK,IAAI;AAAA,MACtB;AAAA,MAEA,YAAY;AACV,YAAI,KAAK,MAAM,SAAS,GAAG;AACzB,iBAAO,KAAK,MAAM,IAAI;AAAA,QACxB;AAEA,eAAO;AAAA,MACT;AAAA,MAEA,gBAAgB;AACd,eAAO,KAAK,UAAU,EAAE;AAAA,MAC1B;AAAA,MAEA,SAAS;AACP,eAAO,KAAK,UAAU,KAAK,UAAU,MAAM,CAAC;AAAA,MAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,KAAK,SAAS;AAEZ,eAAO,KAAK,YAAY,MAAM,SAAS,KAAK,QAAQ;AAAA,MAGtD;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,OAAO,MAAM,SAAS,MAAM;AAC1B,YAAI,OAAO,SAAS,UAAU;AAC5B,kBAAQ,QAAQ,IAAI;AAAA,QACtB,WAAW,KAAK,UAAU;AACxB,kBAAQ,SAAS,IAAI;AACrB,eAAK,SAAS,QAAQ,CAAC,UAAU,KAAK,MAAM,SAAS,KAAK,CAAC;AAC3D,kBAAQ,UAAU,IAAI;AAAA,QACxB;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAKA,OAAO,UAAU,MAAM;AACrB,YAAI,OAAO,SAAS,SAAU;AAC9B,YAAI,CAAC,KAAK,SAAU;AAEpB,YAAI,KAAK,SAAS,MAAM,QAAM,OAAO,OAAO,QAAQ,GAAG;AAGrD,eAAK,WAAW,CAAC,KAAK,SAAS,KAAK,EAAE,CAAC;AAAA,QACzC,OAAO;AACL,eAAK,SAAS,QAAQ,CAAC,UAAU;AAC/B,uBAAU,UAAU,KAAK;AAAA,UAC3B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAoBA,QAAM,mBAAN,cAA+B,UAAU;AAAA;AAAA;AAAA;AAAA,MAIvC,YAAY,SAAS;AACnB,cAAM;AACN,aAAK,UAAU;AAAA,MACjB;AAAA;AAAA;AAAA;AAAA,MAKA,QAAQ,MAAM;AACZ,YAAI,SAAS,IAAI;AAAE;AAAA,QAAQ;AAE3B,aAAK,IAAI,IAAI;AAAA,MACf;AAAA;AAAA,MAGA,WAAW,OAAO;AAChB,aAAK,SAAS,KAAK;AAAA,MACrB;AAAA,MAEA,WAAW;AACT,aAAK,UAAU;AAAA,MACjB;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,iBAAiB,SAAS,MAAM;AAE9B,cAAM,OAAO,QAAQ;AACrB,YAAI,KAAM,MAAK,QAAQ,YAAY,IAAI;AAEvC,aAAK,IAAI,IAAI;AAAA,MACf;AAAA,MAEA,SAAS;AACP,cAAM,WAAW,IAAI,aAAa,MAAM,KAAK,OAAO;AACpD,eAAO,SAAS,MAAM;AAAA,MACxB;AAAA,MAEA,WAAW;AACT,aAAK,cAAc;AACnB,eAAO;AAAA,MACT;AAAA,IACF;AAWA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,IAAI;AACrB,aAAO,OAAO,OAAO,IAAI,GAAG;AAAA,IAC9B;AAMA,aAAS,iBAAiB,IAAI;AAC5B,aAAO,OAAO,OAAO,IAAI,IAAI;AAAA,IAC/B;AAMA,aAAS,SAAS,IAAI;AACpB,aAAO,OAAO,OAAO,IAAI,IAAI;AAAA,IAC/B;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AAMA,aAAS,qBAAqB,MAAM;AAClC,YAAM,OAAO,KAAK,KAAK,SAAS,CAAC;AAEjC,UAAI,OAAO,SAAS,YAAY,KAAK,gBAAgB,QAAQ;AAC3D,aAAK,OAAO,KAAK,SAAS,GAAG,CAAC;AAC9B,eAAO;AAAA,MACT,OAAO;AACL,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AAWA,aAAS,UAAU,MAAM;AAEvB,YAAM,OAAO,qBAAqB,IAAI;AACtC,YAAM,SAAS,OACV,KAAK,UAAU,KAAK,QACrB,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAC3C,aAAO;AAAA,IACT;AAMA,aAAS,iBAAiB,IAAI;AAC5B,aAAQ,IAAI,OAAO,GAAG,SAAS,IAAI,GAAG,EAAG,KAAK,EAAE,EAAE,SAAS;AAAA,IAC7D;AAOA,aAAS,WAAW,IAAI,QAAQ;AAC9B,YAAM,QAAQ,MAAM,GAAG,KAAK,MAAM;AAClC,aAAO,SAAS,MAAM,UAAU;AAAA,IAClC;AASA,QAAM,aAAa;AAanB,aAAS,uBAAuB,SAAS,EAAE,SAAS,GAAG;AACrD,UAAI,cAAc;AAElB,aAAO,QAAQ,IAAI,CAAC,UAAU;AAC5B,uBAAe;AACf,cAAM,SAAS;AACf,YAAI,KAAK,OAAO,KAAK;AACrB,YAAI,MAAM;AAEV,eAAO,GAAG,SAAS,GAAG;AACpB,gBAAM,QAAQ,WAAW,KAAK,EAAE;AAChC,cAAI,CAAC,OAAO;AACV,mBAAO;AACP;AAAA,UACF;AACA,iBAAO,GAAG,UAAU,GAAG,MAAM,KAAK;AAClC,eAAK,GAAG,UAAU,MAAM,QAAQ,MAAM,CAAC,EAAE,MAAM;AAC/C,cAAI,MAAM,CAAC,EAAE,CAAC,MAAM,QAAQ,MAAM,CAAC,GAAG;AAEpC,mBAAO,OAAO,OAAO,OAAO,MAAM,CAAC,CAAC,IAAI,MAAM;AAAA,UAChD,OAAO;AACL,mBAAO,MAAM,CAAC;AACd,gBAAI,MAAM,CAAC,MAAM,KAAK;AACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT,CAAC,EAAE,IAAI,QAAM,IAAI,EAAE,GAAG,EAAE,KAAK,QAAQ;AAAA,IACvC;AAMA,QAAM,mBAAmB;AACzB,QAAM,WAAW;AACjB,QAAM,sBAAsB;AAC5B,QAAM,YAAY;AAClB,QAAM,cAAc;AACpB,QAAM,mBAAmB;AACzB,QAAM,iBAAiB;AAKvB,QAAM,UAAU,CAAC,OAAO,CAAC,MAAM;AAC7B,YAAM,eAAe;AACrB,UAAI,KAAK,QAAQ;AACf,aAAK,QAAQ;AAAA,UACX;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,QAAM;AAAA,MACV;AACA,aAAO,UAAU;AAAA,QACf,OAAO;AAAA,QACP,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA;AAAA,QAEX,YAAY,CAAC,GAAG,SAAS;AACvB,cAAI,EAAE,UAAU,EAAG,MAAK,YAAY;AAAA,QACtC;AAAA,MACF,GAAG,IAAI;AAAA,IACT;AAGA,QAAM,mBAAmB;AAAA,MACvB,OAAO;AAAA,MAAgB,WAAW;AAAA,IACpC;AACA,QAAM,mBAAmB;AAAA,MACvB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,MACT,UAAU,CAAC,gBAAgB;AAAA,IAC7B;AACA,QAAM,oBAAoB;AAAA,MACxB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,MACT,UAAU,CAAC,gBAAgB;AAAA,IAC7B;AACA,QAAM,qBAAqB;AAAA,MACzB,OAAO;AAAA,IACT;AASA,QAAM,UAAU,SAAS,OAAO,KAAK,cAAc,CAAC,GAAG;AACrD,YAAM,OAAO;AAAA,QACX;AAAA,UACE,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA,UAAU,CAAC;AAAA,QACb;AAAA,QACA;AAAA,MACF;AACA,WAAK,SAAS,KAAK;AAAA,QACjB,OAAO;AAAA;AAAA;AAAA,QAGP,OAAO;AAAA,QACP,KAAK;AAAA,QACL,cAAc;AAAA,QACd,WAAW;AAAA,MACb,CAAC;AACD,YAAM,eAAe;AAAA;AAAA,QAEnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAEA,WAAK,SAAS;AAAA,QACZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAgBE,OAAO;AAAA,YACL;AAAA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UAAM;AAAA;AAAA,QACV;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,QAAM,sBAAsB,QAAQ,MAAM,GAAG;AAC7C,QAAM,uBAAuB,QAAQ,QAAQ,MAAM;AACnD,QAAM,oBAAoB,QAAQ,KAAK,GAAG;AAC1C,QAAM,cAAc;AAAA,MAClB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AACA,QAAM,gBAAgB;AAAA,MACpB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AACA,QAAM,qBAAqB;AAAA,MACzB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AACA,QAAM,cAAc;AAAA,MAClB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU;AAAA,QACR;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,WAAW;AAAA,UACX,UAAU,CAAC,gBAAgB;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AACA,QAAM,aAAa;AAAA,MACjB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AACA,QAAM,wBAAwB;AAAA,MAC5B,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AACA,QAAM,eAAe;AAAA;AAAA,MAEnB,OAAO,YAAY;AAAA,MACnB,WAAW;AAAA,IACb;AASA,QAAM,oBAAoB,SAAS,MAAM;AACvC,aAAO,OAAO;AAAA,QAAO;AAAA,QACnB;AAAA;AAAA,UAEE,YAAY,CAAC,GAAG,SAAS;AAAE,iBAAK,KAAK,cAAc,EAAE,CAAC;AAAA,UAAG;AAAA;AAAA,UAEzD,UAAU,CAAC,GAAG,SAAS;AAAE,gBAAI,KAAK,KAAK,gBAAgB,EAAE,CAAC,EAAG,MAAK,YAAY;AAAA,UAAG;AAAA,QACnF;AAAA,MAAC;AAAA,IACL;AAEA,QAAI,QAAqB,OAAO,OAAO;AAAA,MACrC,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AA+BD,aAAS,sBAAsB,OAAO,UAAU;AAC9C,YAAM,SAAS,MAAM,MAAM,MAAM,QAAQ,CAAC;AAC1C,UAAI,WAAW,KAAK;AAClB,iBAAS,YAAY;AAAA,MACvB;AAAA,IACF;AAMA,aAAS,eAAe,MAAM,SAAS;AAErC,UAAI,KAAK,cAAc,QAAW;AAChC,aAAK,QAAQ,KAAK;AAClB,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAMA,aAAS,cAAc,MAAM,QAAQ;AACnC,UAAI,CAAC,OAAQ;AACb,UAAI,CAAC,KAAK,cAAe;AAOzB,WAAK,QAAQ,SAAS,KAAK,cAAc,MAAM,GAAG,EAAE,KAAK,GAAG,IAAI;AAChE,WAAK,gBAAgB;AACrB,WAAK,WAAW,KAAK,YAAY,KAAK;AACtC,aAAO,KAAK;AAKZ,UAAI,KAAK,cAAc,OAAW,MAAK,YAAY;AAAA,IACrD;AAMA,aAAS,eAAe,MAAM,SAAS;AACrC,UAAI,CAAC,MAAM,QAAQ,KAAK,OAAO,EAAG;AAElC,WAAK,UAAU,OAAO,GAAG,KAAK,OAAO;AAAA,IACvC;AAMA,aAAS,aAAa,MAAM,SAAS;AACnC,UAAI,CAAC,KAAK,MAAO;AACjB,UAAI,KAAK,SAAS,KAAK,IAAK,OAAM,IAAI,MAAM,0CAA0C;AAEtF,WAAK,QAAQ,KAAK;AAClB,aAAO,KAAK;AAAA,IACd;AAMA,aAAS,iBAAiB,MAAM,SAAS;AAEvC,UAAI,KAAK,cAAc,OAAW,MAAK,YAAY;AAAA,IACrD;AAIA,QAAM,iBAAiB,CAAC,MAAM,WAAW;AACvC,UAAI,CAAC,KAAK,YAAa;AAGvB,UAAI,KAAK,OAAQ,OAAM,IAAI,MAAM,wCAAwC;AAEzE,YAAM,eAAe,OAAO,OAAO,CAAC,GAAG,IAAI;AAC3C,aAAO,KAAK,IAAI,EAAE,QAAQ,CAAC,QAAQ;AAAE,eAAO,KAAK,GAAG;AAAA,MAAG,CAAC;AAExD,WAAK,WAAW,aAAa;AAC7B,WAAK,QAAQ,OAAO,aAAa,aAAa,UAAU,aAAa,KAAK,CAAC;AAC3E,WAAK,SAAS;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,UACR,OAAO,OAAO,cAAc,EAAE,YAAY,KAAK,CAAC;AAAA,QAClD;AAAA,MACF;AACA,WAAK,YAAY;AAEjB,aAAO,aAAa;AAAA,IACtB;AAGA,QAAM,kBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACF;AAEA,QAAM,wBAAwB;AAQ9B,aAAS,gBAAgB,aAAa,iBAAiB,YAAY,uBAAuB;AAExF,YAAM,mBAAmB,uBAAO,OAAO,IAAI;AAI3C,UAAI,OAAO,gBAAgB,UAAU;AACnC,oBAAY,WAAW,YAAY,MAAM,GAAG,CAAC;AAAA,MAC/C,WAAW,MAAM,QAAQ,WAAW,GAAG;AACrC,oBAAY,WAAW,WAAW;AAAA,MACpC,OAAO;AACL,eAAO,KAAK,WAAW,EAAE,QAAQ,SAASA,YAAW;AAEnD,iBAAO;AAAA,YACL;AAAA,YACA,gBAAgB,YAAYA,UAAS,GAAG,iBAAiBA,UAAS;AAAA,UACpE;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO;AAYP,eAAS,YAAYA,YAAW,aAAa;AAC3C,YAAI,iBAAiB;AACnB,wBAAc,YAAY,IAAI,OAAK,EAAE,YAAY,CAAC;AAAA,QACpD;AACA,oBAAY,QAAQ,SAAS,SAAS;AACpC,gBAAM,OAAO,QAAQ,MAAM,GAAG;AAC9B,2BAAiB,KAAK,CAAC,CAAC,IAAI,CAACA,YAAW,gBAAgB,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAAA,QAC3E,CAAC;AAAA,MACH;AAAA,IACF;AAUA,aAAS,gBAAgB,SAAS,eAAe;AAG/C,UAAI,eAAe;AACjB,eAAO,OAAO,aAAa;AAAA,MAC7B;AAEA,aAAO,cAAc,OAAO,IAAI,IAAI;AAAA,IACtC;AAMA,aAAS,cAAc,SAAS;AAC9B,aAAO,gBAAgB,SAAS,QAAQ,YAAY,CAAC;AAAA,IACvD;AAYA,QAAM,mBAAmB,CAAC;AAK1B,QAAM,QAAQ,CAAC,YAAY;AACzB,cAAQ,MAAM,OAAO;AAAA,IACvB;AAMA,QAAM,OAAO,CAAC,YAAY,SAAS;AACjC,cAAQ,IAAI,SAAS,OAAO,IAAI,GAAG,IAAI;AAAA,IACzC;AAMA,QAAM,aAAa,CAACC,UAAS,YAAY;AACvC,UAAI,iBAAiB,GAAGA,QAAO,IAAI,OAAO,EAAE,EAAG;AAE/C,cAAQ,IAAI,oBAAoBA,QAAO,KAAK,OAAO,EAAE;AACrD,uBAAiB,GAAGA,QAAO,IAAI,OAAO,EAAE,IAAI;AAAA,IAC9C;AAQA,QAAM,kBAAkB,IAAI,MAAM;AA8BlC,aAAS,gBAAgB,MAAM,SAAS,EAAE,IAAI,GAAG;AAC/C,UAAI,SAAS;AACb,YAAM,aAAa,KAAK,GAAG;AAE3B,YAAM,OAAO,CAAC;AAEd,YAAM,YAAY,CAAC;AAEnB,eAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,KAAK;AACxC,kBAAU,IAAI,MAAM,IAAI,WAAW,CAAC;AACpC,aAAK,IAAI,MAAM,IAAI;AACnB,kBAAU,iBAAiB,QAAQ,IAAI,CAAC,CAAC;AAAA,MAC3C;AAGA,WAAK,GAAG,IAAI;AACZ,WAAK,GAAG,EAAE,QAAQ;AAClB,WAAK,GAAG,EAAE,SAAS;AAAA,IACrB;AAKA,aAAS,gBAAgB,MAAM;AAC7B,UAAI,CAAC,MAAM,QAAQ,KAAK,KAAK,EAAG;AAEhC,UAAI,KAAK,QAAQ,KAAK,gBAAgB,KAAK,aAAa;AACtD,cAAM,oEAAoE;AAC1E,cAAM;AAAA,MACR;AAEA,UAAI,OAAO,KAAK,eAAe,YAAY,KAAK,eAAe,MAAM;AACnE,cAAM,2BAA2B;AACjC,cAAM;AAAA,MACR;AAEA,sBAAgB,MAAM,KAAK,OAAO,EAAE,KAAK,aAAa,CAAC;AACvD,WAAK,QAAQ,uBAAuB,KAAK,OAAO,EAAE,UAAU,GAAG,CAAC;AAAA,IAClE;AAKA,aAAS,cAAc,MAAM;AAC3B,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG,EAAG;AAE9B,UAAI,KAAK,QAAQ,KAAK,cAAc,KAAK,WAAW;AAClD,cAAM,8DAA8D;AACpE,cAAM;AAAA,MACR;AAEA,UAAI,OAAO,KAAK,aAAa,YAAY,KAAK,aAAa,MAAM;AAC/D,cAAM,yBAAyB;AAC/B,cAAM;AAAA,MACR;AAEA,sBAAgB,MAAM,KAAK,KAAK,EAAE,KAAK,WAAW,CAAC;AACnD,WAAK,MAAM,uBAAuB,KAAK,KAAK,EAAE,UAAU,GAAG,CAAC;AAAA,IAC9D;AAaA,aAAS,WAAW,MAAM;AACxB,UAAI,KAAK,SAAS,OAAO,KAAK,UAAU,YAAY,KAAK,UAAU,MAAM;AACvE,aAAK,aAAa,KAAK;AACvB,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAKA,aAAS,WAAW,MAAM;AACxB,iBAAW,IAAI;AAEf,UAAI,OAAO,KAAK,eAAe,UAAU;AACvC,aAAK,aAAa,EAAE,OAAO,KAAK,WAAW;AAAA,MAC7C;AACA,UAAI,OAAO,KAAK,aAAa,UAAU;AACrC,aAAK,WAAW,EAAE,OAAO,KAAK,SAAS;AAAA,MACzC;AAEA,sBAAgB,IAAI;AACpB,oBAAc,IAAI;AAAA,IACpB;AAoBA,aAAS,gBAAgB,UAAU;AAOjC,eAAS,OAAO,OAAO,QAAQ;AAC7B,eAAO,IAAI;AAAA,UACT,OAAO,KAAK;AAAA,UACZ,OACG,SAAS,mBAAmB,MAAM,OAClC,SAAS,eAAe,MAAM,OAC9B,SAAS,MAAM;AAAA,QACpB;AAAA,MACF;AAAA,MAeA,MAAM,WAAW;AAAA,QACf,cAAc;AACZ,eAAK,eAAe,CAAC;AAErB,eAAK,UAAU,CAAC;AAChB,eAAK,UAAU;AACf,eAAK,WAAW;AAAA,QAClB;AAAA;AAAA,QAGA,QAAQ,IAAI,MAAM;AAChB,eAAK,WAAW,KAAK;AAErB,eAAK,aAAa,KAAK,OAAO,IAAI;AAClC,eAAK,QAAQ,KAAK,CAAC,MAAM,EAAE,CAAC;AAC5B,eAAK,WAAW,iBAAiB,EAAE,IAAI;AAAA,QACzC;AAAA,QAEA,UAAU;AACR,cAAI,KAAK,QAAQ,WAAW,GAAG;AAG7B,iBAAK,OAAO,MAAM;AAAA,UACpB;AACA,gBAAM,cAAc,KAAK,QAAQ,IAAI,QAAM,GAAG,CAAC,CAAC;AAChD,eAAK,YAAY,OAAO,uBAAuB,aAAa,EAAE,UAAU,IAAI,CAAC,GAAG,IAAI;AACpF,eAAK,YAAY;AAAA,QACnB;AAAA;AAAA,QAGA,KAAK,GAAG;AACN,eAAK,UAAU,YAAY,KAAK;AAChC,gBAAM,QAAQ,KAAK,UAAU,KAAK,CAAC;AACnC,cAAI,CAAC,OAAO;AAAE,mBAAO;AAAA,UAAM;AAG3B,gBAAM,IAAI,MAAM,UAAU,CAAC,IAAIC,OAAMA,KAAI,KAAK,OAAO,MAAS;AAE9D,gBAAM,YAAY,KAAK,aAAa,CAAC;AAGrC,gBAAM,OAAO,GAAG,CAAC;AAEjB,iBAAO,OAAO,OAAO,OAAO,SAAS;AAAA,QACvC;AAAA,MACF;AAAA,MAiCA,MAAM,oBAAoB;AAAA,QACxB,cAAc;AAEZ,eAAK,QAAQ,CAAC;AAEd,eAAK,eAAe,CAAC;AACrB,eAAK,QAAQ;AAEb,eAAK,YAAY;AACjB,eAAK,aAAa;AAAA,QACpB;AAAA;AAAA,QAGA,WAAW,OAAO;AAChB,cAAI,KAAK,aAAa,KAAK,EAAG,QAAO,KAAK,aAAa,KAAK;AAE5D,gBAAM,UAAU,IAAI,WAAW;AAC/B,eAAK,MAAM,MAAM,KAAK,EAAE,QAAQ,CAAC,CAAC,IAAI,IAAI,MAAM,QAAQ,QAAQ,IAAI,IAAI,CAAC;AACzE,kBAAQ,QAAQ;AAChB,eAAK,aAAa,KAAK,IAAI;AAC3B,iBAAO;AAAA,QACT;AAAA,QAEA,6BAA6B;AAC3B,iBAAO,KAAK,eAAe;AAAA,QAC7B;AAAA,QAEA,cAAc;AACZ,eAAK,aAAa;AAAA,QACpB;AAAA;AAAA,QAGA,QAAQ,IAAI,MAAM;AAChB,eAAK,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC;AAC1B,cAAI,KAAK,SAAS,QAAS,MAAK;AAAA,QAClC;AAAA;AAAA,QAGA,KAAK,GAAG;AACN,gBAAM,IAAI,KAAK,WAAW,KAAK,UAAU;AACzC,YAAE,YAAY,KAAK;AACnB,cAAI,SAAS,EAAE,KAAK,CAAC;AAiCrB,cAAI,KAAK,2BAA2B,GAAG;AACrC,gBAAI,UAAU,OAAO,UAAU,KAAK,UAAW;AAAA,iBAAO;AACpD,oBAAM,KAAK,KAAK,WAAW,CAAC;AAC5B,iBAAG,YAAY,KAAK,YAAY;AAChC,uBAAS,GAAG,KAAK,CAAC;AAAA,YACpB;AAAA,UACF;AAEA,cAAI,QAAQ;AACV,iBAAK,cAAc,OAAO,WAAW;AACrC,gBAAI,KAAK,eAAe,KAAK,OAAO;AAElC,mBAAK,YAAY;AAAA,YACnB;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AASA,eAAS,eAAe,MAAM;AAC5B,cAAM,KAAK,IAAI,oBAAoB;AAEnC,aAAK,SAAS,QAAQ,UAAQ,GAAG,QAAQ,KAAK,OAAO,EAAE,MAAM,MAAM,MAAM,QAAQ,CAAC,CAAC;AAEnF,YAAI,KAAK,eAAe;AACtB,aAAG,QAAQ,KAAK,eAAe,EAAE,MAAM,MAAM,CAAC;AAAA,QAChD;AACA,YAAI,KAAK,SAAS;AAChB,aAAG,QAAQ,KAAK,SAAS,EAAE,MAAM,UAAU,CAAC;AAAA,QAC9C;AAEA,eAAO;AAAA,MACT;AAyCA,eAAS,YAAY,MAAM,QAAQ;AACjC,cAAM;AAAA;AAAA,UAAmC;AAAA;AACzC,YAAI,KAAK,WAAY,QAAO;AAE5B;AAAA,UACE;AAAA;AAAA;AAAA,UAGA;AAAA,UACA;AAAA,UACA;AAAA,QACF,EAAE,QAAQ,SAAO,IAAI,MAAM,MAAM,CAAC;AAElC,iBAAS,mBAAmB,QAAQ,SAAO,IAAI,MAAM,MAAM,CAAC;AAG5D,aAAK,gBAAgB;AAErB;AAAA,UACE;AAAA;AAAA;AAAA,UAGA;AAAA;AAAA,UAEA;AAAA,QACF,EAAE,QAAQ,SAAO,IAAI,MAAM,MAAM,CAAC;AAElC,aAAK,aAAa;AAElB,YAAI,iBAAiB;AACrB,YAAI,OAAO,KAAK,aAAa,YAAY,KAAK,SAAS,UAAU;AAI/D,eAAK,WAAW,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ;AAC/C,2BAAiB,KAAK,SAAS;AAC/B,iBAAO,KAAK,SAAS;AAAA,QACvB;AACA,yBAAiB,kBAAkB;AAEnC,YAAI,KAAK,UAAU;AACjB,eAAK,WAAW,gBAAgB,KAAK,UAAU,SAAS,gBAAgB;AAAA,QAC1E;AAEA,cAAM,mBAAmB,OAAO,gBAAgB,IAAI;AAEpD,YAAI,QAAQ;AACV,cAAI,CAAC,KAAK,MAAO,MAAK,QAAQ;AAC9B,gBAAM,UAAU,OAAO,MAAM,KAAK;AAClC,cAAI,CAAC,KAAK,OAAO,CAAC,KAAK,eAAgB,MAAK,MAAM;AAClD,cAAI,KAAK,IAAK,OAAM,QAAQ,OAAO,MAAM,GAAG;AAC5C,gBAAM,gBAAgB,OAAO,MAAM,GAAG,KAAK;AAC3C,cAAI,KAAK,kBAAkB,OAAO,eAAe;AAC/C,kBAAM,kBAAkB,KAAK,MAAM,MAAM,MAAM,OAAO;AAAA,UACxD;AAAA,QACF;AACA,YAAI,KAAK,QAAS,OAAM,YAAY;AAAA;AAAA,UAAuC,KAAK;AAAA,QAAQ;AACxF,YAAI,CAAC,KAAK,SAAU,MAAK,WAAW,CAAC;AAErC,aAAK,WAAW,CAAC,EAAE,OAAO,GAAG,KAAK,SAAS,IAAI,SAAS,GAAG;AACzD,iBAAO,kBAAkB,MAAM,SAAS,OAAO,CAAC;AAAA,QAClD,CAAC,CAAC;AACF,aAAK,SAAS,QAAQ,SAAS,GAAG;AAAE;AAAA;AAAA,YAA+B;AAAA,YAAI;AAAA,UAAK;AAAA,QAAG,CAAC;AAEhF,YAAI,KAAK,QAAQ;AACf,sBAAY,KAAK,QAAQ,MAAM;AAAA,QACjC;AAEA,cAAM,UAAU,eAAe,KAAK;AACpC,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,SAAS,mBAAoB,UAAS,qBAAqB,CAAC;AAGjE,UAAI,SAAS,YAAY,SAAS,SAAS,SAAS,MAAM,GAAG;AAC3D,cAAM,IAAI,MAAM,2FAA2F;AAAA,MAC7G;AAGA,eAAS,mBAAmB,UAAU,SAAS,oBAAoB,CAAC,CAAC;AAErE,aAAO;AAAA;AAAA,QAA+B;AAAA,MAAS;AAAA,IACjD;AAaA,aAAS,mBAAmB,MAAM;AAChC,UAAI,CAAC,KAAM,QAAO;AAElB,aAAO,KAAK,kBAAkB,mBAAmB,KAAK,MAAM;AAAA,IAC9D;AAYA,aAAS,kBAAkB,MAAM;AAC/B,UAAI,KAAK,YAAY,CAAC,KAAK,gBAAgB;AACzC,aAAK,iBAAiB,KAAK,SAAS,IAAI,SAAS,SAAS;AACxD,iBAAO,UAAU,MAAM,EAAE,UAAU,KAAK,GAAG,OAAO;AAAA,QACpD,CAAC;AAAA,MACH;AAKA,UAAI,KAAK,gBAAgB;AACvB,eAAO,KAAK;AAAA,MACd;AAMA,UAAI,mBAAmB,IAAI,GAAG;AAC5B,eAAO,UAAU,MAAM,EAAE,QAAQ,KAAK,SAAS,UAAU,KAAK,MAAM,IAAI,KAAK,CAAC;AAAA,MAChF;AAEA,UAAI,OAAO,SAAS,IAAI,GAAG;AACzB,eAAO,UAAU,IAAI;AAAA,MACvB;AAGA,aAAO;AAAA,IACT;AAEA,QAAI,UAAU;AAEd,QAAM,qBAAN,cAAiC,MAAM;AAAA,MACrC,YAAY,QAAQ,MAAM;AACxB,cAAM,MAAM;AACZ,aAAK,OAAO;AACZ,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AA+BA,QAAM,SAAS;AACf,QAAM,UAAU;AAChB,QAAM,WAAW,OAAO,SAAS;AACjC,QAAM,mBAAmB;AAMzB,QAAM,OAAO,SAAS,MAAM;AAG1B,YAAM,YAAY,uBAAO,OAAO,IAAI;AAEpC,YAAM,UAAU,uBAAO,OAAO,IAAI;AAElC,YAAM,UAAU,CAAC;AAIjB,UAAI,YAAY;AAChB,YAAM,qBAAqB;AAE3B,YAAM,qBAAqB,EAAE,mBAAmB,MAAM,MAAM,cAAc,UAAU,CAAC,EAAE;AAKvF,UAAI,UAAU;AAAA,QACZ,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,WAAW;AAAA;AAAA;AAAA,QAGX,WAAW;AAAA,MACb;AAQA,eAAS,mBAAmB,cAAc;AACxC,eAAO,QAAQ,cAAc,KAAK,YAAY;AAAA,MAChD;AAKA,eAAS,cAAc,OAAO;AAC5B,YAAI,UAAU,MAAM,YAAY;AAEhC,mBAAW,MAAM,aAAa,MAAM,WAAW,YAAY;AAG3D,cAAM,QAAQ,QAAQ,iBAAiB,KAAK,OAAO;AACnD,YAAI,OAAO;AACT,gBAAM,WAAW,YAAY,MAAM,CAAC,CAAC;AACrC,cAAI,CAAC,UAAU;AACb,iBAAK,mBAAmB,QAAQ,MAAM,MAAM,CAAC,CAAC,CAAC;AAC/C,iBAAK,qDAAqD,KAAK;AAAA,UACjE;AACA,iBAAO,WAAW,MAAM,CAAC,IAAI;AAAA,QAC/B;AAEA,eAAO,QACJ,MAAM,KAAK,EACX,KAAK,CAAC,WAAW,mBAAmB,MAAM,KAAK,YAAY,MAAM,CAAC;AAAA,MACvE;AAuBA,eAASC,WAAU,oBAAoB,eAAe,gBAAgB;AACpE,YAAI,OAAO;AACX,YAAI,eAAe;AACnB,YAAI,OAAO,kBAAkB,UAAU;AACrC,iBAAO;AACP,2BAAiB,cAAc;AAC/B,yBAAe,cAAc;AAAA,QAC/B,OAAO;AAEL,qBAAW,UAAU,qDAAqD;AAC1E,qBAAW,UAAU,uGAAuG;AAC5H,yBAAe;AACf,iBAAO;AAAA,QACT;AAIA,YAAI,mBAAmB,QAAW;AAAE,2BAAiB;AAAA,QAAM;AAG3D,cAAM,UAAU;AAAA,UACd;AAAA,UACA,UAAU;AAAA,QACZ;AAGA,aAAK,oBAAoB,OAAO;AAIhC,cAAM,SAAS,QAAQ,SACnB,QAAQ,SACR,WAAW,QAAQ,UAAU,QAAQ,MAAM,cAAc;AAE7D,eAAO,OAAO,QAAQ;AAEtB,aAAK,mBAAmB,MAAM;AAE9B,eAAO;AAAA,MACT;AAWA,eAAS,WAAW,cAAc,iBAAiB,gBAAgB,cAAc;AAC/E,cAAM,cAAc,uBAAO,OAAO,IAAI;AAQtC,iBAAS,YAAY,MAAM,WAAW;AACpC,iBAAO,KAAK,SAAS,SAAS;AAAA,QAChC;AAEA,iBAAS,kBAAkB;AACzB,cAAI,CAAC,IAAI,UAAU;AACjB,oBAAQ,QAAQ,UAAU;AAC1B;AAAA,UACF;AAEA,cAAI,YAAY;AAChB,cAAI,iBAAiB,YAAY;AACjC,cAAI,QAAQ,IAAI,iBAAiB,KAAK,UAAU;AAChD,cAAI,MAAM;AAEV,iBAAO,OAAO;AACZ,mBAAO,WAAW,UAAU,WAAW,MAAM,KAAK;AAClD,kBAAM,OAAO,SAAS,mBAAmB,MAAM,CAAC,EAAE,YAAY,IAAI,MAAM,CAAC;AACzE,kBAAM,OAAO,YAAY,KAAK,IAAI;AAClC,gBAAI,MAAM;AACR,oBAAM,CAAC,MAAM,gBAAgB,IAAI;AACjC,sBAAQ,QAAQ,GAAG;AACnB,oBAAM;AAEN,0BAAY,IAAI,KAAK,YAAY,IAAI,KAAK,KAAK;AAC/C,kBAAI,YAAY,IAAI,KAAK,iBAAkB,cAAa;AACxD,kBAAI,KAAK,WAAW,GAAG,GAAG;AAGxB,uBAAO,MAAM,CAAC;AAAA,cAChB,OAAO;AACL,sBAAM,WAAW,SAAS,iBAAiB,IAAI,KAAK;AACpD,4BAAY,MAAM,CAAC,GAAG,QAAQ;AAAA,cAChC;AAAA,YACF,OAAO;AACL,qBAAO,MAAM,CAAC;AAAA,YAChB;AACA,wBAAY,IAAI,iBAAiB;AACjC,oBAAQ,IAAI,iBAAiB,KAAK,UAAU;AAAA,UAC9C;AACA,iBAAO,WAAW,UAAU,SAAS;AACrC,kBAAQ,QAAQ,GAAG;AAAA,QACrB;AAEA,iBAAS,qBAAqB;AAC5B,cAAI,eAAe,GAAI;AAEvB,cAAIC,UAAS;AAEb,cAAI,OAAO,IAAI,gBAAgB,UAAU;AACvC,gBAAI,CAAC,UAAU,IAAI,WAAW,GAAG;AAC/B,sBAAQ,QAAQ,UAAU;AAC1B;AAAA,YACF;AACA,YAAAA,UAAS,WAAW,IAAI,aAAa,YAAY,MAAM,cAAc,IAAI,WAAW,CAAC;AACrF,0BAAc,IAAI,WAAW;AAAA,YAAiCA,QAAO;AAAA,UACvE,OAAO;AACL,YAAAA,UAAS,cAAc,YAAY,IAAI,YAAY,SAAS,IAAI,cAAc,IAAI;AAAA,UACpF;AAMA,cAAI,IAAI,YAAY,GAAG;AACrB,yBAAaA,QAAO;AAAA,UACtB;AACA,kBAAQ,iBAAiBA,QAAO,UAAUA,QAAO,QAAQ;AAAA,QAC3D;AAEA,iBAAS,gBAAgB;AACvB,cAAI,IAAI,eAAe,MAAM;AAC3B,+BAAmB;AAAA,UACrB,OAAO;AACL,4BAAgB;AAAA,UAClB;AACA,uBAAa;AAAA,QACf;AAMA,iBAAS,YAAY,SAAS,OAAO;AACnC,cAAI,YAAY,GAAI;AAEpB,kBAAQ,WAAW,KAAK;AACxB,kBAAQ,QAAQ,OAAO;AACvB,kBAAQ,SAAS;AAAA,QACnB;AAMA,iBAAS,eAAe,OAAO,OAAO;AACpC,cAAI,IAAI;AACR,gBAAM,MAAM,MAAM,SAAS;AAC3B,iBAAO,KAAK,KAAK;AACf,gBAAI,CAAC,MAAM,MAAM,CAAC,GAAG;AAAE;AAAK;AAAA,YAAU;AACtC,kBAAM,QAAQ,SAAS,iBAAiB,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC;AAC5D,kBAAM,OAAO,MAAM,CAAC;AACpB,gBAAI,OAAO;AACT,0BAAY,MAAM,KAAK;AAAA,YACzB,OAAO;AACL,2BAAa;AACb,8BAAgB;AAChB,2BAAa;AAAA,YACf;AACA;AAAA,UACF;AAAA,QACF;AAMA,iBAAS,aAAa,MAAM,OAAO;AACjC,cAAI,KAAK,SAAS,OAAO,KAAK,UAAU,UAAU;AAChD,oBAAQ,SAAS,SAAS,iBAAiB,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,UACtE;AACA,cAAI,KAAK,YAAY;AAEnB,gBAAI,KAAK,WAAW,OAAO;AACzB,0BAAY,YAAY,SAAS,iBAAiB,KAAK,WAAW,KAAK,KAAK,KAAK,WAAW,KAAK;AACjG,2BAAa;AAAA,YACf,WAAW,KAAK,WAAW,QAAQ;AAEjC,6BAAe,KAAK,YAAY,KAAK;AACrC,2BAAa;AAAA,YACf;AAAA,UACF;AAEA,gBAAM,OAAO,OAAO,MAAM,EAAE,QAAQ,EAAE,OAAO,IAAI,EAAE,CAAC;AACpD,iBAAO;AAAA,QACT;AAQA,iBAAS,UAAU,MAAM,OAAO,oBAAoB;AAClD,cAAI,UAAU,WAAW,KAAK,OAAO,kBAAkB;AAEvD,cAAI,SAAS;AACX,gBAAI,KAAK,QAAQ,GAAG;AAClB,oBAAM,OAAO,IAAI,SAAS,IAAI;AAC9B,mBAAK,QAAQ,EAAE,OAAO,IAAI;AAC1B,kBAAI,KAAK,eAAgB,WAAU;AAAA,YACrC;AAEA,gBAAI,SAAS;AACX,qBAAO,KAAK,cAAc,KAAK,QAAQ;AACrC,uBAAO,KAAK;AAAA,cACd;AACA,qBAAO;AAAA,YACT;AAAA,UACF;AAGA,cAAI,KAAK,gBAAgB;AACvB,mBAAO,UAAU,KAAK,QAAQ,OAAO,kBAAkB;AAAA,UACzD;AAAA,QACF;AAOA,iBAAS,SAAS,QAAQ;AACxB,cAAI,IAAI,QAAQ,eAAe,GAAG;AAGhC,0BAAc,OAAO,CAAC;AACtB,mBAAO;AAAA,UACT,OAAO;AAGL,uCAA2B;AAC3B,mBAAO;AAAA,UACT;AAAA,QACF;AAQA,iBAAS,aAAa,OAAO;AAC3B,gBAAM,SAAS,MAAM,CAAC;AACtB,gBAAM,UAAU,MAAM;AAEtB,gBAAM,OAAO,IAAI,SAAS,OAAO;AAEjC,gBAAM,kBAAkB,CAAC,QAAQ,eAAe,QAAQ,UAAU,CAAC;AACnE,qBAAW,MAAM,iBAAiB;AAChC,gBAAI,CAAC,GAAI;AACT,eAAG,OAAO,IAAI;AACd,gBAAI,KAAK,eAAgB,QAAO,SAAS,MAAM;AAAA,UACjD;AAEA,cAAI,QAAQ,MAAM;AAChB,0BAAc;AAAA,UAChB,OAAO;AACL,gBAAI,QAAQ,cAAc;AACxB,4BAAc;AAAA,YAChB;AACA,0BAAc;AACd,gBAAI,CAAC,QAAQ,eAAe,CAAC,QAAQ,cAAc;AACjD,2BAAa;AAAA,YACf;AAAA,UACF;AACA,uBAAa,SAAS,KAAK;AAC3B,iBAAO,QAAQ,cAAc,IAAI,OAAO;AAAA,QAC1C;AAOA,iBAAS,WAAW,OAAO;AACzB,gBAAM,SAAS,MAAM,CAAC;AACtB,gBAAM,qBAAqB,gBAAgB,UAAU,MAAM,KAAK;AAEhE,gBAAM,UAAU,UAAU,KAAK,OAAO,kBAAkB;AACxD,cAAI,CAAC,SAAS;AAAE,mBAAO;AAAA,UAAU;AAEjC,gBAAM,SAAS;AACf,cAAI,IAAI,YAAY,IAAI,SAAS,OAAO;AACtC,0BAAc;AACd,wBAAY,QAAQ,IAAI,SAAS,KAAK;AAAA,UACxC,WAAW,IAAI,YAAY,IAAI,SAAS,QAAQ;AAC9C,0BAAc;AACd,2BAAe,IAAI,UAAU,KAAK;AAAA,UACpC,WAAW,OAAO,MAAM;AACtB,0BAAc;AAAA,UAChB,OAAO;AACL,gBAAI,EAAE,OAAO,aAAa,OAAO,aAAa;AAC5C,4BAAc;AAAA,YAChB;AACA,0BAAc;AACd,gBAAI,OAAO,YAAY;AACrB,2BAAa;AAAA,YACf;AAAA,UACF;AACA,aAAG;AACD,gBAAI,IAAI,OAAO;AACb,sBAAQ,UAAU;AAAA,YACpB;AACA,gBAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,aAAa;AACjC,2BAAa,IAAI;AAAA,YACnB;AACA,kBAAM,IAAI;AAAA,UACZ,SAAS,QAAQ,QAAQ;AACzB,cAAI,QAAQ,QAAQ;AAClB,yBAAa,QAAQ,QAAQ,KAAK;AAAA,UACpC;AACA,iBAAO,OAAO,YAAY,IAAI,OAAO;AAAA,QACvC;AAEA,iBAAS,uBAAuB;AAC9B,gBAAM,OAAO,CAAC;AACd,mBAAS,UAAU,KAAK,YAAY,UAAU,UAAU,QAAQ,QAAQ;AACtE,gBAAI,QAAQ,OAAO;AACjB,mBAAK,QAAQ,QAAQ,KAAK;AAAA,YAC5B;AAAA,UACF;AACA,eAAK,QAAQ,UAAQ,QAAQ,SAAS,IAAI,CAAC;AAAA,QAC7C;AAGA,YAAI,YAAY,CAAC;AAQjB,iBAAS,cAAc,iBAAiB,OAAO;AAC7C,gBAAM,SAAS,SAAS,MAAM,CAAC;AAG/B,wBAAc;AAEd,cAAI,UAAU,MAAM;AAClB,0BAAc;AACd,mBAAO;AAAA,UACT;AAMA,cAAI,UAAU,SAAS,WAAW,MAAM,SAAS,SAAS,UAAU,UAAU,MAAM,SAAS,WAAW,IAAI;AAE1G,0BAAc,gBAAgB,MAAM,MAAM,OAAO,MAAM,QAAQ,CAAC;AAChE,gBAAI,CAAC,WAAW;AAEd,oBAAM,MAAM,IAAI,MAAM,wBAAwB,YAAY,GAAG;AAC7D,kBAAI,eAAe;AACnB,kBAAI,UAAU,UAAU;AACxB,oBAAM;AAAA,YACR;AACA,mBAAO;AAAA,UACT;AACA,sBAAY;AAEZ,cAAI,MAAM,SAAS,SAAS;AAC1B,mBAAO,aAAa,KAAK;AAAA,UAC3B,WAAW,MAAM,SAAS,aAAa,CAAC,gBAAgB;AAGtD,kBAAM,MAAM,IAAI,MAAM,qBAAqB,SAAS,kBAAkB,IAAI,SAAS,eAAe,GAAG;AACrG,gBAAI,OAAO;AACX,kBAAM;AAAA,UACR,WAAW,MAAM,SAAS,OAAO;AAC/B,kBAAM,YAAY,WAAW,KAAK;AAClC,gBAAI,cAAc,UAAU;AAC1B,qBAAO;AAAA,YACT;AAAA,UACF;AAKA,cAAI,MAAM,SAAS,aAAa,WAAW,IAAI;AAE7C,mBAAO;AAAA,UACT;AAMA,cAAI,aAAa,OAAU,aAAa,MAAM,QAAQ,GAAG;AACvD,kBAAM,MAAM,IAAI,MAAM,2DAA2D;AACjF,kBAAM;AAAA,UACR;AAUA,wBAAc;AACd,iBAAO,OAAO;AAAA,QAChB;AAEA,cAAM,WAAW,YAAY,YAAY;AACzC,YAAI,CAAC,UAAU;AACb,gBAAM,mBAAmB,QAAQ,MAAM,YAAY,CAAC;AACpD,gBAAM,IAAI,MAAM,wBAAwB,eAAe,GAAG;AAAA,QAC5D;AAEA,cAAM,KAAK,gBAAgB,QAAQ;AACnC,YAAI,SAAS;AAEb,YAAI,MAAM,gBAAgB;AAE1B,cAAM,gBAAgB,CAAC;AACvB,cAAM,UAAU,IAAI,QAAQ,UAAU,OAAO;AAC7C,6BAAqB;AACrB,YAAI,aAAa;AACjB,YAAI,YAAY;AAChB,YAAI,QAAQ;AACZ,YAAI,aAAa;AACjB,YAAI,2BAA2B;AAE/B,YAAI;AACF,cAAI,CAAC,SAAS,cAAc;AAC1B,gBAAI,QAAQ,YAAY;AAExB,uBAAS;AACP;AACA,kBAAI,0BAA0B;AAG5B,2CAA2B;AAAA,cAC7B,OAAO;AACL,oBAAI,QAAQ,YAAY;AAAA,cAC1B;AACA,kBAAI,QAAQ,YAAY;AAExB,oBAAM,QAAQ,IAAI,QAAQ,KAAK,eAAe;AAG9C,kBAAI,CAAC,MAAO;AAEZ,oBAAM,cAAc,gBAAgB,UAAU,OAAO,MAAM,KAAK;AAChE,oBAAM,iBAAiB,cAAc,aAAa,KAAK;AACvD,sBAAQ,MAAM,QAAQ;AAAA,YACxB;AACA,0BAAc,gBAAgB,UAAU,KAAK,CAAC;AAAA,UAChD,OAAO;AACL,qBAAS,aAAa,iBAAiB,OAAO;AAAA,UAChD;AAEA,kBAAQ,SAAS;AACjB,mBAAS,QAAQ,OAAO;AAExB,iBAAO;AAAA,YACL,UAAU;AAAA,YACV,OAAO;AAAA,YACP;AAAA,YACA,SAAS;AAAA,YACT,UAAU;AAAA,YACV,MAAM;AAAA,UACR;AAAA,QACF,SAAS,KAAK;AACZ,cAAI,IAAI,WAAW,IAAI,QAAQ,SAAS,SAAS,GAAG;AAClD,mBAAO;AAAA,cACL,UAAU;AAAA,cACV,OAAO,OAAO,eAAe;AAAA,cAC7B,SAAS;AAAA,cACT,WAAW;AAAA,cACX,YAAY;AAAA,gBACV,SAAS,IAAI;AAAA,gBACb;AAAA,gBACA,SAAS,gBAAgB,MAAM,QAAQ,KAAK,QAAQ,GAAG;AAAA,gBACvD,MAAM,IAAI;AAAA,gBACV,aAAa;AAAA,cACf;AAAA,cACA,UAAU;AAAA,YACZ;AAAA,UACF,WAAW,WAAW;AACpB,mBAAO;AAAA,cACL,UAAU;AAAA,cACV,OAAO,OAAO,eAAe;AAAA,cAC7B,SAAS;AAAA,cACT,WAAW;AAAA,cACX,aAAa;AAAA,cACb,UAAU;AAAA,cACV,MAAM;AAAA,YACR;AAAA,UACF,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AASA,eAAS,wBAAwB,MAAM;AACrC,cAAM,SAAS;AAAA,UACb,OAAO,OAAO,IAAI;AAAA,UAClB,SAAS;AAAA,UACT,WAAW;AAAA,UACX,MAAM;AAAA,UACN,UAAU,IAAI,QAAQ,UAAU,OAAO;AAAA,QACzC;AACA,eAAO,SAAS,QAAQ,IAAI;AAC5B,eAAO;AAAA,MACT;AAgBA,eAAS,cAAc,MAAM,gBAAgB;AAC3C,yBAAiB,kBAAkB,QAAQ,aAAa,OAAO,KAAK,SAAS;AAC7E,cAAM,YAAY,wBAAwB,IAAI;AAE9C,cAAM,UAAU,eAAe,OAAO,WAAW,EAAE,OAAO,aAAa,EAAE;AAAA,UAAI,UAC3E,WAAW,MAAM,MAAM,KAAK;AAAA,QAC9B;AACA,gBAAQ,QAAQ,SAAS;AAEzB,cAAM,SAAS,QAAQ,KAAK,CAAC,GAAG,MAAM;AAEpC,cAAI,EAAE,cAAc,EAAE,UAAW,QAAO,EAAE,YAAY,EAAE;AAIxD,cAAI,EAAE,YAAY,EAAE,UAAU;AAC5B,gBAAI,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU;AACrD,qBAAO;AAAA,YACT,WAAW,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU;AAC5D,qBAAO;AAAA,YACT;AAAA,UACF;AAMA,iBAAO;AAAA,QACT,CAAC;AAED,cAAM,CAAC,MAAM,UAAU,IAAI;AAG3B,cAAM,SAAS;AACf,eAAO,aAAa;AAEpB,eAAO;AAAA,MACT;AASA,eAAS,gBAAgB,SAAS,aAAa,YAAY;AACzD,cAAM,WAAY,eAAe,QAAQ,WAAW,KAAM;AAE1D,gBAAQ,UAAU,IAAI,MAAM;AAC5B,gBAAQ,UAAU,IAAI,YAAY,QAAQ,EAAE;AAAA,MAC9C;AAOA,eAAS,iBAAiB,SAAS;AAEjC,YAAI,OAAO;AACX,cAAM,WAAW,cAAc,OAAO;AAEtC,YAAI,mBAAmB,QAAQ,EAAG;AAElC;AAAA,UAAK;AAAA,UACH,EAAE,IAAI,SAAS,SAAS;AAAA,QAAC;AAE3B,YAAI,QAAQ,QAAQ,aAAa;AAC/B,kBAAQ,IAAI,0FAA0F,OAAO;AAC7G;AAAA,QACF;AAOA,YAAI,QAAQ,SAAS,SAAS,GAAG;AAC/B,cAAI,CAAC,QAAQ,qBAAqB;AAChC,oBAAQ,KAAK,+FAA+F;AAC5G,oBAAQ,KAAK,2DAA2D;AACxE,oBAAQ,KAAK,kCAAkC;AAC/C,oBAAQ,KAAK,OAAO;AAAA,UACtB;AACA,cAAI,QAAQ,oBAAoB;AAC9B,kBAAM,MAAM,IAAI;AAAA,cACd;AAAA,cACA,QAAQ;AAAA,YACV;AACA,kBAAM;AAAA,UACR;AAAA,QACF;AAEA,eAAO;AACP,cAAM,OAAO,KAAK;AAClB,cAAM,SAAS,WAAWD,WAAU,MAAM,EAAE,UAAU,gBAAgB,KAAK,CAAC,IAAI,cAAc,IAAI;AAElG,gBAAQ,YAAY,OAAO;AAC3B,gBAAQ,QAAQ,cAAc;AAC9B,wBAAgB,SAAS,UAAU,OAAO,QAAQ;AAClD,gBAAQ,SAAS;AAAA,UACf,UAAU,OAAO;AAAA;AAAA,UAEjB,IAAI,OAAO;AAAA,UACX,WAAW,OAAO;AAAA,QACpB;AACA,YAAI,OAAO,YAAY;AACrB,kBAAQ,aAAa;AAAA,YACnB,UAAU,OAAO,WAAW;AAAA,YAC5B,WAAW,OAAO,WAAW;AAAA,UAC/B;AAAA,QACF;AAEA,aAAK,0BAA0B,EAAE,IAAI,SAAS,QAAQ,KAAK,CAAC;AAAA,MAC9D;AAOA,eAAS,UAAU,aAAa;AAC9B,kBAAU,QAAQ,SAAS,WAAW;AAAA,MACxC;AAGA,YAAM,mBAAmB,MAAM;AAC7B,qBAAa;AACb,mBAAW,UAAU,yDAAyD;AAAA,MAChF;AAGA,eAAS,yBAAyB;AAChC,qBAAa;AACb,mBAAW,UAAU,+DAA+D;AAAA,MACtF;AAEA,UAAI,iBAAiB;AAKrB,eAAS,eAAe;AAEtB,YAAI,SAAS,eAAe,WAAW;AACrC,2BAAiB;AACjB;AAAA,QACF;AAEA,cAAM,SAAS,SAAS,iBAAiB,QAAQ,WAAW;AAC5D,eAAO,QAAQ,gBAAgB;AAAA,MACjC;AAEA,eAAS,OAAO;AAEd,YAAI,eAAgB,cAAa;AAAA,MACnC;AAGA,UAAI,OAAO,WAAW,eAAe,OAAO,kBAAkB;AAC5D,eAAO,iBAAiB,oBAAoB,MAAM,KAAK;AAAA,MACzD;AAQA,eAAS,iBAAiB,cAAc,oBAAoB;AAC1D,YAAI,OAAO;AACX,YAAI;AACF,iBAAO,mBAAmB,IAAI;AAAA,QAChC,SAAS,SAAS;AAChB,gBAAM,wDAAwD,QAAQ,MAAM,YAAY,CAAC;AAEzF,cAAI,CAAC,WAAW;AAAE,kBAAM;AAAA,UAAS,OAAO;AAAE,kBAAM,OAAO;AAAA,UAAG;AAK1D,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,KAAK,KAAM,MAAK,OAAO;AAC5B,kBAAU,YAAY,IAAI;AAC1B,aAAK,gBAAgB,mBAAmB,KAAK,MAAM,IAAI;AAEvD,YAAI,KAAK,SAAS;AAChB,0BAAgB,KAAK,SAAS,EAAE,aAAa,CAAC;AAAA,QAChD;AAAA,MACF;AAOA,eAAS,mBAAmB,cAAc;AACxC,eAAO,UAAU,YAAY;AAC7B,mBAAW,SAAS,OAAO,KAAK,OAAO,GAAG;AACxC,cAAI,QAAQ,KAAK,MAAM,cAAc;AACnC,mBAAO,QAAQ,KAAK;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAKA,eAAS,gBAAgB;AACvB,eAAO,OAAO,KAAK,SAAS;AAAA,MAC9B;AAMA,eAAS,YAAY,MAAM;AACzB,gBAAQ,QAAQ,IAAI,YAAY;AAChC,eAAO,UAAU,IAAI,KAAK,UAAU,QAAQ,IAAI,CAAC;AAAA,MACnD;AAOA,eAAS,gBAAgB,WAAW,EAAE,aAAa,GAAG;AACpD,YAAI,OAAO,cAAc,UAAU;AACjC,sBAAY,CAAC,SAAS;AAAA,QACxB;AACA,kBAAU,QAAQ,WAAS;AAAE,kBAAQ,MAAM,YAAY,CAAC,IAAI;AAAA,QAAc,CAAC;AAAA,MAC7E;AAMA,eAAS,cAAc,MAAM;AAC3B,cAAM,OAAO,YAAY,IAAI;AAC7B,eAAO,QAAQ,CAAC,KAAK;AAAA,MACvB;AAOA,eAAS,iBAAiB,QAAQ;AAEhC,YAAI,OAAO,uBAAuB,KAAK,CAAC,OAAO,yBAAyB,GAAG;AACzE,iBAAO,yBAAyB,IAAI,CAAC,SAAS;AAC5C,mBAAO,uBAAuB;AAAA,cAC5B,OAAO,OAAO,EAAE,OAAO,KAAK,GAAG,GAAG,IAAI;AAAA,YACxC;AAAA,UACF;AAAA,QACF;AACA,YAAI,OAAO,sBAAsB,KAAK,CAAC,OAAO,wBAAwB,GAAG;AACvE,iBAAO,wBAAwB,IAAI,CAAC,SAAS;AAC3C,mBAAO,sBAAsB;AAAA,cAC3B,OAAO,OAAO,EAAE,OAAO,KAAK,GAAG,GAAG,IAAI;AAAA,YACxC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAKA,eAAS,UAAU,QAAQ;AACzB,yBAAiB,MAAM;AACvB,gBAAQ,KAAK,MAAM;AAAA,MACrB;AAKA,eAAS,aAAa,QAAQ;AAC5B,cAAM,QAAQ,QAAQ,QAAQ,MAAM;AACpC,YAAI,UAAU,IAAI;AAChB,kBAAQ,OAAO,OAAO,CAAC;AAAA,QACzB;AAAA,MACF;AAOA,eAAS,KAAK,OAAO,MAAM;AACzB,cAAM,KAAK;AACX,gBAAQ,QAAQ,SAAS,QAAQ;AAC/B,cAAI,OAAO,EAAE,GAAG;AACd,mBAAO,EAAE,EAAE,IAAI;AAAA,UACjB;AAAA,QACF,CAAC;AAAA,MACH;AAMA,eAAS,wBAAwB,IAAI;AACnC,mBAAW,UAAU,kDAAkD;AACvE,mBAAW,UAAU,kCAAkC;AAEvD,eAAO,iBAAiB,EAAE;AAAA,MAC5B;AAGA,aAAO,OAAO,MAAM;AAAA,QAClB,WAAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAED,WAAK,YAAY,WAAW;AAAE,oBAAY;AAAA,MAAO;AACjD,WAAK,WAAW,WAAW;AAAE,oBAAY;AAAA,MAAM;AAC/C,WAAK,gBAAgB;AAErB,WAAK,QAAQ;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,iBAAW,OAAO,OAAO;AAEvB,YAAI,OAAO,MAAM,GAAG,MAAM,UAAU;AAElC,qBAAW,MAAM,GAAG,CAAC;AAAA,QACvB;AAAA,MACF;AAGA,aAAO,OAAO,MAAM,KAAK;AAEzB,aAAO;AAAA,IACT;AAGA,QAAM,YAAY,KAAK,CAAC,CAAC;AAIzB,cAAU,cAAc,MAAM,KAAK,CAAC,CAAC;AAErC,WAAO,UAAU;AACjB,cAAU,cAAc;AACxB,cAAU,UAAU;AAAA;AAAA;", "names": ["scopeName", "version", "i", "highlight", "result"]}