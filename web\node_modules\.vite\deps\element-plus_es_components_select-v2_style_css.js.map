{"version": 3, "sources": ["../../element-plus/es/components/select-v2/style/css.mjs"], "sourcesContent": ["import '../../base/style/css.mjs';\nimport '../../input/style/css.mjs';\nimport '../../tag/style/css.mjs';\nimport '../../scrollbar/style/css.mjs';\nimport '../../popper/style/css.mjs';\nimport '../../virtual-list/style/css.mjs';\nimport 'element-plus/theme-chalk/el-select-v2.css';\n//# sourceMappingURL=css.mjs.map\n"], "mappings": ";;;;;;;;AAMA,OAAO;", "names": []}