{"version": 3, "sources": ["../../highlight.js/lib/languages/xml.js", "../../highlight.js/lib/languages/bash.js", "../../highlight.js/lib/languages/c.js", "../../highlight.js/lib/languages/cpp.js", "../../highlight.js/lib/languages/csharp.js", "../../highlight.js/lib/languages/css.js", "../../highlight.js/lib/languages/markdown.js", "../../highlight.js/lib/languages/diff.js", "../../highlight.js/lib/languages/ruby.js", "../../highlight.js/lib/languages/go.js", "../../highlight.js/lib/languages/graphql.js", "../../highlight.js/lib/languages/ini.js", "../../highlight.js/lib/languages/java.js", "../../highlight.js/lib/languages/javascript.js", "../../highlight.js/lib/languages/json.js", "../../highlight.js/lib/languages/kotlin.js", "../../highlight.js/lib/languages/less.js", "../../highlight.js/lib/languages/lua.js", "../../highlight.js/lib/languages/makefile.js", "../../highlight.js/lib/languages/perl.js", "../../highlight.js/lib/languages/objectivec.js", "../../highlight.js/lib/languages/php.js", "../../highlight.js/lib/languages/php-template.js", "../../highlight.js/lib/languages/plaintext.js", "../../highlight.js/lib/languages/python.js", "../../highlight.js/lib/languages/python-repl.js", "../../highlight.js/lib/languages/r.js", "../../highlight.js/lib/languages/rust.js", "../../highlight.js/lib/languages/scss.js", "../../highlight.js/lib/languages/shell.js", "../../highlight.js/lib/languages/sql.js", "../../highlight.js/lib/languages/swift.js", "../../highlight.js/lib/languages/yaml.js", "../../highlight.js/lib/languages/typescript.js", "../../highlight.js/lib/languages/vbnet.js", "../../highlight.js/lib/languages/wasm.js", "../../highlight.js/lib/common.js", "../../highlight.js/es/common.js"], "sourcesContent": ["/*\nLanguage: HTML, XML\nWebsite: https://www.w3.org/XML/\nCategory: common, web\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction xml(hljs) {\n  const regex = hljs.regex;\n  // XML names can have the following additional letters: https://www.w3.org/TR/xml/#NT-NameChar\n  // OTHER_NAME_CHARS = /[:\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]/;\n  // Element names start with NAME_START_CHAR followed by optional other Unicode letters, ASCII digits, hyphens, underscores, and periods\n  // const TAG_NAME_RE = regex.concat(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/, regex.optional(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*:/), /[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*/);;\n  // const XML_IDENT_RE = /[A-Z_a-z:\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]+/;\n  // const TAG_NAME_RE = regex.concat(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/, regex.optional(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*:/), /[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*/);\n  // however, to cater for performance and more Unicode support rely simply on the Unicode letter class\n  const TAG_NAME_RE = regex.concat(/[\\p{L}_]/u, regex.optional(/[\\p{L}0-9_.-]*:/u), /[\\p{L}0-9_.-]*/u);\n  const XML_IDENT_RE = /[\\p{L}0-9._:-]+/u;\n  const XML_ENTITIES = {\n    className: 'symbol',\n    begin: /&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/\n  };\n  const XML_META_KEYWORDS = {\n    begin: /\\s/,\n    contains: [\n      {\n        className: 'keyword',\n        begin: /#?[a-z_][a-z1-9_-]+/,\n        illegal: /\\n/\n      }\n    ]\n  };\n  const XML_META_PAR_KEYWORDS = hljs.inherit(XML_META_KEYWORDS, {\n    begin: /\\(/,\n    end: /\\)/\n  });\n  const APOS_META_STRING_MODE = hljs.inherit(hljs.APOS_STRING_MODE, { className: 'string' });\n  const QUOTE_META_STRING_MODE = hljs.inherit(hljs.QUOTE_STRING_MODE, { className: 'string' });\n  const TAG_INTERNALS = {\n    endsWithParent: true,\n    illegal: /</,\n    relevance: 0,\n    contains: [\n      {\n        className: 'attr',\n        begin: XML_IDENT_RE,\n        relevance: 0\n      },\n      {\n        begin: /=\\s*/,\n        relevance: 0,\n        contains: [\n          {\n            className: 'string',\n            endsParent: true,\n            variants: [\n              {\n                begin: /\"/,\n                end: /\"/,\n                contains: [ XML_ENTITIES ]\n              },\n              {\n                begin: /'/,\n                end: /'/,\n                contains: [ XML_ENTITIES ]\n              },\n              { begin: /[^\\s\"'=<>`]+/ }\n            ]\n          }\n        ]\n      }\n    ]\n  };\n  return {\n    name: 'HTML, XML',\n    aliases: [\n      'html',\n      'xhtml',\n      'rss',\n      'atom',\n      'xjb',\n      'xsd',\n      'xsl',\n      'plist',\n      'wsf',\n      'svg'\n    ],\n    case_insensitive: true,\n    unicodeRegex: true,\n    contains: [\n      {\n        className: 'meta',\n        begin: /<![a-z]/,\n        end: />/,\n        relevance: 10,\n        contains: [\n          XML_META_KEYWORDS,\n          QUOTE_META_STRING_MODE,\n          APOS_META_STRING_MODE,\n          XML_META_PAR_KEYWORDS,\n          {\n            begin: /\\[/,\n            end: /\\]/,\n            contains: [\n              {\n                className: 'meta',\n                begin: /<![a-z]/,\n                end: />/,\n                contains: [\n                  XML_META_KEYWORDS,\n                  XML_META_PAR_KEYWORDS,\n                  QUOTE_META_STRING_MODE,\n                  APOS_META_STRING_MODE\n                ]\n              }\n            ]\n          }\n        ]\n      },\n      hljs.COMMENT(\n        /<!--/,\n        /-->/,\n        { relevance: 10 }\n      ),\n      {\n        begin: /<!\\[CDATA\\[/,\n        end: /\\]\\]>/,\n        relevance: 10\n      },\n      XML_ENTITIES,\n      // xml processing instructions\n      {\n        className: 'meta',\n        end: /\\?>/,\n        variants: [\n          {\n            begin: /<\\?xml/,\n            relevance: 10,\n            contains: [\n              QUOTE_META_STRING_MODE\n            ]\n          },\n          {\n            begin: /<\\?[a-z][a-z0-9]+/,\n          }\n        ]\n\n      },\n      {\n        className: 'tag',\n        /*\n        The lookahead pattern (?=...) ensures that 'begin' only matches\n        '<style' as a single word, followed by a whitespace or an\n        ending bracket.\n        */\n        begin: /<style(?=\\s|>)/,\n        end: />/,\n        keywords: { name: 'style' },\n        contains: [ TAG_INTERNALS ],\n        starts: {\n          end: /<\\/style>/,\n          returnEnd: true,\n          subLanguage: [\n            'css',\n            'xml'\n          ]\n        }\n      },\n      {\n        className: 'tag',\n        // See the comment in the <style tag about the lookahead pattern\n        begin: /<script(?=\\s|>)/,\n        end: />/,\n        keywords: { name: 'script' },\n        contains: [ TAG_INTERNALS ],\n        starts: {\n          end: /<\\/script>/,\n          returnEnd: true,\n          subLanguage: [\n            'javascript',\n            'handlebars',\n            'xml'\n          ]\n        }\n      },\n      // we need this for now for jSX\n      {\n        className: 'tag',\n        begin: /<>|<\\/>/\n      },\n      // open tag\n      {\n        className: 'tag',\n        begin: regex.concat(\n          /</,\n          regex.lookahead(regex.concat(\n            TAG_NAME_RE,\n            // <tag/>\n            // <tag>\n            // <tag ...\n            regex.either(/\\/>/, />/, /\\s/)\n          ))\n        ),\n        end: /\\/?>/,\n        contains: [\n          {\n            className: 'name',\n            begin: TAG_NAME_RE,\n            relevance: 0,\n            starts: TAG_INTERNALS\n          }\n        ]\n      },\n      // close tag\n      {\n        className: 'tag',\n        begin: regex.concat(\n          /<\\//,\n          regex.lookahead(regex.concat(\n            TAG_NAME_RE, />/\n          ))\n        ),\n        contains: [\n          {\n            className: 'name',\n            begin: TAG_NAME_RE,\n            relevance: 0\n          },\n          {\n            begin: />/,\n            relevance: 0,\n            endsParent: true\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = xml;\n", "/*\nLanguage: Bash\nAuthor: vah <vah<PERSON><EMAIL>>\nContributrors: <PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/bash/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction bash(hljs) {\n  const regex = hljs.regex;\n  const VAR = {};\n  const BRACED_VAR = {\n    begin: /\\$\\{/,\n    end: /\\}/,\n    contains: [\n      \"self\",\n      {\n        begin: /:-/,\n        contains: [ VAR ]\n      } // default values\n    ]\n  };\n  Object.assign(VAR, {\n    className: 'variable',\n    variants: [\n      { begin: regex.concat(/\\$[\\w\\d#@][\\w\\d_]*/,\n        // negative look-ahead tries to avoid matching patterns that are not\n        // Perl at all like $ident$, @ident@, etc.\n        `(?![\\\\w\\\\d])(?![$])`) },\n      BRACED_VAR\n    ]\n  });\n\n  const SUBST = {\n    className: 'subst',\n    begin: /\\$\\(/,\n    end: /\\)/,\n    contains: [ hljs.BACKSLASH_ESCAPE ]\n  };\n  const HERE_DOC = {\n    begin: /<<-?\\s*(?=\\w+)/,\n    starts: { contains: [\n      hljs.END_SAME_AS_BEGIN({\n        begin: /(\\w+)/,\n        end: /(\\w+)/,\n        className: 'string'\n      })\n    ] }\n  };\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VAR,\n      SUBST\n    ]\n  };\n  SUBST.contains.push(QUOTE_STRING);\n  const ESCAPED_QUOTE = {\n    match: /\\\\\"/\n  };\n  const APOS_STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/\n  };\n  const ESCAPED_APOS = {\n    match: /\\\\'/\n  };\n  const ARITHMETIC = {\n    begin: /\\$?\\(\\(/,\n    end: /\\)\\)/,\n    contains: [\n      {\n        begin: /\\d+#[0-9a-f]+/,\n        className: \"number\"\n      },\n      hljs.NUMBER_MODE,\n      VAR\n    ]\n  };\n  const SH_LIKE_SHELLS = [\n    \"fish\",\n    \"bash\",\n    \"zsh\",\n    \"sh\",\n    \"csh\",\n    \"ksh\",\n    \"tcsh\",\n    \"dash\",\n    \"scsh\",\n  ];\n  const KNOWN_SHEBANG = hljs.SHEBANG({\n    binary: `(${SH_LIKE_SHELLS.join(\"|\")})`,\n    relevance: 10\n  });\n  const FUNCTION = {\n    className: 'function',\n    begin: /\\w[\\w\\d_]*\\s*\\(\\s*\\)\\s*\\{/,\n    returnBegin: true,\n    contains: [ hljs.inherit(hljs.TITLE_MODE, { begin: /\\w[\\w\\d_]*/ }) ],\n    relevance: 0\n  };\n\n  const KEYWORDS = [\n    \"if\",\n    \"then\",\n    \"else\",\n    \"elif\",\n    \"fi\",\n    \"for\",\n    \"while\",\n    \"until\",\n    \"in\",\n    \"do\",\n    \"done\",\n    \"case\",\n    \"esac\",\n    \"function\",\n    \"select\"\n  ];\n\n  const LITERALS = [\n    \"true\",\n    \"false\"\n  ];\n\n  // to consume paths to prevent keyword matches inside them\n  const PATH_MODE = { match: /(\\/[a-z._-]+)+/ };\n\n  // http://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n  const SHELL_BUILT_INS = [\n    \"break\",\n    \"cd\",\n    \"continue\",\n    \"eval\",\n    \"exec\",\n    \"exit\",\n    \"export\",\n    \"getopts\",\n    \"hash\",\n    \"pwd\",\n    \"readonly\",\n    \"return\",\n    \"shift\",\n    \"test\",\n    \"times\",\n    \"trap\",\n    \"umask\",\n    \"unset\"\n  ];\n\n  const BASH_BUILT_INS = [\n    \"alias\",\n    \"bind\",\n    \"builtin\",\n    \"caller\",\n    \"command\",\n    \"declare\",\n    \"echo\",\n    \"enable\",\n    \"help\",\n    \"let\",\n    \"local\",\n    \"logout\",\n    \"mapfile\",\n    \"printf\",\n    \"read\",\n    \"readarray\",\n    \"source\",\n    \"type\",\n    \"typeset\",\n    \"ulimit\",\n    \"unalias\"\n  ];\n\n  const ZSH_BUILT_INS = [\n    \"autoload\",\n    \"bg\",\n    \"bindkey\",\n    \"bye\",\n    \"cap\",\n    \"chdir\",\n    \"clone\",\n    \"comparguments\",\n    \"compcall\",\n    \"compctl\",\n    \"compdescribe\",\n    \"compfiles\",\n    \"compgroups\",\n    \"compquote\",\n    \"comptags\",\n    \"comptry\",\n    \"compvalues\",\n    \"dirs\",\n    \"disable\",\n    \"disown\",\n    \"echotc\",\n    \"echoti\",\n    \"emulate\",\n    \"fc\",\n    \"fg\",\n    \"float\",\n    \"functions\",\n    \"getcap\",\n    \"getln\",\n    \"history\",\n    \"integer\",\n    \"jobs\",\n    \"kill\",\n    \"limit\",\n    \"log\",\n    \"noglob\",\n    \"popd\",\n    \"print\",\n    \"pushd\",\n    \"pushln\",\n    \"rehash\",\n    \"sched\",\n    \"setcap\",\n    \"setopt\",\n    \"stat\",\n    \"suspend\",\n    \"ttyctl\",\n    \"unfunction\",\n    \"unhash\",\n    \"unlimit\",\n    \"unsetopt\",\n    \"vared\",\n    \"wait\",\n    \"whence\",\n    \"where\",\n    \"which\",\n    \"zcompile\",\n    \"zformat\",\n    \"zftp\",\n    \"zle\",\n    \"zmodload\",\n    \"zparseopts\",\n    \"zprof\",\n    \"zpty\",\n    \"zregexparse\",\n    \"zsocket\",\n    \"zstyle\",\n    \"ztcp\"\n  ];\n\n  const GNU_CORE_UTILS = [\n    \"chcon\",\n    \"chgrp\",\n    \"chown\",\n    \"chmod\",\n    \"cp\",\n    \"dd\",\n    \"df\",\n    \"dir\",\n    \"dircolors\",\n    \"ln\",\n    \"ls\",\n    \"mkdir\",\n    \"mkfifo\",\n    \"mknod\",\n    \"mktemp\",\n    \"mv\",\n    \"realpath\",\n    \"rm\",\n    \"rmdir\",\n    \"shred\",\n    \"sync\",\n    \"touch\",\n    \"truncate\",\n    \"vdir\",\n    \"b2sum\",\n    \"base32\",\n    \"base64\",\n    \"cat\",\n    \"cksum\",\n    \"comm\",\n    \"csplit\",\n    \"cut\",\n    \"expand\",\n    \"fmt\",\n    \"fold\",\n    \"head\",\n    \"join\",\n    \"md5sum\",\n    \"nl\",\n    \"numfmt\",\n    \"od\",\n    \"paste\",\n    \"ptx\",\n    \"pr\",\n    \"sha1sum\",\n    \"sha224sum\",\n    \"sha256sum\",\n    \"sha384sum\",\n    \"sha512sum\",\n    \"shuf\",\n    \"sort\",\n    \"split\",\n    \"sum\",\n    \"tac\",\n    \"tail\",\n    \"tr\",\n    \"tsort\",\n    \"unexpand\",\n    \"uniq\",\n    \"wc\",\n    \"arch\",\n    \"basename\",\n    \"chroot\",\n    \"date\",\n    \"dirname\",\n    \"du\",\n    \"echo\",\n    \"env\",\n    \"expr\",\n    \"factor\",\n    // \"false\", // keyword literal already\n    \"groups\",\n    \"hostid\",\n    \"id\",\n    \"link\",\n    \"logname\",\n    \"nice\",\n    \"nohup\",\n    \"nproc\",\n    \"pathchk\",\n    \"pinky\",\n    \"printenv\",\n    \"printf\",\n    \"pwd\",\n    \"readlink\",\n    \"runcon\",\n    \"seq\",\n    \"sleep\",\n    \"stat\",\n    \"stdbuf\",\n    \"stty\",\n    \"tee\",\n    \"test\",\n    \"timeout\",\n    // \"true\", // keyword literal already\n    \"tty\",\n    \"uname\",\n    \"unlink\",\n    \"uptime\",\n    \"users\",\n    \"who\",\n    \"whoami\",\n    \"yes\"\n  ];\n\n  return {\n    name: 'Bash',\n    aliases: [ 'sh' ],\n    keywords: {\n      $pattern: /\\b[a-z][a-z0-9._-]+\\b/,\n      keyword: KEYWORDS,\n      literal: LITERALS,\n      built_in: [\n        ...SHELL_BUILT_INS,\n        ...BASH_BUILT_INS,\n        // Shell modifiers\n        \"set\",\n        \"shopt\",\n        ...ZSH_BUILT_INS,\n        ...GNU_CORE_UTILS\n      ]\n    },\n    contains: [\n      KNOWN_SHEBANG, // to catch known shells and boost relevancy\n      hljs.SHEBANG(), // to catch unknown shells but still highlight the shebang\n      FUNCTION,\n      ARITHMETIC,\n      hljs.HASH_COMMENT_MODE,\n      HERE_DOC,\n      PATH_MODE,\n      QUOTE_STRING,\n      ESCAPED_QUOTE,\n      APOS_STRING,\n      ESCAPED_APOS,\n      VAR\n    ]\n  };\n}\n\nmodule.exports = bash;\n", "/*\nLanguage: C\nCategory: common, system\nWebsite: https://en.wikipedia.org/wiki/C_(programming_language)\n*/\n\n/** @type LanguageFn */\nfunction c(hljs) {\n  const regex = hljs.regex;\n  // added for historic reasons because `hljs.C_LINE_COMMENT_MODE` does\n  // not include such support nor can we be sure all the grammars depending\n  // on it would desire this behavior\n  const C_LINE_COMMENT_MODE = hljs.COMMENT('//', '$', { contains: [ { begin: /\\\\\\n/ } ] });\n  const DECLTYPE_AUTO_RE = 'decltype\\\\(auto\\\\)';\n  const NAMESPACE_RE = '[a-zA-Z_]\\\\w*::';\n  const TEMPLATE_ARGUMENT_RE = '<[^<>]+>';\n  const FUNCTION_TYPE_RE = '('\n    + DECLTYPE_AUTO_RE + '|'\n    + regex.optional(NAMESPACE_RE)\n    + '[a-zA-Z_]\\\\w*' + regex.optional(TEMPLATE_ARGUMENT_RE)\n  + ')';\n\n\n  const TYPES = {\n    className: 'type',\n    variants: [\n      { begin: '\\\\b[a-z\\\\d_]*_t\\\\b' },\n      { match: /\\batomic_[a-z]{3,6}\\b/ }\n    ]\n\n  };\n\n  // https://en.cppreference.com/w/cpp/language/escape\n  // \\\\ \\x \\xFF \\u2837 \\u00323747 \\374\n  const CHARACTER_ESCAPES = '\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\\\S)';\n  const STRINGS = {\n    className: 'string',\n    variants: [\n      {\n        begin: '(u8?|U|L)?\"',\n        end: '\"',\n        illegal: '\\\\n',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: '(u8?|U|L)?\\'(' + CHARACTER_ESCAPES + \"|.)\",\n        end: '\\'',\n        illegal: '.'\n      },\n      hljs.END_SAME_AS_BEGIN({\n        begin: /(?:u8?|U|L)?R\"([^()\\\\ ]{0,16})\\(/,\n        end: /\\)([^()\\\\ ]{0,16})\"/\n      })\n    ]\n  };\n\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      { begin: '\\\\b(0b[01\\']+)' },\n      { begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)((ll|LL|l|L)(u|U)?|(u|U)(ll|LL|l|L)?|f|F|b|B)' },\n      { begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)' }\n    ],\n    relevance: 0\n  };\n\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: { keyword:\n        'if else elif endif define undef warning error line '\n        + 'pragma _Pragma ifdef ifndef include' },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      hljs.inherit(STRINGS, { className: 'string' }),\n      {\n        className: 'string',\n        begin: /<.*?>/\n      },\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n\n  const TITLE_MODE = {\n    className: 'title',\n    begin: regex.optional(NAMESPACE_RE) + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  const FUNCTION_TITLE = regex.optional(NAMESPACE_RE) + hljs.IDENT_RE + '\\\\s*\\\\(';\n\n  const C_KEYWORDS = [\n    \"asm\",\n    \"auto\",\n    \"break\",\n    \"case\",\n    \"continue\",\n    \"default\",\n    \"do\",\n    \"else\",\n    \"enum\",\n    \"extern\",\n    \"for\",\n    \"fortran\",\n    \"goto\",\n    \"if\",\n    \"inline\",\n    \"register\",\n    \"restrict\",\n    \"return\",\n    \"sizeof\",\n    \"struct\",\n    \"switch\",\n    \"typedef\",\n    \"union\",\n    \"volatile\",\n    \"while\",\n    \"_Alignas\",\n    \"_Alignof\",\n    \"_Atomic\",\n    \"_Generic\",\n    \"_Noreturn\",\n    \"_Static_assert\",\n    \"_Thread_local\",\n    // aliases\n    \"alignas\",\n    \"alignof\",\n    \"noreturn\",\n    \"static_assert\",\n    \"thread_local\",\n    // not a C keyword but is, for all intents and purposes, treated exactly like one.\n    \"_Pragma\"\n  ];\n\n  const C_TYPES = [\n    \"float\",\n    \"double\",\n    \"signed\",\n    \"unsigned\",\n    \"int\",\n    \"short\",\n    \"long\",\n    \"char\",\n    \"void\",\n    \"_Bool\",\n    \"_Complex\",\n    \"_Imaginary\",\n    \"_Decimal32\",\n    \"_Decimal64\",\n    \"_Decimal128\",\n    // modifiers\n    \"const\",\n    \"static\",\n    // aliases\n    \"complex\",\n    \"bool\",\n    \"imaginary\"\n  ];\n\n  const KEYWORDS = {\n    keyword: C_KEYWORDS,\n    type: C_TYPES,\n    literal: 'true false NULL',\n    // TODO: apply hinting work similar to what was done in cpp.js\n    built_in: 'std string wstring cin cout cerr clog stdin stdout stderr stringstream istringstream ostringstream '\n      + 'auto_ptr deque list queue stack vector map set pair bitset multiset multimap unordered_set '\n      + 'unordered_map unordered_multiset unordered_multimap priority_queue make_pair array shared_ptr abort terminate abs acos '\n      + 'asin atan2 atan calloc ceil cosh cos exit exp fabs floor fmod fprintf fputs free frexp '\n      + 'fscanf future isalnum isalpha iscntrl isdigit isgraph islower isprint ispunct isspace isupper '\n      + 'isxdigit tolower toupper labs ldexp log10 log malloc realloc memchr memcmp memcpy memset modf pow '\n      + 'printf putchar puts scanf sinh sin snprintf sprintf sqrt sscanf strcat strchr strcmp '\n      + 'strcpy strcspn strlen strncat strncmp strncpy strpbrk strrchr strspn strstr tanh tan '\n      + 'vfprintf vprintf vsprintf endl initializer_list unique_ptr',\n  };\n\n  const EXPRESSION_CONTAINS = [\n    PREPROCESSOR,\n    TYPES,\n    C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    NUMBERS,\n    STRINGS\n  ];\n\n  const EXPRESSION_CONTEXT = {\n    // This mode covers expression context where we can't expect a function\n    // definition and shouldn't highlight anything that looks like one:\n    // `return some()`, `else if()`, `(x*sum(1, 2))`\n    variants: [\n      {\n        begin: /=/,\n        end: /;/\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/\n      },\n      {\n        beginKeywords: 'new throw return else',\n        end: /;/\n      }\n    ],\n    keywords: KEYWORDS,\n    contains: EXPRESSION_CONTAINS.concat([\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        contains: EXPRESSION_CONTAINS.concat([ 'self' ]),\n        relevance: 0\n      }\n    ]),\n    relevance: 0\n  };\n\n  const FUNCTION_DECLARATION = {\n    begin: '(' + FUNCTION_TYPE_RE + '[\\\\*&\\\\s]+)+' + FUNCTION_TITLE,\n    returnBegin: true,\n    end: /[{;=]/,\n    excludeEnd: true,\n    keywords: KEYWORDS,\n    illegal: /[^\\w\\s\\*&:<>.]/,\n    contains: [\n      { // to prevent it from being confused as the function title\n        begin: DECLTYPE_AUTO_RE,\n        keywords: KEYWORDS,\n        relevance: 0\n      },\n      {\n        begin: FUNCTION_TITLE,\n        returnBegin: true,\n        contains: [ hljs.inherit(TITLE_MODE, { className: \"title.function\" }) ],\n        relevance: 0\n      },\n      // allow for multiple declarations, e.g.:\n      // extern void f(int), g(char);\n      {\n        relevance: 0,\n        match: /,/\n      },\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        relevance: 0,\n        contains: [\n          C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          STRINGS,\n          NUMBERS,\n          TYPES,\n          // Count matching parentheses.\n          {\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              'self',\n              C_LINE_COMMENT_MODE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRINGS,\n              NUMBERS,\n              TYPES\n            ]\n          }\n        ]\n      },\n      TYPES,\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      PREPROCESSOR\n    ]\n  };\n\n  return {\n    name: \"C\",\n    aliases: [ 'h' ],\n    keywords: KEYWORDS,\n    // Until differentiations are added between `c` and `cpp`, `c` will\n    // not be auto-detected to avoid auto-detect conflicts between C and C++\n    disableAutodetect: true,\n    illegal: '</',\n    contains: [].concat(\n      EXPRESSION_CONTEXT,\n      FUNCTION_DECLARATION,\n      EXPRESSION_CONTAINS,\n      [\n        PREPROCESSOR,\n        {\n          begin: hljs.IDENT_RE + '::',\n          keywords: KEYWORDS\n        },\n        {\n          className: 'class',\n          beginKeywords: 'enum class struct union',\n          end: /[{;:<>=]/,\n          contains: [\n            { beginKeywords: \"final class struct\" },\n            hljs.TITLE_MODE\n          ]\n        }\n      ]),\n    exports: {\n      preprocessor: PREPROCESSOR,\n      strings: STRINGS,\n      keywords: KEYWORDS\n    }\n  };\n}\n\nmodule.exports = c;\n", "/*\nLanguage: C++\nCategory: common, system\nWebsite: https://isocpp.org\n*/\n\n/** @type LanguageFn */\nfunction cpp(hljs) {\n  const regex = hljs.regex;\n  // added for historic reasons because `hljs.C_LINE_COMMENT_MODE` does\n  // not include such support nor can we be sure all the grammars depending\n  // on it would desire this behavior\n  const C_LINE_COMMENT_MODE = hljs.COMMENT('//', '$', { contains: [ { begin: /\\\\\\n/ } ] });\n  const DECLTYPE_AUTO_RE = 'decltype\\\\(auto\\\\)';\n  const NAMESPACE_RE = '[a-zA-Z_]\\\\w*::';\n  const TEMPLATE_ARGUMENT_RE = '<[^<>]+>';\n  const FUNCTION_TYPE_RE = '(?!struct)('\n    + DECLTYPE_AUTO_RE + '|'\n    + regex.optional(NAMESPACE_RE)\n    + '[a-zA-Z_]\\\\w*' + regex.optional(TEMPLATE_ARGUMENT_RE)\n  + ')';\n\n  const CPP_PRIMITIVE_TYPES = {\n    className: 'type',\n    begin: '\\\\b[a-z\\\\d_]*_t\\\\b'\n  };\n\n  // https://en.cppreference.com/w/cpp/language/escape\n  // \\\\ \\x \\xFF \\u2837 \\u00323747 \\374\n  const CHARACTER_ESCAPES = '\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\\\S)';\n  const STRINGS = {\n    className: 'string',\n    variants: [\n      {\n        begin: '(u8?|U|L)?\"',\n        end: '\"',\n        illegal: '\\\\n',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: '(u8?|U|L)?\\'(' + CHARACTER_ESCAPES + '|.)',\n        end: '\\'',\n        illegal: '.'\n      },\n      hljs.END_SAME_AS_BEGIN({\n        begin: /(?:u8?|U|L)?R\"([^()\\\\ ]{0,16})\\(/,\n        end: /\\)([^()\\\\ ]{0,16})\"/\n      })\n    ]\n  };\n\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      { begin: '\\\\b(0b[01\\']+)' },\n      { begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)((ll|LL|l|L)(u|U)?|(u|U)(ll|LL|l|L)?|f|F|b|B)' },\n      { begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)' }\n    ],\n    relevance: 0\n  };\n\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: { keyword:\n        'if else elif endif define undef warning error line '\n        + 'pragma _Pragma ifdef ifndef include' },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      hljs.inherit(STRINGS, { className: 'string' }),\n      {\n        className: 'string',\n        begin: /<.*?>/\n      },\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n\n  const TITLE_MODE = {\n    className: 'title',\n    begin: regex.optional(NAMESPACE_RE) + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  const FUNCTION_TITLE = regex.optional(NAMESPACE_RE) + hljs.IDENT_RE + '\\\\s*\\\\(';\n\n  // https://en.cppreference.com/w/cpp/keyword\n  const RESERVED_KEYWORDS = [\n    'alignas',\n    'alignof',\n    'and',\n    'and_eq',\n    'asm',\n    'atomic_cancel',\n    'atomic_commit',\n    'atomic_noexcept',\n    'auto',\n    'bitand',\n    'bitor',\n    'break',\n    'case',\n    'catch',\n    'class',\n    'co_await',\n    'co_return',\n    'co_yield',\n    'compl',\n    'concept',\n    'const_cast|10',\n    'consteval',\n    'constexpr',\n    'constinit',\n    'continue',\n    'decltype',\n    'default',\n    'delete',\n    'do',\n    'dynamic_cast|10',\n    'else',\n    'enum',\n    'explicit',\n    'export',\n    'extern',\n    'false',\n    'final',\n    'for',\n    'friend',\n    'goto',\n    'if',\n    'import',\n    'inline',\n    'module',\n    'mutable',\n    'namespace',\n    'new',\n    'noexcept',\n    'not',\n    'not_eq',\n    'nullptr',\n    'operator',\n    'or',\n    'or_eq',\n    'override',\n    'private',\n    'protected',\n    'public',\n    'reflexpr',\n    'register',\n    'reinterpret_cast|10',\n    'requires',\n    'return',\n    'sizeof',\n    'static_assert',\n    'static_cast|10',\n    'struct',\n    'switch',\n    'synchronized',\n    'template',\n    'this',\n    'thread_local',\n    'throw',\n    'transaction_safe',\n    'transaction_safe_dynamic',\n    'true',\n    'try',\n    'typedef',\n    'typeid',\n    'typename',\n    'union',\n    'using',\n    'virtual',\n    'volatile',\n    'while',\n    'xor',\n    'xor_eq'\n  ];\n\n  // https://en.cppreference.com/w/cpp/keyword\n  const RESERVED_TYPES = [\n    'bool',\n    'char',\n    'char16_t',\n    'char32_t',\n    'char8_t',\n    'double',\n    'float',\n    'int',\n    'long',\n    'short',\n    'void',\n    'wchar_t',\n    'unsigned',\n    'signed',\n    'const',\n    'static'\n  ];\n\n  const TYPE_HINTS = [\n    'any',\n    'auto_ptr',\n    'barrier',\n    'binary_semaphore',\n    'bitset',\n    'complex',\n    'condition_variable',\n    'condition_variable_any',\n    'counting_semaphore',\n    'deque',\n    'false_type',\n    'future',\n    'imaginary',\n    'initializer_list',\n    'istringstream',\n    'jthread',\n    'latch',\n    'lock_guard',\n    'multimap',\n    'multiset',\n    'mutex',\n    'optional',\n    'ostringstream',\n    'packaged_task',\n    'pair',\n    'promise',\n    'priority_queue',\n    'queue',\n    'recursive_mutex',\n    'recursive_timed_mutex',\n    'scoped_lock',\n    'set',\n    'shared_future',\n    'shared_lock',\n    'shared_mutex',\n    'shared_timed_mutex',\n    'shared_ptr',\n    'stack',\n    'string_view',\n    'stringstream',\n    'timed_mutex',\n    'thread',\n    'true_type',\n    'tuple',\n    'unique_lock',\n    'unique_ptr',\n    'unordered_map',\n    'unordered_multimap',\n    'unordered_multiset',\n    'unordered_set',\n    'variant',\n    'vector',\n    'weak_ptr',\n    'wstring',\n    'wstring_view'\n  ];\n\n  const FUNCTION_HINTS = [\n    'abort',\n    'abs',\n    'acos',\n    'apply',\n    'as_const',\n    'asin',\n    'atan',\n    'atan2',\n    'calloc',\n    'ceil',\n    'cerr',\n    'cin',\n    'clog',\n    'cos',\n    'cosh',\n    'cout',\n    'declval',\n    'endl',\n    'exchange',\n    'exit',\n    'exp',\n    'fabs',\n    'floor',\n    'fmod',\n    'forward',\n    'fprintf',\n    'fputs',\n    'free',\n    'frexp',\n    'fscanf',\n    'future',\n    'invoke',\n    'isalnum',\n    'isalpha',\n    'iscntrl',\n    'isdigit',\n    'isgraph',\n    'islower',\n    'isprint',\n    'ispunct',\n    'isspace',\n    'isupper',\n    'isxdigit',\n    'labs',\n    'launder',\n    'ldexp',\n    'log',\n    'log10',\n    'make_pair',\n    'make_shared',\n    'make_shared_for_overwrite',\n    'make_tuple',\n    'make_unique',\n    'malloc',\n    'memchr',\n    'memcmp',\n    'memcpy',\n    'memset',\n    'modf',\n    'move',\n    'pow',\n    'printf',\n    'putchar',\n    'puts',\n    'realloc',\n    'scanf',\n    'sin',\n    'sinh',\n    'snprintf',\n    'sprintf',\n    'sqrt',\n    'sscanf',\n    'std',\n    'stderr',\n    'stdin',\n    'stdout',\n    'strcat',\n    'strchr',\n    'strcmp',\n    'strcpy',\n    'strcspn',\n    'strlen',\n    'strncat',\n    'strncmp',\n    'strncpy',\n    'strpbrk',\n    'strrchr',\n    'strspn',\n    'strstr',\n    'swap',\n    'tan',\n    'tanh',\n    'terminate',\n    'to_underlying',\n    'tolower',\n    'toupper',\n    'vfprintf',\n    'visit',\n    'vprintf',\n    'vsprintf'\n  ];\n\n  const LITERALS = [\n    'NULL',\n    'false',\n    'nullopt',\n    'nullptr',\n    'true'\n  ];\n\n  // https://en.cppreference.com/w/cpp/keyword\n  const BUILT_IN = [ '_Pragma' ];\n\n  const CPP_KEYWORDS = {\n    type: RESERVED_TYPES,\n    keyword: RESERVED_KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_IN,\n    _type_hints: TYPE_HINTS\n  };\n\n  const FUNCTION_DISPATCH = {\n    className: 'function.dispatch',\n    relevance: 0,\n    keywords: {\n      // Only for relevance, not highlighting.\n      _hint: FUNCTION_HINTS },\n    begin: regex.concat(\n      /\\b/,\n      /(?!decltype)/,\n      /(?!if)/,\n      /(?!for)/,\n      /(?!switch)/,\n      /(?!while)/,\n      hljs.IDENT_RE,\n      regex.lookahead(/(<[^<>]+>|)\\s*\\(/))\n  };\n\n  const EXPRESSION_CONTAINS = [\n    FUNCTION_DISPATCH,\n    PREPROCESSOR,\n    CPP_PRIMITIVE_TYPES,\n    C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    NUMBERS,\n    STRINGS\n  ];\n\n  const EXPRESSION_CONTEXT = {\n    // This mode covers expression context where we can't expect a function\n    // definition and shouldn't highlight anything that looks like one:\n    // `return some()`, `else if()`, `(x*sum(1, 2))`\n    variants: [\n      {\n        begin: /=/,\n        end: /;/\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/\n      },\n      {\n        beginKeywords: 'new throw return else',\n        end: /;/\n      }\n    ],\n    keywords: CPP_KEYWORDS,\n    contains: EXPRESSION_CONTAINS.concat([\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: CPP_KEYWORDS,\n        contains: EXPRESSION_CONTAINS.concat([ 'self' ]),\n        relevance: 0\n      }\n    ]),\n    relevance: 0\n  };\n\n  const FUNCTION_DECLARATION = {\n    className: 'function',\n    begin: '(' + FUNCTION_TYPE_RE + '[\\\\*&\\\\s]+)+' + FUNCTION_TITLE,\n    returnBegin: true,\n    end: /[{;=]/,\n    excludeEnd: true,\n    keywords: CPP_KEYWORDS,\n    illegal: /[^\\w\\s\\*&:<>.]/,\n    contains: [\n      { // to prevent it from being confused as the function title\n        begin: DECLTYPE_AUTO_RE,\n        keywords: CPP_KEYWORDS,\n        relevance: 0\n      },\n      {\n        begin: FUNCTION_TITLE,\n        returnBegin: true,\n        contains: [ TITLE_MODE ],\n        relevance: 0\n      },\n      // needed because we do not have look-behind on the below rule\n      // to prevent it from grabbing the final : in a :: pair\n      {\n        begin: /::/,\n        relevance: 0\n      },\n      // initializers\n      {\n        begin: /:/,\n        endsWithParent: true,\n        contains: [\n          STRINGS,\n          NUMBERS\n        ]\n      },\n      // allow for multiple declarations, e.g.:\n      // extern void f(int), g(char);\n      {\n        relevance: 0,\n        match: /,/\n      },\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: CPP_KEYWORDS,\n        relevance: 0,\n        contains: [\n          C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          STRINGS,\n          NUMBERS,\n          CPP_PRIMITIVE_TYPES,\n          // Count matching parentheses.\n          {\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: CPP_KEYWORDS,\n            relevance: 0,\n            contains: [\n              'self',\n              C_LINE_COMMENT_MODE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRINGS,\n              NUMBERS,\n              CPP_PRIMITIVE_TYPES\n            ]\n          }\n        ]\n      },\n      CPP_PRIMITIVE_TYPES,\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      PREPROCESSOR\n    ]\n  };\n\n  return {\n    name: 'C++',\n    aliases: [\n      'cc',\n      'c++',\n      'h++',\n      'hpp',\n      'hh',\n      'hxx',\n      'cxx'\n    ],\n    keywords: CPP_KEYWORDS,\n    illegal: '</',\n    classNameAliases: { 'function.dispatch': 'built_in' },\n    contains: [].concat(\n      EXPRESSION_CONTEXT,\n      FUNCTION_DECLARATION,\n      FUNCTION_DISPATCH,\n      EXPRESSION_CONTAINS,\n      [\n        PREPROCESSOR,\n        { // containers: ie, `vector <int> rooms (9);`\n          begin: '\\\\b(deque|list|queue|priority_queue|pair|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array|tuple|optional|variant|function)\\\\s*<(?!<)',\n          end: '>',\n          keywords: CPP_KEYWORDS,\n          contains: [\n            'self',\n            CPP_PRIMITIVE_TYPES\n          ]\n        },\n        {\n          begin: hljs.IDENT_RE + '::',\n          keywords: CPP_KEYWORDS\n        },\n        {\n          match: [\n            // extra complexity to deal with `enum class` and `enum struct`\n            /\\b(?:enum(?:\\s+(?:class|struct))?|class|struct|union)/,\n            /\\s+/,\n            /\\w+/\n          ],\n          className: {\n            1: 'keyword',\n            3: 'title.class'\n          }\n        }\n      ])\n  };\n}\n\nmodule.exports = cpp;\n", "/*\nLanguage: C#\nAuthor: <PERSON> <<EMAIL>>\nContributor: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://docs.microsoft.com/dotnet/csharp/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction csharp(hljs) {\n  const BUILT_IN_KEYWORDS = [\n    'bool',\n    'byte',\n    'char',\n    'decimal',\n    'delegate',\n    'double',\n    'dynamic',\n    'enum',\n    'float',\n    'int',\n    'long',\n    'nint',\n    'nuint',\n    'object',\n    'sbyte',\n    'short',\n    'string',\n    'ulong',\n    'uint',\n    'ushort'\n  ];\n  const FUNCTION_MODIFIERS = [\n    'public',\n    'private',\n    'protected',\n    'static',\n    'internal',\n    'protected',\n    'abstract',\n    'async',\n    'extern',\n    'override',\n    'unsafe',\n    'virtual',\n    'new',\n    'sealed',\n    'partial'\n  ];\n  const LITERAL_KEYWORDS = [\n    'default',\n    'false',\n    'null',\n    'true'\n  ];\n  const NORMAL_KEYWORDS = [\n    'abstract',\n    'as',\n    'base',\n    'break',\n    'case',\n    'catch',\n    'class',\n    'const',\n    'continue',\n    'do',\n    'else',\n    'event',\n    'explicit',\n    'extern',\n    'finally',\n    'fixed',\n    'for',\n    'foreach',\n    'goto',\n    'if',\n    'implicit',\n    'in',\n    'interface',\n    'internal',\n    'is',\n    'lock',\n    'namespace',\n    'new',\n    'operator',\n    'out',\n    'override',\n    'params',\n    'private',\n    'protected',\n    'public',\n    'readonly',\n    'record',\n    'ref',\n    'return',\n    'scoped',\n    'sealed',\n    'sizeof',\n    'stackalloc',\n    'static',\n    'struct',\n    'switch',\n    'this',\n    'throw',\n    'try',\n    'typeof',\n    'unchecked',\n    'unsafe',\n    'using',\n    'virtual',\n    'void',\n    'volatile',\n    'while'\n  ];\n  const CONTEXTUAL_KEYWORDS = [\n    'add',\n    'alias',\n    'and',\n    'ascending',\n    'async',\n    'await',\n    'by',\n    'descending',\n    'equals',\n    'from',\n    'get',\n    'global',\n    'group',\n    'init',\n    'into',\n    'join',\n    'let',\n    'nameof',\n    'not',\n    'notnull',\n    'on',\n    'or',\n    'orderby',\n    'partial',\n    'remove',\n    'select',\n    'set',\n    'unmanaged',\n    'value|0',\n    'var',\n    'when',\n    'where',\n    'with',\n    'yield'\n  ];\n\n  const KEYWORDS = {\n    keyword: NORMAL_KEYWORDS.concat(CONTEXTUAL_KEYWORDS),\n    built_in: BUILT_IN_KEYWORDS,\n    literal: LITERAL_KEYWORDS\n  };\n  const TITLE_MODE = hljs.inherit(hljs.TITLE_MODE, { begin: '[a-zA-Z](\\\\.?\\\\w)*' });\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      { begin: '\\\\b(0b[01\\']+)' },\n      { begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)(u|U|l|L|ul|UL|f|F|b|B)' },\n      { begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)' }\n    ],\n    relevance: 0\n  };\n  const VERBATIM_STRING = {\n    className: 'string',\n    begin: '@\"',\n    end: '\"',\n    contains: [ { begin: '\"\"' } ]\n  };\n  const VERBATIM_STRING_NO_LF = hljs.inherit(VERBATIM_STRING, { illegal: /\\n/ });\n  const SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS\n  };\n  const SUBST_NO_LF = hljs.inherit(SUBST, { illegal: /\\n/ });\n  const INTERPOLATED_STRING = {\n    className: 'string',\n    begin: /\\$\"/,\n    end: '\"',\n    illegal: /\\n/,\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      hljs.BACKSLASH_ESCAPE,\n      SUBST_NO_LF\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING = {\n    className: 'string',\n    begin: /\\$@\"/,\n    end: '\"',\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      { begin: '\"\"' },\n      SUBST\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING_NO_LF = hljs.inherit(INTERPOLATED_VERBATIM_STRING, {\n    illegal: /\\n/,\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      { begin: '\"\"' },\n      SUBST_NO_LF\n    ]\n  });\n  SUBST.contains = [\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.C_BLOCK_COMMENT_MODE\n  ];\n  SUBST_NO_LF.contains = [\n    INTERPOLATED_VERBATIM_STRING_NO_LF,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING_NO_LF,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.inherit(hljs.C_BLOCK_COMMENT_MODE, { illegal: /\\n/ })\n  ];\n  const STRING = { variants: [\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE\n  ] };\n\n  const GENERIC_MODIFIER = {\n    begin: \"<\",\n    end: \">\",\n    contains: [\n      { beginKeywords: \"in out\" },\n      TITLE_MODE\n    ]\n  };\n  const TYPE_IDENT_RE = hljs.IDENT_RE + '(<' + hljs.IDENT_RE + '(\\\\s*,\\\\s*' + hljs.IDENT_RE + ')*>)?(\\\\[\\\\])?';\n  const AT_IDENTIFIER = {\n    // prevents expressions like `@class` from incorrect flagging\n    // `class` as a keyword\n    begin: \"@\" + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  return {\n    name: 'C#',\n    aliases: [\n      'cs',\n      'c#'\n    ],\n    keywords: KEYWORDS,\n    illegal: /::/,\n    contains: [\n      hljs.COMMENT(\n        '///',\n        '$',\n        {\n          returnBegin: true,\n          contains: [\n            {\n              className: 'doctag',\n              variants: [\n                {\n                  begin: '///',\n                  relevance: 0\n                },\n                { begin: '<!--|-->' },\n                {\n                  begin: '</?',\n                  end: '>'\n                }\n              ]\n            }\n          ]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'meta',\n        begin: '#',\n        end: '$',\n        keywords: { keyword: 'if else elif endif define undef warning error line region endregion pragma checksum' }\n      },\n      STRING,\n      NUMBERS,\n      {\n        beginKeywords: 'class interface',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:,]/,\n        contains: [\n          { beginKeywords: \"where class\" },\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'namespace',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'record',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        // [Attributes(\"\")]\n        className: 'meta',\n        begin: '^\\\\s*\\\\[(?=[\\\\w])',\n        excludeBegin: true,\n        end: '\\\\]',\n        excludeEnd: true,\n        contains: [\n          {\n            className: 'string',\n            begin: /\"/,\n            end: /\"/\n          }\n        ]\n      },\n      {\n        // Expression keywords prevent 'keyword Name(...)' from being\n        // recognized as a function definition\n        beginKeywords: 'new return throw await else',\n        relevance: 0\n      },\n      {\n        className: 'function',\n        begin: '(' + TYPE_IDENT_RE + '\\\\s+)+' + hljs.IDENT_RE + '\\\\s*(<[^=]+>\\\\s*)?\\\\(',\n        returnBegin: true,\n        end: /\\s*[{;=]/,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          // prevents these from being highlighted `title`\n          {\n            beginKeywords: FUNCTION_MODIFIERS.join(\" \"),\n            relevance: 0\n          },\n          {\n            begin: hljs.IDENT_RE + '\\\\s*(<[^=]+>\\\\s*)?\\\\(',\n            returnBegin: true,\n            contains: [\n              hljs.TITLE_MODE,\n              GENERIC_MODIFIER\n            ],\n            relevance: 0\n          },\n          { match: /\\(\\)/ },\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              STRING,\n              NUMBERS,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      AT_IDENTIFIER\n    ]\n  };\n}\n\nmodule.exports = csharp;\n", "const MODES = (hljs) => {\n  return {\n    IMPORTANT: {\n      scope: 'meta',\n      begin: '!important'\n    },\n    BLOCK_COMMENT: hljs.C_BLOCK_COMMENT_MODE,\n    HEXCOLOR: {\n      scope: 'number',\n      begin: /#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\\b/\n    },\n    FUNCTION_DISPATCH: {\n      className: \"built_in\",\n      begin: /[\\w-]+(?=\\()/\n    },\n    ATTRIBUTE_SELECTOR_MODE: {\n      scope: 'selector-attr',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: '$',\n      contains: [\n        hljs.APOS_STRING_MODE,\n        hljs.QUOTE_STRING_MODE\n      ]\n    },\n    CSS_NUMBER_MODE: {\n      scope: 'number',\n      begin: hljs.NUMBER_RE + '(' +\n        '%|em|ex|ch|rem' +\n        '|vw|vh|vmin|vmax' +\n        '|cm|mm|in|pt|pc|px' +\n        '|deg|grad|rad|turn' +\n        '|s|ms' +\n        '|Hz|kHz' +\n        '|dpi|dpcm|dppx' +\n        ')?',\n      relevance: 0\n    },\n    CSS_VARIABLE: {\n      className: \"attr\",\n      begin: /--[A-Za-z_][A-Za-z0-9_-]*/\n    }\n  };\n};\n\nconst TAGS = [\n  'a',\n  'abbr',\n  'address',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'blockquote',\n  'body',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'mark',\n  'menu',\n  'nav',\n  'object',\n  'ol',\n  'p',\n  'q',\n  'quote',\n  'samp',\n  'section',\n  'span',\n  'strong',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'ul',\n  'var',\n  'video'\n];\n\nconst MEDIA_FEATURES = [\n  'any-hover',\n  'any-pointer',\n  'aspect-ratio',\n  'color',\n  'color-gamut',\n  'color-index',\n  'device-aspect-ratio',\n  'device-height',\n  'device-width',\n  'display-mode',\n  'forced-colors',\n  'grid',\n  'height',\n  'hover',\n  'inverted-colors',\n  'monochrome',\n  'orientation',\n  'overflow-block',\n  'overflow-inline',\n  'pointer',\n  'prefers-color-scheme',\n  'prefers-contrast',\n  'prefers-reduced-motion',\n  'prefers-reduced-transparency',\n  'resolution',\n  'scan',\n  'scripting',\n  'update',\n  'width',\n  // TODO: find a better solution?\n  'min-width',\n  'max-width',\n  'min-height',\n  'max-height'\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes\nconst PSEUDO_CLASSES = [\n  'active',\n  'any-link',\n  'blank',\n  'checked',\n  'current',\n  'default',\n  'defined',\n  'dir', // dir()\n  'disabled',\n  'drop',\n  'empty',\n  'enabled',\n  'first',\n  'first-child',\n  'first-of-type',\n  'fullscreen',\n  'future',\n  'focus',\n  'focus-visible',\n  'focus-within',\n  'has', // has()\n  'host', // host or host()\n  'host-context', // host-context()\n  'hover',\n  'indeterminate',\n  'in-range',\n  'invalid',\n  'is', // is()\n  'lang', // lang()\n  'last-child',\n  'last-of-type',\n  'left',\n  'link',\n  'local-link',\n  'not', // not()\n  'nth-child', // nth-child()\n  'nth-col', // nth-col()\n  'nth-last-child', // nth-last-child()\n  'nth-last-col', // nth-last-col()\n  'nth-last-of-type', //nth-last-of-type()\n  'nth-of-type', //nth-of-type()\n  'only-child',\n  'only-of-type',\n  'optional',\n  'out-of-range',\n  'past',\n  'placeholder-shown',\n  'read-only',\n  'read-write',\n  'required',\n  'right',\n  'root',\n  'scope',\n  'target',\n  'target-within',\n  'user-invalid',\n  'valid',\n  'visited',\n  'where' // where()\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-elements\nconst PSEUDO_ELEMENTS = [\n  'after',\n  'backdrop',\n  'before',\n  'cue',\n  'cue-region',\n  'first-letter',\n  'first-line',\n  'grammar-error',\n  'marker',\n  'part',\n  'placeholder',\n  'selection',\n  'slotted',\n  'spelling-error'\n];\n\nconst ATTRIBUTES = [\n  'align-content',\n  'align-items',\n  'align-self',\n  'all',\n  'animation',\n  'animation-delay',\n  'animation-direction',\n  'animation-duration',\n  'animation-fill-mode',\n  'animation-iteration-count',\n  'animation-name',\n  'animation-play-state',\n  'animation-timing-function',\n  'backface-visibility',\n  'background',\n  'background-attachment',\n  'background-blend-mode',\n  'background-clip',\n  'background-color',\n  'background-image',\n  'background-origin',\n  'background-position',\n  'background-repeat',\n  'background-size',\n  'block-size',\n  'border',\n  'border-block',\n  'border-block-color',\n  'border-block-end',\n  'border-block-end-color',\n  'border-block-end-style',\n  'border-block-end-width',\n  'border-block-start',\n  'border-block-start-color',\n  'border-block-start-style',\n  'border-block-start-width',\n  'border-block-style',\n  'border-block-width',\n  'border-bottom',\n  'border-bottom-color',\n  'border-bottom-left-radius',\n  'border-bottom-right-radius',\n  'border-bottom-style',\n  'border-bottom-width',\n  'border-collapse',\n  'border-color',\n  'border-image',\n  'border-image-outset',\n  'border-image-repeat',\n  'border-image-slice',\n  'border-image-source',\n  'border-image-width',\n  'border-inline',\n  'border-inline-color',\n  'border-inline-end',\n  'border-inline-end-color',\n  'border-inline-end-style',\n  'border-inline-end-width',\n  'border-inline-start',\n  'border-inline-start-color',\n  'border-inline-start-style',\n  'border-inline-start-width',\n  'border-inline-style',\n  'border-inline-width',\n  'border-left',\n  'border-left-color',\n  'border-left-style',\n  'border-left-width',\n  'border-radius',\n  'border-right',\n  'border-right-color',\n  'border-right-style',\n  'border-right-width',\n  'border-spacing',\n  'border-style',\n  'border-top',\n  'border-top-color',\n  'border-top-left-radius',\n  'border-top-right-radius',\n  'border-top-style',\n  'border-top-width',\n  'border-width',\n  'bottom',\n  'box-decoration-break',\n  'box-shadow',\n  'box-sizing',\n  'break-after',\n  'break-before',\n  'break-inside',\n  'caption-side',\n  'caret-color',\n  'clear',\n  'clip',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'column-count',\n  'column-fill',\n  'column-gap',\n  'column-rule',\n  'column-rule-color',\n  'column-rule-style',\n  'column-rule-width',\n  'column-span',\n  'column-width',\n  'columns',\n  'contain',\n  'content',\n  'content-visibility',\n  'counter-increment',\n  'counter-reset',\n  'cue',\n  'cue-after',\n  'cue-before',\n  'cursor',\n  'direction',\n  'display',\n  'empty-cells',\n  'filter',\n  'flex',\n  'flex-basis',\n  'flex-direction',\n  'flex-flow',\n  'flex-grow',\n  'flex-shrink',\n  'flex-wrap',\n  'float',\n  'flow',\n  'font',\n  'font-display',\n  'font-family',\n  'font-feature-settings',\n  'font-kerning',\n  'font-language-override',\n  'font-size',\n  'font-size-adjust',\n  'font-smoothing',\n  'font-stretch',\n  'font-style',\n  'font-synthesis',\n  'font-variant',\n  'font-variant-caps',\n  'font-variant-east-asian',\n  'font-variant-ligatures',\n  'font-variant-numeric',\n  'font-variant-position',\n  'font-variation-settings',\n  'font-weight',\n  'gap',\n  'glyph-orientation-vertical',\n  'grid',\n  'grid-area',\n  'grid-auto-columns',\n  'grid-auto-flow',\n  'grid-auto-rows',\n  'grid-column',\n  'grid-column-end',\n  'grid-column-start',\n  'grid-gap',\n  'grid-row',\n  'grid-row-end',\n  'grid-row-start',\n  'grid-template',\n  'grid-template-areas',\n  'grid-template-columns',\n  'grid-template-rows',\n  'hanging-punctuation',\n  'height',\n  'hyphens',\n  'icon',\n  'image-orientation',\n  'image-rendering',\n  'image-resolution',\n  'ime-mode',\n  'inline-size',\n  'isolation',\n  'justify-content',\n  'left',\n  'letter-spacing',\n  'line-break',\n  'line-height',\n  'list-style',\n  'list-style-image',\n  'list-style-position',\n  'list-style-type',\n  'margin',\n  'margin-block',\n  'margin-block-end',\n  'margin-block-start',\n  'margin-bottom',\n  'margin-inline',\n  'margin-inline-end',\n  'margin-inline-start',\n  'margin-left',\n  'margin-right',\n  'margin-top',\n  'marks',\n  'mask',\n  'mask-border',\n  'mask-border-mode',\n  'mask-border-outset',\n  'mask-border-repeat',\n  'mask-border-slice',\n  'mask-border-source',\n  'mask-border-width',\n  'mask-clip',\n  'mask-composite',\n  'mask-image',\n  'mask-mode',\n  'mask-origin',\n  'mask-position',\n  'mask-repeat',\n  'mask-size',\n  'mask-type',\n  'max-block-size',\n  'max-height',\n  'max-inline-size',\n  'max-width',\n  'min-block-size',\n  'min-height',\n  'min-inline-size',\n  'min-width',\n  'mix-blend-mode',\n  'nav-down',\n  'nav-index',\n  'nav-left',\n  'nav-right',\n  'nav-up',\n  'none',\n  'normal',\n  'object-fit',\n  'object-position',\n  'opacity',\n  'order',\n  'orphans',\n  'outline',\n  'outline-color',\n  'outline-offset',\n  'outline-style',\n  'outline-width',\n  'overflow',\n  'overflow-wrap',\n  'overflow-x',\n  'overflow-y',\n  'padding',\n  'padding-block',\n  'padding-block-end',\n  'padding-block-start',\n  'padding-bottom',\n  'padding-inline',\n  'padding-inline-end',\n  'padding-inline-start',\n  'padding-left',\n  'padding-right',\n  'padding-top',\n  'page-break-after',\n  'page-break-before',\n  'page-break-inside',\n  'pause',\n  'pause-after',\n  'pause-before',\n  'perspective',\n  'perspective-origin',\n  'pointer-events',\n  'position',\n  'quotes',\n  'resize',\n  'rest',\n  'rest-after',\n  'rest-before',\n  'right',\n  'row-gap',\n  'scroll-margin',\n  'scroll-margin-block',\n  'scroll-margin-block-end',\n  'scroll-margin-block-start',\n  'scroll-margin-bottom',\n  'scroll-margin-inline',\n  'scroll-margin-inline-end',\n  'scroll-margin-inline-start',\n  'scroll-margin-left',\n  'scroll-margin-right',\n  'scroll-margin-top',\n  'scroll-padding',\n  'scroll-padding-block',\n  'scroll-padding-block-end',\n  'scroll-padding-block-start',\n  'scroll-padding-bottom',\n  'scroll-padding-inline',\n  'scroll-padding-inline-end',\n  'scroll-padding-inline-start',\n  'scroll-padding-left',\n  'scroll-padding-right',\n  'scroll-padding-top',\n  'scroll-snap-align',\n  'scroll-snap-stop',\n  'scroll-snap-type',\n  'scrollbar-color',\n  'scrollbar-gutter',\n  'scrollbar-width',\n  'shape-image-threshold',\n  'shape-margin',\n  'shape-outside',\n  'speak',\n  'speak-as',\n  'src', // @font-face\n  'tab-size',\n  'table-layout',\n  'text-align',\n  'text-align-all',\n  'text-align-last',\n  'text-combine-upright',\n  'text-decoration',\n  'text-decoration-color',\n  'text-decoration-line',\n  'text-decoration-style',\n  'text-emphasis',\n  'text-emphasis-color',\n  'text-emphasis-position',\n  'text-emphasis-style',\n  'text-indent',\n  'text-justify',\n  'text-orientation',\n  'text-overflow',\n  'text-rendering',\n  'text-shadow',\n  'text-transform',\n  'text-underline-position',\n  'top',\n  'transform',\n  'transform-box',\n  'transform-origin',\n  'transform-style',\n  'transition',\n  'transition-delay',\n  'transition-duration',\n  'transition-property',\n  'transition-timing-function',\n  'unicode-bidi',\n  'vertical-align',\n  'visibility',\n  'voice-balance',\n  'voice-duration',\n  'voice-family',\n  'voice-pitch',\n  'voice-range',\n  'voice-rate',\n  'voice-stress',\n  'voice-volume',\n  'white-space',\n  'widows',\n  'width',\n  'will-change',\n  'word-break',\n  'word-spacing',\n  'word-wrap',\n  'writing-mode',\n  'z-index'\n  // reverse makes sure longer attributes `font-weight` are matched fully\n  // instead of getting false positives on say `font`\n].reverse();\n\n/*\nLanguage: CSS\nCategory: common, css, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/CSS\n*/\n\n\n/** @type LanguageFn */\nfunction css(hljs) {\n  const regex = hljs.regex;\n  const modes = MODES(hljs);\n  const VENDOR_PREFIX = { begin: /-(webkit|moz|ms|o)-(?=[a-z])/ };\n  const AT_MODIFIERS = \"and or not only\";\n  const AT_PROPERTY_RE = /@-?\\w[\\w]*(-\\w+)*/; // @-webkit-keyframes\n  const IDENT_RE = '[a-zA-Z-][a-zA-Z0-9_-]*';\n  const STRINGS = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE\n  ];\n\n  return {\n    name: 'CSS',\n    case_insensitive: true,\n    illegal: /[=|'\\$]/,\n    keywords: { keyframePosition: \"from to\" },\n    classNameAliases: {\n      // for visual continuity with `tag {}` and because we\n      // don't have a great class for this?\n      keyframePosition: \"selector-tag\" },\n    contains: [\n      modes.BLOCK_COMMENT,\n      VENDOR_PREFIX,\n      // to recognize keyframe 40% etc which are outside the scope of our\n      // attribute value mode\n      modes.CSS_NUMBER_MODE,\n      {\n        className: 'selector-id',\n        begin: /#[A-Za-z0-9_-]+/,\n        relevance: 0\n      },\n      {\n        className: 'selector-class',\n        begin: '\\\\.' + IDENT_RE,\n        relevance: 0\n      },\n      modes.ATTRIBUTE_SELECTOR_MODE,\n      {\n        className: 'selector-pseudo',\n        variants: [\n          { begin: ':(' + PSEUDO_CLASSES.join('|') + ')' },\n          { begin: ':(:)?(' + PSEUDO_ELEMENTS.join('|') + ')' }\n        ]\n      },\n      // we may actually need this (12/2020)\n      // { // pseudo-selector params\n      //   begin: /\\(/,\n      //   end: /\\)/,\n      //   contains: [ hljs.CSS_NUMBER_MODE ]\n      // },\n      modes.CSS_VARIABLE,\n      {\n        className: 'attribute',\n        begin: '\\\\b(' + ATTRIBUTES.join('|') + ')\\\\b'\n      },\n      // attribute values\n      {\n        begin: /:/,\n        end: /[;}{]/,\n        contains: [\n          modes.BLOCK_COMMENT,\n          modes.HEXCOLOR,\n          modes.IMPORTANT,\n          modes.CSS_NUMBER_MODE,\n          ...STRINGS,\n          // needed to highlight these as strings and to avoid issues with\n          // illegal characters that might be inside urls that would tigger the\n          // languages illegal stack\n          {\n            begin: /(url|data-uri)\\(/,\n            end: /\\)/,\n            relevance: 0, // from keywords\n            keywords: { built_in: \"url data-uri\" },\n            contains: [\n              ...STRINGS,\n              {\n                className: \"string\",\n                // any character other than `)` as in `url()` will be the start\n                // of a string, which ends with `)` (from the parent mode)\n                begin: /[^)]/,\n                endsWithParent: true,\n                excludeEnd: true\n              }\n            ]\n          },\n          modes.FUNCTION_DISPATCH\n        ]\n      },\n      {\n        begin: regex.lookahead(/@/),\n        end: '[{;]',\n        relevance: 0,\n        illegal: /:/, // break on Less variables @var: ...\n        contains: [\n          {\n            className: 'keyword',\n            begin: AT_PROPERTY_RE\n          },\n          {\n            begin: /\\s/,\n            endsWithParent: true,\n            excludeEnd: true,\n            relevance: 0,\n            keywords: {\n              $pattern: /[a-z-]+/,\n              keyword: AT_MODIFIERS,\n              attribute: MEDIA_FEATURES.join(\" \")\n            },\n            contains: [\n              {\n                begin: /[a-z-]+(?=:)/,\n                className: \"attribute\"\n              },\n              ...STRINGS,\n              modes.CSS_NUMBER_MODE\n            ]\n          }\n        ]\n      },\n      {\n        className: 'selector-tag',\n        begin: '\\\\b(' + TAGS.join('|') + ')\\\\b'\n      }\n    ]\n  };\n}\n\nmodule.exports = css;\n", "/*\nLanguage: Markdown\nRequires: xml.js\nAuthor: <PERSON> <john.cre<PERSON><PERSON>@gmail.com>\nWebsite: https://daringfireball.net/projects/markdown/\nCategory: common, markup\n*/\n\nfunction markdown(hljs) {\n  const regex = hljs.regex;\n  const INLINE_HTML = {\n    begin: /<\\/?[A-Za-z_]/,\n    end: '>',\n    subLanguage: 'xml',\n    relevance: 0\n  };\n  const HORIZONTAL_RULE = {\n    begin: '^[-\\\\*]{3,}',\n    end: '$'\n  };\n  const CODE = {\n    className: 'code',\n    variants: [\n      // TODO: fix to allow these to work with sublanguage also\n      { begin: '(`{3,})[^`](.|\\\\n)*?\\\\1`*[ ]*' },\n      { begin: '(~{3,})[^~](.|\\\\n)*?\\\\1~*[ ]*' },\n      // needed to allow markdown as a sublanguage to work\n      {\n        begin: '```',\n        end: '```+[ ]*$'\n      },\n      {\n        begin: '~~~',\n        end: '~~~+[ ]*$'\n      },\n      { begin: '`.+?`' },\n      {\n        begin: '(?=^( {4}|\\\\t))',\n        // use contains to gobble up multiple lines to allow the block to be whatever size\n        // but only have a single open/close tag vs one per line\n        contains: [\n          {\n            begin: '^( {4}|\\\\t)',\n            end: '(\\\\n)$'\n          }\n        ],\n        relevance: 0\n      }\n    ]\n  };\n  const LIST = {\n    className: 'bullet',\n    begin: '^[ \\t]*([*+-]|(\\\\d+\\\\.))(?=\\\\s+)',\n    end: '\\\\s+',\n    excludeEnd: true\n  };\n  const LINK_REFERENCE = {\n    begin: /^\\[[^\\n]+\\]:/,\n    returnBegin: true,\n    contains: [\n      {\n        className: 'symbol',\n        begin: /\\[/,\n        end: /\\]/,\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'link',\n        begin: /:\\s*/,\n        end: /$/,\n        excludeBegin: true\n      }\n    ]\n  };\n  const URL_SCHEME = /[A-Za-z][A-Za-z0-9+.-]*/;\n  const LINK = {\n    variants: [\n      // too much like nested array access in so many languages\n      // to have any real relevance\n      {\n        begin: /\\[.+?\\]\\[.*?\\]/,\n        relevance: 0\n      },\n      // popular internet URLs\n      {\n        begin: /\\[.+?\\]\\(((data|javascript|mailto):|(?:http|ftp)s?:\\/\\/).*?\\)/,\n        relevance: 2\n      },\n      {\n        begin: regex.concat(/\\[.+?\\]\\(/, URL_SCHEME, /:\\/\\/.*?\\)/),\n        relevance: 2\n      },\n      // relative urls\n      {\n        begin: /\\[.+?\\]\\([./?&#].*?\\)/,\n        relevance: 1\n      },\n      // whatever else, lower relevance (might not be a link at all)\n      {\n        begin: /\\[.*?\\]\\(.*?\\)/,\n        relevance: 0\n      }\n    ],\n    returnBegin: true,\n    contains: [\n      {\n        // empty strings for alt or link text\n        match: /\\[(?=\\])/ },\n      {\n        className: 'string',\n        relevance: 0,\n        begin: '\\\\[',\n        end: '\\\\]',\n        excludeBegin: true,\n        returnEnd: true\n      },\n      {\n        className: 'link',\n        relevance: 0,\n        begin: '\\\\]\\\\(',\n        end: '\\\\)',\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'symbol',\n        relevance: 0,\n        begin: '\\\\]\\\\[',\n        end: '\\\\]',\n        excludeBegin: true,\n        excludeEnd: true\n      }\n    ]\n  };\n  const BOLD = {\n    className: 'strong',\n    contains: [], // defined later\n    variants: [\n      {\n        begin: /_{2}(?!\\s)/,\n        end: /_{2}/\n      },\n      {\n        begin: /\\*{2}(?!\\s)/,\n        end: /\\*{2}/\n      }\n    ]\n  };\n  const ITALIC = {\n    className: 'emphasis',\n    contains: [], // defined later\n    variants: [\n      {\n        begin: /\\*(?![*\\s])/,\n        end: /\\*/\n      },\n      {\n        begin: /_(?![_\\s])/,\n        end: /_/,\n        relevance: 0\n      }\n    ]\n  };\n\n  // 3 level deep nesting is not allowed because it would create confusion\n  // in cases like `***testing***` because where we don't know if the last\n  // `***` is starting a new bold/italic or finishing the last one\n  const BOLD_WITHOUT_ITALIC = hljs.inherit(BOLD, { contains: [] });\n  const ITALIC_WITHOUT_BOLD = hljs.inherit(ITALIC, { contains: [] });\n  BOLD.contains.push(ITALIC_WITHOUT_BOLD);\n  ITALIC.contains.push(BOLD_WITHOUT_ITALIC);\n\n  let CONTAINABLE = [\n    INLINE_HTML,\n    LINK\n  ];\n\n  [\n    BOLD,\n    ITALIC,\n    BOLD_WITHOUT_ITALIC,\n    ITALIC_WITHOUT_BOLD\n  ].forEach(m => {\n    m.contains = m.contains.concat(CONTAINABLE);\n  });\n\n  CONTAINABLE = CONTAINABLE.concat(BOLD, ITALIC);\n\n  const HEADER = {\n    className: 'section',\n    variants: [\n      {\n        begin: '^#{1,6}',\n        end: '$',\n        contains: CONTAINABLE\n      },\n      {\n        begin: '(?=^.+?\\\\n[=-]{2,}$)',\n        contains: [\n          { begin: '^[=-]*$' },\n          {\n            begin: '^',\n            end: \"\\\\n\",\n            contains: CONTAINABLE\n          }\n        ]\n      }\n    ]\n  };\n\n  const BLOCKQUOTE = {\n    className: 'quote',\n    begin: '^>\\\\s+',\n    contains: CONTAINABLE,\n    end: '$'\n  };\n\n  return {\n    name: 'Markdown',\n    aliases: [\n      'md',\n      'mkdown',\n      'mkd'\n    ],\n    contains: [\n      HEADER,\n      INLINE_HTML,\n      LIST,\n      BOLD,\n      ITALIC,\n      BLOCKQUOTE,\n      CODE,\n      HORIZONTAL_RULE,\n      LINK,\n      LINK_REFERENCE\n    ]\n  };\n}\n\nmodule.exports = markdown;\n", "/*\nLanguage: Diff\nDescription: Unified and context diff\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/diffutils/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction diff(hljs) {\n  const regex = hljs.regex;\n  return {\n    name: 'Diff',\n    aliases: [ 'patch' ],\n    contains: [\n      {\n        className: 'meta',\n        relevance: 10,\n        match: regex.either(\n          /^@@ +-\\d+,\\d+ +\\+\\d+,\\d+ +@@/,\n          /^\\*\\*\\* +\\d+,\\d+ +\\*\\*\\*\\*$/,\n          /^--- +\\d+,\\d+ +----$/\n        )\n      },\n      {\n        className: 'comment',\n        variants: [\n          {\n            begin: regex.either(\n              /Index: /,\n              /^index/,\n              /={3,}/,\n              /^-{3}/,\n              /^\\*{3} /,\n              /^\\+{3}/,\n              /^diff --git/\n            ),\n            end: /$/\n          },\n          { match: /^\\*{15}$/ }\n        ]\n      },\n      {\n        className: 'addition',\n        begin: /^\\+/,\n        end: /$/\n      },\n      {\n        className: 'deletion',\n        begin: /^-/,\n        end: /$/\n      },\n      {\n        className: 'addition',\n        begin: /^!/,\n        end: /$/\n      }\n    ]\n  };\n}\n\nmodule.exports = diff;\n", "/*\nLanguage: Ruby\nDescription: Ruby is a dynamic, open source programming language with a focus on simplicity and productivity.\nWebsite: https://www.ruby-lang.org/\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nCategory: common\n*/\n\nfunction ruby(hljs) {\n  const regex = hljs.regex;\n  const RUBY_METHOD_RE = '([a-zA-Z_]\\\\w*[!?=]?|[-+~]@|<<|>>|=~|===?|<=>|[<>]=?|\\\\*\\\\*|[-/+%^&*~`|]|\\\\[\\\\]=?)';\n  // TODO: move concepts like CAMEL_CASE into `modes.js`\n  const CLASS_NAME_RE = regex.either(\n    /\\b([A-Z]+[a-z0-9]+)+/,\n    // ends in caps\n    /\\b([A-Z]+[a-z0-9]+)+[A-Z]+/,\n  )\n  ;\n  const CLASS_NAME_WITH_NAMESPACE_RE = regex.concat(CLASS_NAME_RE, /(::\\w+)*/);\n  // very popular ruby built-ins that one might even assume\n  // are actual keywords (despite that not being the case)\n  const PSEUDO_KWS = [\n    \"include\",\n    \"extend\",\n    \"prepend\",\n    \"public\",\n    \"private\",\n    \"protected\",\n    \"raise\",\n    \"throw\"\n  ];\n  const RUBY_KEYWORDS = {\n    \"variable.constant\": [\n      \"__FILE__\",\n      \"__LINE__\",\n      \"__ENCODING__\"\n    ],\n    \"variable.language\": [\n      \"self\",\n      \"super\",\n    ],\n    keyword: [\n      \"alias\",\n      \"and\",\n      \"begin\",\n      \"BEGIN\",\n      \"break\",\n      \"case\",\n      \"class\",\n      \"defined\",\n      \"do\",\n      \"else\",\n      \"elsif\",\n      \"end\",\n      \"END\",\n      \"ensure\",\n      \"for\",\n      \"if\",\n      \"in\",\n      \"module\",\n      \"next\",\n      \"not\",\n      \"or\",\n      \"redo\",\n      \"require\",\n      \"rescue\",\n      \"retry\",\n      \"return\",\n      \"then\",\n      \"undef\",\n      \"unless\",\n      \"until\",\n      \"when\",\n      \"while\",\n      \"yield\",\n      ...PSEUDO_KWS\n    ],\n    built_in: [\n      \"proc\",\n      \"lambda\",\n      \"attr_accessor\",\n      \"attr_reader\",\n      \"attr_writer\",\n      \"define_method\",\n      \"private_constant\",\n      \"module_function\"\n    ],\n    literal: [\n      \"true\",\n      \"false\",\n      \"nil\"\n    ]\n  };\n  const YARDOCTAG = {\n    className: 'doctag',\n    begin: '@[A-Za-z]+'\n  };\n  const IRB_OBJECT = {\n    begin: '#<',\n    end: '>'\n  };\n  const COMMENT_MODES = [\n    hljs.COMMENT(\n      '#',\n      '$',\n      { contains: [ YARDOCTAG ] }\n    ),\n    hljs.COMMENT(\n      '^=begin',\n      '^=end',\n      {\n        contains: [ YARDOCTAG ],\n        relevance: 10\n      }\n    ),\n    hljs.COMMENT('^__END__', hljs.MATCH_NOTHING_RE)\n  ];\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: RUBY_KEYWORDS\n  };\n  const STRING = {\n    className: 'string',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ],\n    variants: [\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\"/,\n        end: /\"/\n      },\n      {\n        begin: /`/,\n        end: /`/\n      },\n      {\n        begin: /%[qQwWx]?\\(/,\n        end: /\\)/\n      },\n      {\n        begin: /%[qQwWx]?\\[/,\n        end: /\\]/\n      },\n      {\n        begin: /%[qQwWx]?\\{/,\n        end: /\\}/\n      },\n      {\n        begin: /%[qQwWx]?</,\n        end: />/\n      },\n      {\n        begin: /%[qQwWx]?\\//,\n        end: /\\//\n      },\n      {\n        begin: /%[qQwWx]?%/,\n        end: /%/\n      },\n      {\n        begin: /%[qQwWx]?-/,\n        end: /-/\n      },\n      {\n        begin: /%[qQwWx]?\\|/,\n        end: /\\|/\n      },\n      // in the following expressions, \\B in the beginning suppresses recognition of ?-sequences\n      // where ? is the last character of a preceding identifier, as in: `func?4`\n      { begin: /\\B\\?(\\\\\\d{1,3})/ },\n      { begin: /\\B\\?(\\\\x[A-Fa-f0-9]{1,2})/ },\n      { begin: /\\B\\?(\\\\u\\{?[A-Fa-f0-9]{1,6}\\}?)/ },\n      { begin: /\\B\\?(\\\\M-\\\\C-|\\\\M-\\\\c|\\\\c\\\\M-|\\\\M-|\\\\C-\\\\M-)[\\x20-\\x7e]/ },\n      { begin: /\\B\\?\\\\(c|C-)[\\x20-\\x7e]/ },\n      { begin: /\\B\\?\\\\?\\S/ },\n      // heredocs\n      {\n        // this guard makes sure that we have an entire heredoc and not a false\n        // positive (auto-detect, etc.)\n        begin: regex.concat(\n          /<<[-~]?'?/,\n          regex.lookahead(/(\\w+)(?=\\W)[^\\n]*\\n(?:[^\\n]*\\n)*?\\s*\\1\\b/)\n        ),\n        contains: [\n          hljs.END_SAME_AS_BEGIN({\n            begin: /(\\w+)/,\n            end: /(\\w+)/,\n            contains: [\n              hljs.BACKSLASH_ESCAPE,\n              SUBST\n            ]\n          })\n        ]\n      }\n    ]\n  };\n\n  // Ruby syntax is underdocumented, but this grammar seems to be accurate\n  // as of version 2.7.2 (confirmed with (irb and `Ripper.sexp(...)`)\n  // https://docs.ruby-lang.org/en/2.7.0/doc/syntax/literals_rdoc.html#label-Numbers\n  const decimal = '[1-9](_?[0-9])*|0';\n  const digits = '[0-9](_?[0-9])*';\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      // decimal integer/float, optionally exponential or rational, optionally imaginary\n      { begin: `\\\\b(${decimal})(\\\\.(${digits}))?([eE][+-]?(${digits})|r)?i?\\\\b` },\n\n      // explicit decimal/binary/octal/hexadecimal integer,\n      // optionally rational and/or imaginary\n      { begin: \"\\\\b0[dD][0-9](_?[0-9])*r?i?\\\\b\" },\n      { begin: \"\\\\b0[bB][0-1](_?[0-1])*r?i?\\\\b\" },\n      { begin: \"\\\\b0[oO][0-7](_?[0-7])*r?i?\\\\b\" },\n      { begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*r?i?\\\\b\" },\n\n      // 0-prefixed implicit octal integer, optionally rational and/or imaginary\n      { begin: \"\\\\b0(_?[0-7])+r?i?\\\\b\" }\n    ]\n  };\n\n  const PARAMS = {\n    variants: [\n      {\n        match: /\\(\\)/,\n      },\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /(?=\\))/,\n        excludeBegin: true,\n        endsParent: true,\n        keywords: RUBY_KEYWORDS,\n      }\n    ]\n  };\n\n  const INCLUDE_EXTEND = {\n    match: [\n      /(include|extend)\\s+/,\n      CLASS_NAME_WITH_NAMESPACE_RE\n    ],\n    scope: {\n      2: \"title.class\"\n    },\n    keywords: RUBY_KEYWORDS\n  };\n\n  const CLASS_DEFINITION = {\n    variants: [\n      {\n        match: [\n          /class\\s+/,\n          CLASS_NAME_WITH_NAMESPACE_RE,\n          /\\s+<\\s+/,\n          CLASS_NAME_WITH_NAMESPACE_RE\n        ]\n      },\n      {\n        match: [\n          /\\b(class|module)\\s+/,\n          CLASS_NAME_WITH_NAMESPACE_RE\n        ]\n      }\n    ],\n    scope: {\n      2: \"title.class\",\n      4: \"title.class.inherited\"\n    },\n    keywords: RUBY_KEYWORDS\n  };\n\n  const UPPER_CASE_CONSTANT = {\n    relevance: 0,\n    match: /\\b[A-Z][A-Z_0-9]+\\b/,\n    className: \"variable.constant\"\n  };\n\n  const METHOD_DEFINITION = {\n    match: [\n      /def/, /\\s+/,\n      RUBY_METHOD_RE\n    ],\n    scope: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      PARAMS\n    ]\n  };\n\n  const OBJECT_CREATION = {\n    relevance: 0,\n    match: [\n      CLASS_NAME_WITH_NAMESPACE_RE,\n      /\\.new[. (]/\n    ],\n    scope: {\n      1: \"title.class\"\n    }\n  };\n\n  // CamelCase\n  const CLASS_REFERENCE = {\n    relevance: 0,\n    match: CLASS_NAME_RE,\n    scope: \"title.class\"\n  };\n\n  const RUBY_DEFAULT_CONTAINS = [\n    STRING,\n    CLASS_DEFINITION,\n    INCLUDE_EXTEND,\n    OBJECT_CREATION,\n    UPPER_CASE_CONSTANT,\n    CLASS_REFERENCE,\n    METHOD_DEFINITION,\n    {\n      // swallow namespace qualifiers before symbols\n      begin: hljs.IDENT_RE + '::' },\n    {\n      className: 'symbol',\n      begin: hljs.UNDERSCORE_IDENT_RE + '(!|\\\\?)?:',\n      relevance: 0\n    },\n    {\n      className: 'symbol',\n      begin: ':(?!\\\\s)',\n      contains: [\n        STRING,\n        { begin: RUBY_METHOD_RE }\n      ],\n      relevance: 0\n    },\n    NUMBER,\n    {\n      // negative-look forward attempts to prevent false matches like:\n      // @ident@ or $ident$ that might indicate this is not ruby at all\n      className: \"variable\",\n      begin: '(\\\\$\\\\W)|((\\\\$|@@?)(\\\\w+))(?=[^@$?])' + `(?![A-Za-z])(?![@$?'])`\n    },\n    {\n      className: 'params',\n      begin: /\\|/,\n      end: /\\|/,\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0, // this could be a lot of things (in other languages) other than params\n      keywords: RUBY_KEYWORDS\n    },\n    { // regexp container\n      begin: '(' + hljs.RE_STARTERS_RE + '|unless)\\\\s*',\n      keywords: 'unless',\n      contains: [\n        {\n          className: 'regexp',\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST\n          ],\n          illegal: /\\n/,\n          variants: [\n            {\n              begin: '/',\n              end: '/[a-z]*'\n            },\n            {\n              begin: /%r\\{/,\n              end: /\\}[a-z]*/\n            },\n            {\n              begin: '%r\\\\(',\n              end: '\\\\)[a-z]*'\n            },\n            {\n              begin: '%r!',\n              end: '![a-z]*'\n            },\n            {\n              begin: '%r\\\\[',\n              end: '\\\\][a-z]*'\n            }\n          ]\n        }\n      ].concat(IRB_OBJECT, COMMENT_MODES),\n      relevance: 0\n    }\n  ].concat(IRB_OBJECT, COMMENT_MODES);\n\n  SUBST.contains = RUBY_DEFAULT_CONTAINS;\n  PARAMS.contains = RUBY_DEFAULT_CONTAINS;\n\n  // >>\n  // ?>\n  const SIMPLE_PROMPT = \"[>?]>\";\n  // irb(main):001:0>\n  const DEFAULT_PROMPT = \"[\\\\w#]+\\\\(\\\\w+\\\\):\\\\d+:\\\\d+[>*]\";\n  const RVM_PROMPT = \"(\\\\w+-)?\\\\d+\\\\.\\\\d+\\\\.\\\\d+(p\\\\d+)?[^\\\\d][^>]+>\";\n\n  const IRB_DEFAULT = [\n    {\n      begin: /^\\s*=>/,\n      starts: {\n        end: '$',\n        contains: RUBY_DEFAULT_CONTAINS\n      }\n    },\n    {\n      className: 'meta.prompt',\n      begin: '^(' + SIMPLE_PROMPT + \"|\" + DEFAULT_PROMPT + '|' + RVM_PROMPT + ')(?=[ ])',\n      starts: {\n        end: '$',\n        keywords: RUBY_KEYWORDS,\n        contains: RUBY_DEFAULT_CONTAINS\n      }\n    }\n  ];\n\n  COMMENT_MODES.unshift(IRB_OBJECT);\n\n  return {\n    name: 'Ruby',\n    aliases: [\n      'rb',\n      'gemspec',\n      'podspec',\n      'thor',\n      'irb'\n    ],\n    keywords: RUBY_KEYWORDS,\n    illegal: /\\/\\*/,\n    contains: [ hljs.SHEBANG({ binary: \"ruby\" }) ]\n      .concat(IRB_DEFAULT)\n      .concat(COMMENT_MODES)\n      .concat(RUBY_DEFAULT_CONTAINS)\n  };\n}\n\nmodule.exports = ruby;\n", "/*\nLanguage: Go\nAuthor: <PERSON> aka StepLg <<EMAIL>>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: Google go language (golang). For info about language\nWebsite: http://golang.org/\nCategory: common, system\n*/\n\nfunction go(hljs) {\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"iota\",\n    \"nil\"\n  ];\n  const BUILT_INS = [\n    \"append\",\n    \"cap\",\n    \"close\",\n    \"complex\",\n    \"copy\",\n    \"imag\",\n    \"len\",\n    \"make\",\n    \"new\",\n    \"panic\",\n    \"print\",\n    \"println\",\n    \"real\",\n    \"recover\",\n    \"delete\"\n  ];\n  const TYPES = [\n    \"bool\",\n    \"byte\",\n    \"complex64\",\n    \"complex128\",\n    \"error\",\n    \"float32\",\n    \"float64\",\n    \"int8\",\n    \"int16\",\n    \"int32\",\n    \"int64\",\n    \"string\",\n    \"uint8\",\n    \"uint16\",\n    \"uint32\",\n    \"uint64\",\n    \"int\",\n    \"uint\",\n    \"uintptr\",\n    \"rune\"\n  ];\n  const KWS = [\n    \"break\",\n    \"case\",\n    \"chan\",\n    \"const\",\n    \"continue\",\n    \"default\",\n    \"defer\",\n    \"else\",\n    \"fallthrough\",\n    \"for\",\n    \"func\",\n    \"go\",\n    \"goto\",\n    \"if\",\n    \"import\",\n    \"interface\",\n    \"map\",\n    \"package\",\n    \"range\",\n    \"return\",\n    \"select\",\n    \"struct\",\n    \"switch\",\n    \"type\",\n    \"var\",\n  ];\n  const KEYWORDS = {\n    keyword: KWS,\n    type: TYPES,\n    literal: LITERALS,\n    built_in: BUILT_INS\n  };\n  return {\n    name: 'Go',\n    aliases: [ 'golang' ],\n    keywords: KEYWORDS,\n    illegal: '</',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'string',\n        variants: [\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          {\n            begin: '`',\n            end: '`'\n          }\n        ]\n      },\n      {\n        className: 'number',\n        variants: [\n          {\n            begin: hljs.C_NUMBER_RE + '[i]',\n            relevance: 1\n          },\n          hljs.C_NUMBER_MODE\n        ]\n      },\n      { begin: /:=/ // relevance booster\n      },\n      {\n        className: 'function',\n        beginKeywords: 'func',\n        end: '\\\\s*(\\\\{|$)',\n        excludeEnd: true,\n        contains: [\n          hljs.TITLE_MODE,\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            endsParent: true,\n            keywords: KEYWORDS,\n            illegal: /[\"']/\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = go;\n", "/*\n Language: GraphQL\n Author: <PERSON> (GH jf990), and others\n Description: GraphQL is a query language for APIs\n Category: web, common\n*/\n\n/** @type LanguageFn */\nfunction graphql(hljs) {\n  const regex = hljs.regex;\n  const GQL_NAME = /[_A-Za-z][_0-9A-Za-z]*/;\n  return {\n    name: \"GraphQL\",\n    aliases: [ \"gql\" ],\n    case_insensitive: true,\n    disableAutodetect: false,\n    keywords: {\n      keyword: [\n        \"query\",\n        \"mutation\",\n        \"subscription\",\n        \"type\",\n        \"input\",\n        \"schema\",\n        \"directive\",\n        \"interface\",\n        \"union\",\n        \"scalar\",\n        \"fragment\",\n        \"enum\",\n        \"on\"\n      ],\n      literal: [\n        \"true\",\n        \"false\",\n        \"null\"\n      ]\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE,\n      {\n        scope: \"punctuation\",\n        match: /[.]{3}/,\n        relevance: 0\n      },\n      {\n        scope: \"punctuation\",\n        begin: /[\\!\\(\\)\\:\\=\\[\\]\\{\\|\\}]{1}/,\n        relevance: 0\n      },\n      {\n        scope: \"variable\",\n        begin: /\\$/,\n        end: /\\W/,\n        excludeEnd: true,\n        relevance: 0\n      },\n      {\n        scope: \"meta\",\n        match: /@\\w+/,\n        excludeEnd: true\n      },\n      {\n        scope: \"symbol\",\n        begin: regex.concat(GQL_NAME, regex.lookahead(/\\s*:/)),\n        relevance: 0\n      }\n    ],\n    illegal: [\n      /[;<']/,\n      /BEGIN/\n    ]\n  };\n}\n\nmodule.exports = graphql;\n", "/*\nLanguage: TOML, also INI\nDescription: TOML aims to be a minimal configuration file format that's easy to read due to obvious semantics.\nContributors: <PERSON> <<EMAIL>>\nCategory: common, config\nWebsite: https://github.com/toml-lang/toml\n*/\n\nfunction ini(hljs) {\n  const regex = hljs.regex;\n  const NUMBERS = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      { begin: /([+-]+)?[\\d]+_[\\d_]+/ },\n      { begin: hljs.NUMBER_RE }\n    ]\n  };\n  const COMMENTS = hljs.COMMENT();\n  COMMENTS.variants = [\n    {\n      begin: /;/,\n      end: /$/\n    },\n    {\n      begin: /#/,\n      end: /$/\n    }\n  ];\n  const VARIABLES = {\n    className: 'variable',\n    variants: [\n      { begin: /\\$[\\w\\d\"][\\w\\d_]*/ },\n      { begin: /\\$\\{(.*?)\\}/ }\n    ]\n  };\n  const LITERALS = {\n    className: 'literal',\n    begin: /\\bon|off|true|false|yes|no\\b/\n  };\n  const STRINGS = {\n    className: \"string\",\n    contains: [ hljs.BACKSLASH_ESCAPE ],\n    variants: [\n      {\n        begin: \"'''\",\n        end: \"'''\",\n        relevance: 10\n      },\n      {\n        begin: '\"\"\"',\n        end: '\"\"\"',\n        relevance: 10\n      },\n      {\n        begin: '\"',\n        end: '\"'\n      },\n      {\n        begin: \"'\",\n        end: \"'\"\n      }\n    ]\n  };\n  const ARRAY = {\n    begin: /\\[/,\n    end: /\\]/,\n    contains: [\n      COMMENTS,\n      LITERALS,\n      VARIABLES,\n      STRINGS,\n      NUMBERS,\n      'self'\n    ],\n    relevance: 0\n  };\n\n  const BARE_KEY = /[A-Za-z0-9_-]+/;\n  const QUOTED_KEY_DOUBLE_QUOTE = /\"(\\\\\"|[^\"])*\"/;\n  const QUOTED_KEY_SINGLE_QUOTE = /'[^']*'/;\n  const ANY_KEY = regex.either(\n    BARE_KEY, QUOTED_KEY_DOUBLE_QUOTE, QUOTED_KEY_SINGLE_QUOTE\n  );\n  const DOTTED_KEY = regex.concat(\n    ANY_KEY, '(\\\\s*\\\\.\\\\s*', ANY_KEY, ')*',\n    regex.lookahead(/\\s*=\\s*[^#\\s]/)\n  );\n\n  return {\n    name: 'TOML, also INI',\n    aliases: [ 'toml' ],\n    case_insensitive: true,\n    illegal: /\\S/,\n    contains: [\n      COMMENTS,\n      {\n        className: 'section',\n        begin: /\\[+/,\n        end: /\\]+/\n      },\n      {\n        begin: DOTTED_KEY,\n        className: 'attr',\n        starts: {\n          end: /$/,\n          contains: [\n            COMMENTS,\n            ARRAY,\n            LITERALS,\n            VARIABLES,\n            STRINGS,\n            NUMBERS\n          ]\n        }\n      }\n    ]\n  };\n}\n\nmodule.exports = ini;\n", "// https://docs.oracle.com/javase/specs/jls/se15/html/jls-3.html#jls-3.10\nvar decimalDigits = '[0-9](_*[0-9])*';\nvar frac = `\\\\.(${decimalDigits})`;\nvar hexDigits = '[0-9a-fA-F](_*[0-9a-fA-F])*';\nvar NUMERIC = {\n  className: 'number',\n  variants: [\n    // DecimalFloatingPointLiteral\n    // including ExponentPart\n    { begin: `(\\\\b(${decimalDigits})((${frac})|\\\\.)?|(${frac}))` +\n      `[eE][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n    // excluding ExponentPart\n    { begin: `\\\\b(${decimalDigits})((${frac})[fFdD]?\\\\b|\\\\.([fFdD]\\\\b)?)` },\n    { begin: `(${frac})[fFdD]?\\\\b` },\n    { begin: `\\\\b(${decimalDigits})[fFdD]\\\\b` },\n\n    // HexadecimalFloatingPointLiteral\n    { begin: `\\\\b0[xX]((${hexDigits})\\\\.?|(${hexDigits})?\\\\.(${hexDigits}))` +\n      `[pP][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n\n    // DecimalIntegerLiteral\n    { begin: '\\\\b(0|[1-9](_*[0-9])*)[lL]?\\\\b' },\n\n    // HexIntegerLiteral\n    { begin: `\\\\b0[xX](${hexDigits})[lL]?\\\\b` },\n\n    // OctalIntegerLiteral\n    { begin: '\\\\b0(_*[0-7])*[lL]?\\\\b' },\n\n    // BinaryIntegerLiteral\n    { begin: '\\\\b0[bB][01](_*[01])*[lL]?\\\\b' },\n  ],\n  relevance: 0\n};\n\n/*\nLanguage: Java\nAuthor: Vsevolod Solovyov <<EMAIL>>\nCategory: common, enterprise\nWebsite: https://www.java.com/\n*/\n\n\n/**\n * Allows recursive regex expressions to a given depth\n *\n * ie: recurRegex(\"(abc~~~)\", /~~~/g, 2) becomes:\n * (abc(abc(abc)))\n *\n * @param {string} re\n * @param {RegExp} substitution (should be a g mode regex)\n * @param {number} depth\n * @returns {string}``\n */\nfunction recurRegex(re, substitution, depth) {\n  if (depth === -1) return \"\";\n\n  return re.replace(substitution, _ => {\n    return recurRegex(re, substitution, depth - 1);\n  });\n}\n\n/** @type LanguageFn */\nfunction java(hljs) {\n  const regex = hljs.regex;\n  const JAVA_IDENT_RE = '[\\u00C0-\\u02B8a-zA-Z_$][\\u00C0-\\u02B8a-zA-Z_$0-9]*';\n  const GENERIC_IDENT_RE = JAVA_IDENT_RE\n    + recurRegex('(?:<' + JAVA_IDENT_RE + '~~~(?:\\\\s*,\\\\s*' + JAVA_IDENT_RE + '~~~)*>)?', /~~~/g, 2);\n  const MAIN_KEYWORDS = [\n    'synchronized',\n    'abstract',\n    'private',\n    'var',\n    'static',\n    'if',\n    'const ',\n    'for',\n    'while',\n    'strictfp',\n    'finally',\n    'protected',\n    'import',\n    'native',\n    'final',\n    'void',\n    'enum',\n    'else',\n    'break',\n    'transient',\n    'catch',\n    'instanceof',\n    'volatile',\n    'case',\n    'assert',\n    'package',\n    'default',\n    'public',\n    'try',\n    'switch',\n    'continue',\n    'throws',\n    'protected',\n    'public',\n    'private',\n    'module',\n    'requires',\n    'exports',\n    'do',\n    'sealed',\n    'yield',\n    'permits'\n  ];\n\n  const BUILT_INS = [\n    'super',\n    'this'\n  ];\n\n  const LITERALS = [\n    'false',\n    'true',\n    'null'\n  ];\n\n  const TYPES = [\n    'char',\n    'boolean',\n    'long',\n    'float',\n    'int',\n    'byte',\n    'short',\n    'double'\n  ];\n\n  const KEYWORDS = {\n    keyword: MAIN_KEYWORDS,\n    literal: LITERALS,\n    type: TYPES,\n    built_in: BUILT_INS\n  };\n\n  const ANNOTATION = {\n    className: 'meta',\n    begin: '@' + JAVA_IDENT_RE,\n    contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [ \"self\" ] // allow nested () inside our annotation\n      }\n    ]\n  };\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS,\n    relevance: 0,\n    contains: [ hljs.C_BLOCK_COMMENT_MODE ],\n    endsParent: true\n  };\n\n  return {\n    name: 'Java',\n    aliases: [ 'jsp' ],\n    keywords: KEYWORDS,\n    illegal: /<\\/|#/,\n    contains: [\n      hljs.COMMENT(\n        '/\\\\*\\\\*',\n        '\\\\*/',\n        {\n          relevance: 0,\n          contains: [\n            {\n              // eat up @'s in emails to prevent them to be recognized as doctags\n              begin: /\\w+@/,\n              relevance: 0\n            },\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            }\n          ]\n        }\n      ),\n      // relevance boost\n      {\n        begin: /import java\\.[a-z]+\\./,\n        keywords: \"import\",\n        relevance: 2\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        begin: /\"\"\"/,\n        end: /\"\"\"/,\n        className: \"string\",\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        match: [\n          /\\b(?:class|interface|enum|extends|implements|new)/,\n          /\\s+/,\n          JAVA_IDENT_RE\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n      {\n        // Exceptions for hyphenated keywords\n        match: /non-sealed/,\n        scope: \"keyword\"\n      },\n      {\n        begin: [\n          regex.concat(/(?!else)/, JAVA_IDENT_RE),\n          /\\s+/,\n          JAVA_IDENT_RE,\n          /\\s+/,\n          /=(?!=)/\n        ],\n        className: {\n          1: \"type\",\n          3: \"variable\",\n          5: \"operator\"\n        }\n      },\n      {\n        begin: [\n          /record/,\n          /\\s+/,\n          JAVA_IDENT_RE\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"title.class\"\n        },\n        contains: [\n          PARAMS,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        // Expression keywords prevent 'keyword Name(...)' from being\n        // recognized as a function definition\n        beginKeywords: 'new throw return else',\n        relevance: 0\n      },\n      {\n        begin: [\n          '(?:' + GENERIC_IDENT_RE + '\\\\s+)',\n          hljs.UNDERSCORE_IDENT_RE,\n          /\\s*(?=\\()/\n        ],\n        className: { 2: \"title.function\" },\n        keywords: KEYWORDS,\n        contains: [\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              ANNOTATION,\n              hljs.APOS_STRING_MODE,\n              hljs.QUOTE_STRING_MODE,\n              NUMERIC,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      NUMERIC,\n      ANNOTATION\n    ]\n  };\n}\n\nmodule.exports = java;\n", "const IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\nconst KEYWORDS = [\n  \"as\", // for exports\n  \"in\",\n  \"of\",\n  \"if\",\n  \"for\",\n  \"while\",\n  \"finally\",\n  \"var\",\n  \"new\",\n  \"function\",\n  \"do\",\n  \"return\",\n  \"void\",\n  \"else\",\n  \"break\",\n  \"catch\",\n  \"instanceof\",\n  \"with\",\n  \"throw\",\n  \"case\",\n  \"default\",\n  \"try\",\n  \"switch\",\n  \"continue\",\n  \"typeof\",\n  \"delete\",\n  \"let\",\n  \"yield\",\n  \"const\",\n  \"class\",\n  // J<PERSON> handles these with a special rule\n  // \"get\",\n  // \"set\",\n  \"debugger\",\n  \"async\",\n  \"await\",\n  \"static\",\n  \"import\",\n  \"from\",\n  \"export\",\n  \"extends\"\n];\nconst LITERALS = [\n  \"true\",\n  \"false\",\n  \"null\",\n  \"undefined\",\n  \"NaN\",\n  \"Infinity\"\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects\nconst TYPES = [\n  // Fundamental objects\n  \"Object\",\n  \"Function\",\n  \"Boolean\",\n  \"Symbol\",\n  // numbers and dates\n  \"Math\",\n  \"Date\",\n  \"Number\",\n  \"BigInt\",\n  // text\n  \"String\",\n  \"RegExp\",\n  // Indexed collections\n  \"Array\",\n  \"Float32Array\",\n  \"Float64Array\",\n  \"Int8Array\",\n  \"Uint8Array\",\n  \"Uint8ClampedArray\",\n  \"Int16Array\",\n  \"Int32Array\",\n  \"Uint16Array\",\n  \"Uint32Array\",\n  \"BigInt64Array\",\n  \"BigUint64Array\",\n  // Keyed collections\n  \"Set\",\n  \"Map\",\n  \"WeakSet\",\n  \"WeakMap\",\n  // Structured data\n  \"ArrayBuffer\",\n  \"SharedArrayBuffer\",\n  \"Atomics\",\n  \"DataView\",\n  \"JSON\",\n  // Control abstraction objects\n  \"Promise\",\n  \"Generator\",\n  \"GeneratorFunction\",\n  \"AsyncFunction\",\n  // Reflection\n  \"Reflect\",\n  \"Proxy\",\n  // Internationalization\n  \"Intl\",\n  // WebAssembly\n  \"WebAssembly\"\n];\n\nconst ERROR_TYPES = [\n  \"Error\",\n  \"EvalError\",\n  \"InternalError\",\n  \"RangeError\",\n  \"ReferenceError\",\n  \"SyntaxError\",\n  \"TypeError\",\n  \"URIError\"\n];\n\nconst BUILT_IN_GLOBALS = [\n  \"setInterval\",\n  \"setTimeout\",\n  \"clearInterval\",\n  \"clearTimeout\",\n\n  \"require\",\n  \"exports\",\n\n  \"eval\",\n  \"isFinite\",\n  \"isNaN\",\n  \"parseFloat\",\n  \"parseInt\",\n  \"decodeURI\",\n  \"decodeURIComponent\",\n  \"encodeURI\",\n  \"encodeURIComponent\",\n  \"escape\",\n  \"unescape\"\n];\n\nconst BUILT_IN_VARIABLES = [\n  \"arguments\",\n  \"this\",\n  \"super\",\n  \"console\",\n  \"window\",\n  \"document\",\n  \"localStorage\",\n  \"sessionStorage\",\n  \"module\",\n  \"global\" // Node.js\n];\n\nconst BUILT_INS = [].concat(\n  BUILT_IN_GLOBALS,\n  TYPES,\n  ERROR_TYPES\n);\n\n/*\nLanguage: JavaScript\nDescription: JavaScript (JS) is a lightweight, interpreted, or just-in-time compiled programming language with first-class functions.\nCategory: common, scripting, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/JavaScript\n*/\n\n\n/** @type LanguageFn */\nfunction javascript(hljs) {\n  const regex = hljs.regex;\n  /**\n   * Takes a string like \"<Booger\" and checks to see\n   * if we can find a matching \"</Booger\" later in the\n   * content.\n   * @param {RegExpMatchArray} match\n   * @param {{after:number}} param1\n   */\n  const hasClosingTag = (match, { after }) => {\n    const tag = \"</\" + match[0].slice(1);\n    const pos = match.input.indexOf(tag, after);\n    return pos !== -1;\n  };\n\n  const IDENT_RE$1 = IDENT_RE;\n  const FRAGMENT = {\n    begin: '<>',\n    end: '</>'\n  };\n  // to avoid some special cases inside isTrulyOpeningTag\n  const XML_SELF_CLOSING = /<[A-Za-z0-9\\\\._:-]+\\s*\\/>/;\n  const XML_TAG = {\n    begin: /<[A-Za-z0-9\\\\._:-]+/,\n    end: /\\/[A-Za-z0-9\\\\._:-]+>|\\/>/,\n    /**\n     * @param {RegExpMatchArray} match\n     * @param {CallbackResponse} response\n     */\n    isTrulyOpeningTag: (match, response) => {\n      const afterMatchIndex = match[0].length + match.index;\n      const nextChar = match.input[afterMatchIndex];\n      if (\n        // HTML should not include another raw `<` inside a tag\n        // nested type?\n        // `<Array<Array<number>>`, etc.\n        nextChar === \"<\" ||\n        // the , gives away that this is not HTML\n        // `<T, A extends keyof T, V>`\n        nextChar === \",\"\n        ) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<something>`\n      // Quite possibly a tag, lets look for a matching closing tag...\n      if (nextChar === \">\") {\n        // if we cannot find a matching closing tag, then we\n        // will ignore it\n        if (!hasClosingTag(match, { after: afterMatchIndex })) {\n          response.ignoreMatch();\n        }\n      }\n\n      // `<blah />` (self-closing)\n      // handled by simpleSelfClosing rule\n\n      let m;\n      const afterMatch = match.input.substring(afterMatchIndex);\n\n      // some more template typing stuff\n      //  <T = any>(key?: string) => Modify<\n      if ((m = afterMatch.match(/^\\s*=/))) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<From extends string>`\n      // technically this could be HTML, but it smells like a type\n      // NOTE: This is ugh, but added specifically for https://github.com/highlightjs/highlight.js/issues/3276\n      if ((m = afterMatch.match(/^\\s+extends\\s+/))) {\n        if (m.index === 0) {\n          response.ignoreMatch();\n          // eslint-disable-next-line no-useless-return\n          return;\n        }\n      }\n    }\n  };\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_INS,\n    \"variable.language\": BUILT_IN_VARIABLES\n  };\n\n  // https://tc39.es/ecma262/#sec-literals-numeric-literals\n  const decimalDigits = '[0-9](_?[0-9])*';\n  const frac = `\\\\.(${decimalDigits})`;\n  // DecimalIntegerLiteral, including Annex B NonOctalDecimalIntegerLiteral\n  // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n  const decimalInteger = `0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*`;\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      // DecimalLiteral\n      { begin: `(\\\\b(${decimalInteger})((${frac})|\\\\.)?|(${frac}))` +\n        `[eE][+-]?(${decimalDigits})\\\\b` },\n      { begin: `\\\\b(${decimalInteger})\\\\b((${frac})\\\\b|\\\\.)?|(${frac})\\\\b` },\n\n      // DecimalBigIntegerLiteral\n      { begin: `\\\\b(0|[1-9](_?[0-9])*)n\\\\b` },\n\n      // NonDecimalIntegerLiteral\n      { begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\\\b\" },\n      { begin: \"\\\\b0[bB][0-1](_?[0-1])*n?\\\\b\" },\n      { begin: \"\\\\b0[oO][0-7](_?[0-7])*n?\\\\b\" },\n\n      // LegacyOctalIntegerLiteral (does not include underscore separators)\n      // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n      { begin: \"\\\\b0[0-7]+n?\\\\b\" },\n    ],\n    relevance: 0\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS$1,\n    contains: [] // defined later\n  };\n  const HTML_TEMPLATE = {\n    begin: 'html`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'xml'\n    }\n  };\n  const CSS_TEMPLATE = {\n    begin: 'css`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'css'\n    }\n  };\n  const GRAPHQL_TEMPLATE = {\n    begin: 'gql`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'graphql'\n    }\n  };\n  const TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  const JSDOC_COMMENT = hljs.COMMENT(\n    /\\/\\*\\*(?!\\/)/,\n    '\\\\*/',\n    {\n      relevance: 0,\n      contains: [\n        {\n          begin: '(?=@[A-Za-z]+)',\n          relevance: 0,\n          contains: [\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            },\n            {\n              className: 'type',\n              begin: '\\\\{',\n              end: '\\\\}',\n              excludeEnd: true,\n              excludeBegin: true,\n              relevance: 0\n            },\n            {\n              className: 'variable',\n              begin: IDENT_RE$1 + '(?=\\\\s*(-)|$)',\n              endsParent: true,\n              relevance: 0\n            },\n            // eat spaces (not newlines) so we can find\n            // types or variables\n            {\n              begin: /(?=[^\\n])\\s/,\n              relevance: 0\n            }\n          ]\n        }\n      ]\n    }\n  );\n  const COMMENT = {\n    className: \"comment\",\n    variants: [\n      JSDOC_COMMENT,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_LINE_COMMENT_MODE\n    ]\n  };\n  const SUBST_INTERNALS = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    HTML_TEMPLATE,\n    CSS_TEMPLATE,\n    GRAPHQL_TEMPLATE,\n    TEMPLATE_STRING,\n    // Skip numbers when they are part of a variable name\n    { match: /\\$\\d+/ },\n    NUMBER,\n    // This is intentional:\n    // See https://github.com/highlightjs/highlight.js/issues/3288\n    // hljs.REGEXP_MODE\n  ];\n  SUBST.contains = SUBST_INTERNALS\n    .concat({\n      // we need to pair up {} inside our subst to prevent\n      // it from ending too early by matching another }\n      begin: /\\{/,\n      end: /\\}/,\n      keywords: KEYWORDS$1,\n      contains: [\n        \"self\"\n      ].concat(SUBST_INTERNALS)\n    });\n  const SUBST_AND_COMMENTS = [].concat(COMMENT, SUBST.contains);\n  const PARAMS_CONTAINS = SUBST_AND_COMMENTS.concat([\n    // eat recursive parens in sub expressions\n    {\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS$1,\n      contains: [\"self\"].concat(SUBST_AND_COMMENTS)\n    }\n  ]);\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true,\n    keywords: KEYWORDS$1,\n    contains: PARAMS_CONTAINS\n  };\n\n  // ES6 classes\n  const CLASS_OR_EXTENDS = {\n    variants: [\n      // class Car extends vehicle\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1,\n          /\\s+/,\n          /extends/,\n          /\\s+/,\n          regex.concat(IDENT_RE$1, \"(\", regex.concat(/\\./, IDENT_RE$1), \")*\")\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\",\n          5: \"keyword\",\n          7: \"title.class.inherited\"\n        }\n      },\n      // class Car\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n\n    ]\n  };\n\n  const CLASS_REFERENCE = {\n    relevance: 0,\n    match:\n    regex.either(\n      // Hard coded exceptions\n      /\\bJSON/,\n      // Float32Array, OutT\n      /\\b[A-Z][a-z]+([A-Z][a-z]*|\\d)*/,\n      // CSSFactory, CSSFactoryT\n      /\\b[A-Z]{2,}([A-Z][a-z]+|\\d)+([A-Z][a-z]*)*/,\n      // FPs, FPsT\n      /\\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\\d)*([A-Z][a-z]*)*/,\n      // P\n      // single letters are not highlighted\n      // BLAH\n      // this will be flagged as a UPPER_CASE_CONSTANT instead\n    ),\n    className: \"title.class\",\n    keywords: {\n      _: [\n        // se we still get relevance credit for JS library classes\n        ...TYPES,\n        ...ERROR_TYPES\n      ]\n    }\n  };\n\n  const USE_STRICT = {\n    label: \"use_strict\",\n    className: 'meta',\n    relevance: 10,\n    begin: /^\\s*['\"]use (strict|asm)['\"]/\n  };\n\n  const FUNCTION_DEFINITION = {\n    variants: [\n      {\n        match: [\n          /function/,\n          /\\s+/,\n          IDENT_RE$1,\n          /(?=\\s*\\()/\n        ]\n      },\n      // anonymous function\n      {\n        match: [\n          /function/,\n          /\\s*(?=\\()/\n        ]\n      }\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    label: \"func.def\",\n    contains: [ PARAMS ],\n    illegal: /%/\n  };\n\n  const UPPER_CASE_CONSTANT = {\n    relevance: 0,\n    match: /\\b[A-Z][A-Z_0-9]+\\b/,\n    className: \"variable.constant\"\n  };\n\n  function noneOf(list) {\n    return regex.concat(\"(?!\", list.join(\"|\"), \")\");\n  }\n\n  const FUNCTION_CALL = {\n    match: regex.concat(\n      /\\b/,\n      noneOf([\n        ...BUILT_IN_GLOBALS,\n        \"super\",\n        \"import\"\n      ]),\n      IDENT_RE$1, regex.lookahead(/\\(/)),\n    className: \"title.function\",\n    relevance: 0\n  };\n\n  const PROPERTY_ACCESS = {\n    begin: regex.concat(/\\./, regex.lookahead(\n      regex.concat(IDENT_RE$1, /(?![0-9A-Za-z$_(])/)\n    )),\n    end: IDENT_RE$1,\n    excludeBegin: true,\n    keywords: \"prototype\",\n    className: \"property\",\n    relevance: 0\n  };\n\n  const GETTER_OR_SETTER = {\n    match: [\n      /get|set/,\n      /\\s+/,\n      IDENT_RE$1,\n      /(?=\\()/\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      { // eat to avoid empty params\n        begin: /\\(\\)/\n      },\n      PARAMS\n    ]\n  };\n\n  const FUNC_LEAD_IN_RE = '(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)|' + hljs.UNDERSCORE_IDENT_RE + ')\\\\s*=>';\n\n  const FUNCTION_VARIABLE = {\n    match: [\n      /const|var|let/, /\\s+/,\n      IDENT_RE$1, /\\s*/,\n      /=\\s*/,\n      /(async\\s*)?/, // async is optional\n      regex.lookahead(FUNC_LEAD_IN_RE)\n    ],\n    keywords: \"async\",\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      PARAMS\n    ]\n  };\n\n  return {\n    name: 'JavaScript',\n    aliases: ['js', 'jsx', 'mjs', 'cjs'],\n    keywords: KEYWORDS$1,\n    // this will be extended by TypeScript\n    exports: { PARAMS_CONTAINS, CLASS_REFERENCE },\n    illegal: /#(?![$_A-z])/,\n    contains: [\n      hljs.SHEBANG({\n        label: \"shebang\",\n        binary: \"node\",\n        relevance: 5\n      }),\n      USE_STRICT,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      HTML_TEMPLATE,\n      CSS_TEMPLATE,\n      GRAPHQL_TEMPLATE,\n      TEMPLATE_STRING,\n      COMMENT,\n      // Skip numbers when they are part of a variable name\n      { match: /\\$\\d+/ },\n      NUMBER,\n      CLASS_REFERENCE,\n      {\n        className: 'attr',\n        begin: IDENT_RE$1 + regex.lookahead(':'),\n        relevance: 0\n      },\n      FUNCTION_VARIABLE,\n      { // \"value\" container\n        begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n        keywords: 'return throw case',\n        relevance: 0,\n        contains: [\n          COMMENT,\n          hljs.REGEXP_MODE,\n          {\n            className: 'function',\n            // we have to count the parens to make sure we actually have the\n            // correct bounding ( ) before the =>.  There could be any number of\n            // sub-expressions inside also surrounded by parens.\n            begin: FUNC_LEAD_IN_RE,\n            returnBegin: true,\n            end: '\\\\s*=>',\n            contains: [\n              {\n                className: 'params',\n                variants: [\n                  {\n                    begin: hljs.UNDERSCORE_IDENT_RE,\n                    relevance: 0\n                  },\n                  {\n                    className: null,\n                    begin: /\\(\\s*\\)/,\n                    skip: true\n                  },\n                  {\n                    begin: /\\(/,\n                    end: /\\)/,\n                    excludeBegin: true,\n                    excludeEnd: true,\n                    keywords: KEYWORDS$1,\n                    contains: PARAMS_CONTAINS\n                  }\n                ]\n              }\n            ]\n          },\n          { // could be a comma delimited list of params to a function call\n            begin: /,/,\n            relevance: 0\n          },\n          {\n            match: /\\s+/,\n            relevance: 0\n          },\n          { // JSX\n            variants: [\n              { begin: FRAGMENT.begin, end: FRAGMENT.end },\n              { match: XML_SELF_CLOSING },\n              {\n                begin: XML_TAG.begin,\n                // we carefully check the opening tag to see if it truly\n                // is a tag and not a false positive\n                'on:begin': XML_TAG.isTrulyOpeningTag,\n                end: XML_TAG.end\n              }\n            ],\n            subLanguage: 'xml',\n            contains: [\n              {\n                begin: XML_TAG.begin,\n                end: XML_TAG.end,\n                skip: true,\n                contains: ['self']\n              }\n            ]\n          }\n        ],\n      },\n      FUNCTION_DEFINITION,\n      {\n        // prevent this from getting swallowed up by function\n        // since they appear \"function like\"\n        beginKeywords: \"while if switch catch for\"\n      },\n      {\n        // we have to count the parens to make sure we actually have the correct\n        // bounding ( ).  There could be any number of sub-expressions inside\n        // also surrounded by parens.\n        begin: '\\\\b(?!function)' + hljs.UNDERSCORE_IDENT_RE +\n          '\\\\(' + // first parens\n          '[^()]*(\\\\(' +\n            '[^()]*(\\\\(' +\n              '[^()]*' +\n            '\\\\)[^()]*)*' +\n          '\\\\)[^()]*)*' +\n          '\\\\)\\\\s*\\\\{', // end parens\n        returnBegin:true,\n        label: \"func.def\",\n        contains: [\n          PARAMS,\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1, className: \"title.function\" })\n        ]\n      },\n      // catch ... so it won't trigger the property rule below\n      {\n        match: /\\.\\.\\./,\n        relevance: 0\n      },\n      PROPERTY_ACCESS,\n      // hack: prevents detection of keywords in some circumstances\n      // .keyword()\n      // $keyword = x\n      {\n        match: '\\\\$' + IDENT_RE$1,\n        relevance: 0\n      },\n      {\n        match: [ /\\bconstructor(?=\\s*\\()/ ],\n        className: { 1: \"title.function\" },\n        contains: [ PARAMS ]\n      },\n      FUNCTION_CALL,\n      UPPER_CASE_CONSTANT,\n      CLASS_OR_EXTENDS,\n      GETTER_OR_SETTER,\n      {\n        match: /\\$[(.]/ // relevance booster for a pattern common to JS libs: `$(something)` and `$.something`\n      }\n    ]\n  };\n}\n\nmodule.exports = javascript;\n", "/*\nLanguage: JSON\nDescription: J<PERSON><PERSON> (JavaScript Object Notation) is a lightweight data-interchange format.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://www.json.org\nCategory: common, protocols, web\n*/\n\nfunction json(hljs) {\n  const ATTRIBUTE = {\n    className: 'attr',\n    begin: /\"(\\\\.|[^\\\\\"\\r\\n])*\"(?=\\s*:)/,\n    relevance: 1.01\n  };\n  const PUNCTUATION = {\n    match: /[{}[\\],:]/,\n    className: \"punctuation\",\n    relevance: 0\n  };\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"null\"\n  ];\n  // NOTE: normally we would rely on `keywords` for this but using a mode here allows us\n  // - to use the very tight `illegal: \\S` rule later to flag any other character\n  // - as illegal indicating that despite looking like JSON we do not truly have\n  // - JSON and thus improve false-positively greatly since <PERSON><PERSON><PERSON> will try and claim\n  // - all sorts of JSON looking stuff\n  const LITERALS_MODE = {\n    scope: \"literal\",\n    beginKeywords: LITERALS.join(\" \"),\n  };\n\n  return {\n    name: '<PERSON><PERSON><PERSON>',\n    keywords:{\n      literal: LITERALS,\n    },\n    contains: [\n      ATTRIBUTE,\n      PUNCTUATION,\n      hljs.QUOTE_STRING_MODE,\n      LITERALS_MODE,\n      hljs.C_NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ],\n    illegal: '\\\\S'\n  };\n}\n\nmodule.exports = json;\n", "// https://docs.oracle.com/javase/specs/jls/se15/html/jls-3.html#jls-3.10\nvar decimalDigits = '[0-9](_*[0-9])*';\nvar frac = `\\\\.(${decimalDigits})`;\nvar hexDigits = '[0-9a-fA-F](_*[0-9a-fA-F])*';\nvar NUMERIC = {\n  className: 'number',\n  variants: [\n    // DecimalFloatingPointLiteral\n    // including ExponentPart\n    { begin: `(\\\\b(${decimalDigits})((${frac})|\\\\.)?|(${frac}))` +\n      `[eE][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n    // excluding ExponentPart\n    { begin: `\\\\b(${decimalDigits})((${frac})[fFdD]?\\\\b|\\\\.([fFdD]\\\\b)?)` },\n    { begin: `(${frac})[fFdD]?\\\\b` },\n    { begin: `\\\\b(${decimalDigits})[fFdD]\\\\b` },\n\n    // HexadecimalFloatingPointLiteral\n    { begin: `\\\\b0[xX]((${hexDigits})\\\\.?|(${hexDigits})?\\\\.(${hexDigits}))` +\n      `[pP][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n\n    // DecimalIntegerLiteral\n    { begin: '\\\\b(0|[1-9](_*[0-9])*)[lL]?\\\\b' },\n\n    // HexIntegerLiteral\n    { begin: `\\\\b0[xX](${hexDigits})[lL]?\\\\b` },\n\n    // OctalIntegerLiteral\n    { begin: '\\\\b0(_*[0-7])*[lL]?\\\\b' },\n\n    // BinaryIntegerLiteral\n    { begin: '\\\\b0[bB][01](_*[01])*[lL]?\\\\b' },\n  ],\n  relevance: 0\n};\n\n/*\n Language: Kotlin\n Description: Kotlin is an OSS statically typed programming language that targets the JVM, Android, JavaScript and Native.\n Author: Sergey Mashkov <<EMAIL>>\n Website: https://kotlinlang.org\n Category: common\n */\n\n\nfunction kotlin(hljs) {\n  const KEYWORDS = {\n    keyword:\n      'abstract as val var vararg get set class object open private protected public noinline '\n      + 'crossinline dynamic final enum if else do while for when throw try catch finally '\n      + 'import package is in fun override companion reified inline lateinit init '\n      + 'interface annotation data sealed internal infix operator out by constructor super '\n      + 'tailrec where const inner suspend typealias external expect actual',\n    built_in:\n      'Byte Short Char Int Long Boolean Float Double Void Unit Nothing',\n    literal:\n      'true false null'\n  };\n  const KEYWORDS_WITH_LABEL = {\n    className: 'keyword',\n    begin: /\\b(break|continue|return|this)\\b/,\n    starts: { contains: [\n      {\n        className: 'symbol',\n        begin: /@\\w+/\n      }\n    ] }\n  };\n  const LABEL = {\n    className: 'symbol',\n    begin: hljs.UNDERSCORE_IDENT_RE + '@'\n  };\n\n  // for string templates\n  const SUBST = {\n    className: 'subst',\n    begin: /\\$\\{/,\n    end: /\\}/,\n    contains: [ hljs.C_NUMBER_MODE ]\n  };\n  const VARIABLE = {\n    className: 'variable',\n    begin: '\\\\$' + hljs.UNDERSCORE_IDENT_RE\n  };\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: '\"\"\"',\n        end: '\"\"\"(?=[^\"])',\n        contains: [\n          VARIABLE,\n          SUBST\n        ]\n      },\n      // Can't use built-in modes easily, as we want to use STRING in the meta\n      // context as 'meta-string' and there's no syntax to remove explicitly set\n      // classNames in built-in modes.\n      {\n        begin: '\\'',\n        end: '\\'',\n        illegal: /\\n/,\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: '\"',\n        end: '\"',\n        illegal: /\\n/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          VARIABLE,\n          SUBST\n        ]\n      }\n    ]\n  };\n  SUBST.contains.push(STRING);\n\n  const ANNOTATION_USE_SITE = {\n    className: 'meta',\n    begin: '@(?:file|property|field|get|set|receiver|param|setparam|delegate)\\\\s*:(?:\\\\s*' + hljs.UNDERSCORE_IDENT_RE + ')?'\n  };\n  const ANNOTATION = {\n    className: 'meta',\n    begin: '@' + hljs.UNDERSCORE_IDENT_RE,\n    contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [\n          hljs.inherit(STRING, { className: 'string' }),\n          \"self\"\n        ]\n      }\n    ]\n  };\n\n  // https://kotlinlang.org/docs/reference/whatsnew11.html#underscores-in-numeric-literals\n  // According to the doc above, the number mode of kotlin is the same as java 8,\n  // so the code below is copied from java.js\n  const KOTLIN_NUMBER_MODE = NUMERIC;\n  const KOTLIN_NESTED_COMMENT = hljs.COMMENT(\n    '/\\\\*', '\\\\*/',\n    { contains: [ hljs.C_BLOCK_COMMENT_MODE ] }\n  );\n  const KOTLIN_PAREN_TYPE = { variants: [\n    {\n      className: 'type',\n      begin: hljs.UNDERSCORE_IDENT_RE\n    },\n    {\n      begin: /\\(/,\n      end: /\\)/,\n      contains: [] // defined later\n    }\n  ] };\n  const KOTLIN_PAREN_TYPE2 = KOTLIN_PAREN_TYPE;\n  KOTLIN_PAREN_TYPE2.variants[1].contains = [ KOTLIN_PAREN_TYPE ];\n  KOTLIN_PAREN_TYPE.variants[1].contains = [ KOTLIN_PAREN_TYPE2 ];\n\n  return {\n    name: 'Kotlin',\n    aliases: [\n      'kt',\n      'kts'\n    ],\n    keywords: KEYWORDS,\n    contains: [\n      hljs.COMMENT(\n        '/\\\\*\\\\*',\n        '\\\\*/',\n        {\n          relevance: 0,\n          contains: [\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            }\n          ]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      KOTLIN_NESTED_COMMENT,\n      KEYWORDS_WITH_LABEL,\n      LABEL,\n      ANNOTATION_USE_SITE,\n      ANNOTATION,\n      {\n        className: 'function',\n        beginKeywords: 'fun',\n        end: '[(]|$',\n        returnBegin: true,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        relevance: 5,\n        contains: [\n          {\n            begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n            returnBegin: true,\n            relevance: 0,\n            contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n          },\n          {\n            className: 'type',\n            begin: /</,\n            end: />/,\n            keywords: 'reified',\n            relevance: 0\n          },\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            endsParent: true,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              {\n                begin: /:/,\n                end: /[=,\\/]/,\n                endsWithParent: true,\n                contains: [\n                  KOTLIN_PAREN_TYPE,\n                  hljs.C_LINE_COMMENT_MODE,\n                  KOTLIN_NESTED_COMMENT\n                ],\n                relevance: 0\n              },\n              hljs.C_LINE_COMMENT_MODE,\n              KOTLIN_NESTED_COMMENT,\n              ANNOTATION_USE_SITE,\n              ANNOTATION,\n              STRING,\n              hljs.C_NUMBER_MODE\n            ]\n          },\n          KOTLIN_NESTED_COMMENT\n        ]\n      },\n      {\n        begin: [\n          /class|interface|trait/,\n          /\\s+/,\n          hljs.UNDERSCORE_IDENT_RE\n        ],\n        beginScope: {\n          3: \"title.class\"\n        },\n        keywords: 'class interface trait',\n        end: /[:\\{(]|$/,\n        excludeEnd: true,\n        illegal: 'extends implements',\n        contains: [\n          { beginKeywords: 'public protected internal private constructor' },\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            className: 'type',\n            begin: /</,\n            end: />/,\n            excludeBegin: true,\n            excludeEnd: true,\n            relevance: 0\n          },\n          {\n            className: 'type',\n            begin: /[,:]\\s*/,\n            end: /[<\\(,){\\s]|$/,\n            excludeBegin: true,\n            returnEnd: true\n          },\n          ANNOTATION_USE_SITE,\n          ANNOTATION\n        ]\n      },\n      STRING,\n      {\n        className: 'meta',\n        begin: \"^#!/usr/bin/env\",\n        end: '$',\n        illegal: '\\n'\n      },\n      KOTLIN_NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = kotlin;\n", "const MODES = (hljs) => {\n  return {\n    IMPORTANT: {\n      scope: 'meta',\n      begin: '!important'\n    },\n    BLOCK_COMMENT: hljs.C_BLOCK_COMMENT_MODE,\n    HEXCOLOR: {\n      scope: 'number',\n      begin: /#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\\b/\n    },\n    FUNCTION_DISPATCH: {\n      className: \"built_in\",\n      begin: /[\\w-]+(?=\\()/\n    },\n    ATTRIBUTE_SELECTOR_MODE: {\n      scope: 'selector-attr',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: '$',\n      contains: [\n        hljs.APOS_STRING_MODE,\n        hljs.QUOTE_STRING_MODE\n      ]\n    },\n    CSS_NUMBER_MODE: {\n      scope: 'number',\n      begin: hljs.NUMBER_RE + '(' +\n        '%|em|ex|ch|rem' +\n        '|vw|vh|vmin|vmax' +\n        '|cm|mm|in|pt|pc|px' +\n        '|deg|grad|rad|turn' +\n        '|s|ms' +\n        '|Hz|kHz' +\n        '|dpi|dpcm|dppx' +\n        ')?',\n      relevance: 0\n    },\n    CSS_VARIABLE: {\n      className: \"attr\",\n      begin: /--[A-Za-z_][A-Za-z0-9_-]*/\n    }\n  };\n};\n\nconst TAGS = [\n  'a',\n  'abbr',\n  'address',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'blockquote',\n  'body',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'mark',\n  'menu',\n  'nav',\n  'object',\n  'ol',\n  'p',\n  'q',\n  'quote',\n  'samp',\n  'section',\n  'span',\n  'strong',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'ul',\n  'var',\n  'video'\n];\n\nconst MEDIA_FEATURES = [\n  'any-hover',\n  'any-pointer',\n  'aspect-ratio',\n  'color',\n  'color-gamut',\n  'color-index',\n  'device-aspect-ratio',\n  'device-height',\n  'device-width',\n  'display-mode',\n  'forced-colors',\n  'grid',\n  'height',\n  'hover',\n  'inverted-colors',\n  'monochrome',\n  'orientation',\n  'overflow-block',\n  'overflow-inline',\n  'pointer',\n  'prefers-color-scheme',\n  'prefers-contrast',\n  'prefers-reduced-motion',\n  'prefers-reduced-transparency',\n  'resolution',\n  'scan',\n  'scripting',\n  'update',\n  'width',\n  // TODO: find a better solution?\n  'min-width',\n  'max-width',\n  'min-height',\n  'max-height'\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes\nconst PSEUDO_CLASSES = [\n  'active',\n  'any-link',\n  'blank',\n  'checked',\n  'current',\n  'default',\n  'defined',\n  'dir', // dir()\n  'disabled',\n  'drop',\n  'empty',\n  'enabled',\n  'first',\n  'first-child',\n  'first-of-type',\n  'fullscreen',\n  'future',\n  'focus',\n  'focus-visible',\n  'focus-within',\n  'has', // has()\n  'host', // host or host()\n  'host-context', // host-context()\n  'hover',\n  'indeterminate',\n  'in-range',\n  'invalid',\n  'is', // is()\n  'lang', // lang()\n  'last-child',\n  'last-of-type',\n  'left',\n  'link',\n  'local-link',\n  'not', // not()\n  'nth-child', // nth-child()\n  'nth-col', // nth-col()\n  'nth-last-child', // nth-last-child()\n  'nth-last-col', // nth-last-col()\n  'nth-last-of-type', //nth-last-of-type()\n  'nth-of-type', //nth-of-type()\n  'only-child',\n  'only-of-type',\n  'optional',\n  'out-of-range',\n  'past',\n  'placeholder-shown',\n  'read-only',\n  'read-write',\n  'required',\n  'right',\n  'root',\n  'scope',\n  'target',\n  'target-within',\n  'user-invalid',\n  'valid',\n  'visited',\n  'where' // where()\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-elements\nconst PSEUDO_ELEMENTS = [\n  'after',\n  'backdrop',\n  'before',\n  'cue',\n  'cue-region',\n  'first-letter',\n  'first-line',\n  'grammar-error',\n  'marker',\n  'part',\n  'placeholder',\n  'selection',\n  'slotted',\n  'spelling-error'\n];\n\nconst ATTRIBUTES = [\n  'align-content',\n  'align-items',\n  'align-self',\n  'all',\n  'animation',\n  'animation-delay',\n  'animation-direction',\n  'animation-duration',\n  'animation-fill-mode',\n  'animation-iteration-count',\n  'animation-name',\n  'animation-play-state',\n  'animation-timing-function',\n  'backface-visibility',\n  'background',\n  'background-attachment',\n  'background-blend-mode',\n  'background-clip',\n  'background-color',\n  'background-image',\n  'background-origin',\n  'background-position',\n  'background-repeat',\n  'background-size',\n  'block-size',\n  'border',\n  'border-block',\n  'border-block-color',\n  'border-block-end',\n  'border-block-end-color',\n  'border-block-end-style',\n  'border-block-end-width',\n  'border-block-start',\n  'border-block-start-color',\n  'border-block-start-style',\n  'border-block-start-width',\n  'border-block-style',\n  'border-block-width',\n  'border-bottom',\n  'border-bottom-color',\n  'border-bottom-left-radius',\n  'border-bottom-right-radius',\n  'border-bottom-style',\n  'border-bottom-width',\n  'border-collapse',\n  'border-color',\n  'border-image',\n  'border-image-outset',\n  'border-image-repeat',\n  'border-image-slice',\n  'border-image-source',\n  'border-image-width',\n  'border-inline',\n  'border-inline-color',\n  'border-inline-end',\n  'border-inline-end-color',\n  'border-inline-end-style',\n  'border-inline-end-width',\n  'border-inline-start',\n  'border-inline-start-color',\n  'border-inline-start-style',\n  'border-inline-start-width',\n  'border-inline-style',\n  'border-inline-width',\n  'border-left',\n  'border-left-color',\n  'border-left-style',\n  'border-left-width',\n  'border-radius',\n  'border-right',\n  'border-right-color',\n  'border-right-style',\n  'border-right-width',\n  'border-spacing',\n  'border-style',\n  'border-top',\n  'border-top-color',\n  'border-top-left-radius',\n  'border-top-right-radius',\n  'border-top-style',\n  'border-top-width',\n  'border-width',\n  'bottom',\n  'box-decoration-break',\n  'box-shadow',\n  'box-sizing',\n  'break-after',\n  'break-before',\n  'break-inside',\n  'caption-side',\n  'caret-color',\n  'clear',\n  'clip',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'column-count',\n  'column-fill',\n  'column-gap',\n  'column-rule',\n  'column-rule-color',\n  'column-rule-style',\n  'column-rule-width',\n  'column-span',\n  'column-width',\n  'columns',\n  'contain',\n  'content',\n  'content-visibility',\n  'counter-increment',\n  'counter-reset',\n  'cue',\n  'cue-after',\n  'cue-before',\n  'cursor',\n  'direction',\n  'display',\n  'empty-cells',\n  'filter',\n  'flex',\n  'flex-basis',\n  'flex-direction',\n  'flex-flow',\n  'flex-grow',\n  'flex-shrink',\n  'flex-wrap',\n  'float',\n  'flow',\n  'font',\n  'font-display',\n  'font-family',\n  'font-feature-settings',\n  'font-kerning',\n  'font-language-override',\n  'font-size',\n  'font-size-adjust',\n  'font-smoothing',\n  'font-stretch',\n  'font-style',\n  'font-synthesis',\n  'font-variant',\n  'font-variant-caps',\n  'font-variant-east-asian',\n  'font-variant-ligatures',\n  'font-variant-numeric',\n  'font-variant-position',\n  'font-variation-settings',\n  'font-weight',\n  'gap',\n  'glyph-orientation-vertical',\n  'grid',\n  'grid-area',\n  'grid-auto-columns',\n  'grid-auto-flow',\n  'grid-auto-rows',\n  'grid-column',\n  'grid-column-end',\n  'grid-column-start',\n  'grid-gap',\n  'grid-row',\n  'grid-row-end',\n  'grid-row-start',\n  'grid-template',\n  'grid-template-areas',\n  'grid-template-columns',\n  'grid-template-rows',\n  'hanging-punctuation',\n  'height',\n  'hyphens',\n  'icon',\n  'image-orientation',\n  'image-rendering',\n  'image-resolution',\n  'ime-mode',\n  'inline-size',\n  'isolation',\n  'justify-content',\n  'left',\n  'letter-spacing',\n  'line-break',\n  'line-height',\n  'list-style',\n  'list-style-image',\n  'list-style-position',\n  'list-style-type',\n  'margin',\n  'margin-block',\n  'margin-block-end',\n  'margin-block-start',\n  'margin-bottom',\n  'margin-inline',\n  'margin-inline-end',\n  'margin-inline-start',\n  'margin-left',\n  'margin-right',\n  'margin-top',\n  'marks',\n  'mask',\n  'mask-border',\n  'mask-border-mode',\n  'mask-border-outset',\n  'mask-border-repeat',\n  'mask-border-slice',\n  'mask-border-source',\n  'mask-border-width',\n  'mask-clip',\n  'mask-composite',\n  'mask-image',\n  'mask-mode',\n  'mask-origin',\n  'mask-position',\n  'mask-repeat',\n  'mask-size',\n  'mask-type',\n  'max-block-size',\n  'max-height',\n  'max-inline-size',\n  'max-width',\n  'min-block-size',\n  'min-height',\n  'min-inline-size',\n  'min-width',\n  'mix-blend-mode',\n  'nav-down',\n  'nav-index',\n  'nav-left',\n  'nav-right',\n  'nav-up',\n  'none',\n  'normal',\n  'object-fit',\n  'object-position',\n  'opacity',\n  'order',\n  'orphans',\n  'outline',\n  'outline-color',\n  'outline-offset',\n  'outline-style',\n  'outline-width',\n  'overflow',\n  'overflow-wrap',\n  'overflow-x',\n  'overflow-y',\n  'padding',\n  'padding-block',\n  'padding-block-end',\n  'padding-block-start',\n  'padding-bottom',\n  'padding-inline',\n  'padding-inline-end',\n  'padding-inline-start',\n  'padding-left',\n  'padding-right',\n  'padding-top',\n  'page-break-after',\n  'page-break-before',\n  'page-break-inside',\n  'pause',\n  'pause-after',\n  'pause-before',\n  'perspective',\n  'perspective-origin',\n  'pointer-events',\n  'position',\n  'quotes',\n  'resize',\n  'rest',\n  'rest-after',\n  'rest-before',\n  'right',\n  'row-gap',\n  'scroll-margin',\n  'scroll-margin-block',\n  'scroll-margin-block-end',\n  'scroll-margin-block-start',\n  'scroll-margin-bottom',\n  'scroll-margin-inline',\n  'scroll-margin-inline-end',\n  'scroll-margin-inline-start',\n  'scroll-margin-left',\n  'scroll-margin-right',\n  'scroll-margin-top',\n  'scroll-padding',\n  'scroll-padding-block',\n  'scroll-padding-block-end',\n  'scroll-padding-block-start',\n  'scroll-padding-bottom',\n  'scroll-padding-inline',\n  'scroll-padding-inline-end',\n  'scroll-padding-inline-start',\n  'scroll-padding-left',\n  'scroll-padding-right',\n  'scroll-padding-top',\n  'scroll-snap-align',\n  'scroll-snap-stop',\n  'scroll-snap-type',\n  'scrollbar-color',\n  'scrollbar-gutter',\n  'scrollbar-width',\n  'shape-image-threshold',\n  'shape-margin',\n  'shape-outside',\n  'speak',\n  'speak-as',\n  'src', // @font-face\n  'tab-size',\n  'table-layout',\n  'text-align',\n  'text-align-all',\n  'text-align-last',\n  'text-combine-upright',\n  'text-decoration',\n  'text-decoration-color',\n  'text-decoration-line',\n  'text-decoration-style',\n  'text-emphasis',\n  'text-emphasis-color',\n  'text-emphasis-position',\n  'text-emphasis-style',\n  'text-indent',\n  'text-justify',\n  'text-orientation',\n  'text-overflow',\n  'text-rendering',\n  'text-shadow',\n  'text-transform',\n  'text-underline-position',\n  'top',\n  'transform',\n  'transform-box',\n  'transform-origin',\n  'transform-style',\n  'transition',\n  'transition-delay',\n  'transition-duration',\n  'transition-property',\n  'transition-timing-function',\n  'unicode-bidi',\n  'vertical-align',\n  'visibility',\n  'voice-balance',\n  'voice-duration',\n  'voice-family',\n  'voice-pitch',\n  'voice-range',\n  'voice-rate',\n  'voice-stress',\n  'voice-volume',\n  'white-space',\n  'widows',\n  'width',\n  'will-change',\n  'word-break',\n  'word-spacing',\n  'word-wrap',\n  'writing-mode',\n  'z-index'\n  // reverse makes sure longer attributes `font-weight` are matched fully\n  // instead of getting false positives on say `font`\n].reverse();\n\n// some grammars use them all as a single group\nconst PSEUDO_SELECTORS = PSEUDO_CLASSES.concat(PSEUDO_ELEMENTS);\n\n/*\nLanguage: Less\nDescription: It's CSS, with just a little more.\nAuthor:   Max Mikhailov <<EMAIL>>\nWebsite: http://lesscss.org\nCategory: common, css, web\n*/\n\n\n/** @type LanguageFn */\nfunction less(hljs) {\n  const modes = MODES(hljs);\n  const PSEUDO_SELECTORS$1 = PSEUDO_SELECTORS;\n\n  const AT_MODIFIERS = \"and or not only\";\n  const IDENT_RE = '[\\\\w-]+'; // yes, Less identifiers may begin with a digit\n  const INTERP_IDENT_RE = '(' + IDENT_RE + '|@\\\\{' + IDENT_RE + '\\\\})';\n\n  /* Generic Modes */\n\n  const RULES = []; const VALUE_MODES = []; // forward def. for recursive modes\n\n  const STRING_MODE = function(c) {\n    return {\n    // Less strings are not multiline (also include '~' for more consistent coloring of \"escaped\" strings)\n      className: 'string',\n      begin: '~?' + c + '.*?' + c\n    };\n  };\n\n  const IDENT_MODE = function(name, begin, relevance) {\n    return {\n      className: name,\n      begin: begin,\n      relevance: relevance\n    };\n  };\n\n  const AT_KEYWORDS = {\n    $pattern: /[a-z-]+/,\n    keyword: AT_MODIFIERS,\n    attribute: MEDIA_FEATURES.join(\" \")\n  };\n\n  const PARENS_MODE = {\n    // used only to properly balance nested parens inside mixin call, def. arg list\n    begin: '\\\\(',\n    end: '\\\\)',\n    contains: VALUE_MODES,\n    keywords: AT_KEYWORDS,\n    relevance: 0\n  };\n\n  // generic Less highlighter (used almost everywhere except selectors):\n  VALUE_MODES.push(\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    STRING_MODE(\"'\"),\n    STRING_MODE('\"'),\n    modes.CSS_NUMBER_MODE, // fixme: it does not include dot for numbers like .5em :(\n    {\n      begin: '(url|data-uri)\\\\(',\n      starts: {\n        className: 'string',\n        end: '[\\\\)\\\\n]',\n        excludeEnd: true\n      }\n    },\n    modes.HEXCOLOR,\n    PARENS_MODE,\n    IDENT_MODE('variable', '@@?' + IDENT_RE, 10),\n    IDENT_MODE('variable', '@\\\\{' + IDENT_RE + '\\\\}'),\n    IDENT_MODE('built_in', '~?`[^`]*?`'), // inline javascript (or whatever host language) *multiline* string\n    { // @media features (it’s here to not duplicate things in AT_RULE_MODE with extra PARENS_MODE overriding):\n      className: 'attribute',\n      begin: IDENT_RE + '\\\\s*:',\n      end: ':',\n      returnBegin: true,\n      excludeEnd: true\n    },\n    modes.IMPORTANT,\n    { beginKeywords: 'and not' },\n    modes.FUNCTION_DISPATCH\n  );\n\n  const VALUE_WITH_RULESETS = VALUE_MODES.concat({\n    begin: /\\{/,\n    end: /\\}/,\n    contains: RULES\n  });\n\n  const MIXIN_GUARD_MODE = {\n    beginKeywords: 'when',\n    endsWithParent: true,\n    contains: [ { beginKeywords: 'and not' } ].concat(VALUE_MODES) // using this form to override VALUE’s 'function' match\n  };\n\n  /* Rule-Level Modes */\n\n  const RULE_MODE = {\n    begin: INTERP_IDENT_RE + '\\\\s*:',\n    returnBegin: true,\n    end: /[;}]/,\n    relevance: 0,\n    contains: [\n      { begin: /-(webkit|moz|ms|o)-/ },\n      modes.CSS_VARIABLE,\n      {\n        className: 'attribute',\n        begin: '\\\\b(' + ATTRIBUTES.join('|') + ')\\\\b',\n        end: /(?=:)/,\n        starts: {\n          endsWithParent: true,\n          illegal: '[<=$]',\n          relevance: 0,\n          contains: VALUE_MODES\n        }\n      }\n    ]\n  };\n\n  const AT_RULE_MODE = {\n    className: 'keyword',\n    begin: '@(import|media|charset|font-face|(-[a-z]+-)?keyframes|supports|document|namespace|page|viewport|host)\\\\b',\n    starts: {\n      end: '[;{}]',\n      keywords: AT_KEYWORDS,\n      returnEnd: true,\n      contains: VALUE_MODES,\n      relevance: 0\n    }\n  };\n\n  // variable definitions and calls\n  const VAR_RULE_MODE = {\n    className: 'variable',\n    variants: [\n      // using more strict pattern for higher relevance to increase chances of Less detection.\n      // this is *the only* Less specific statement used in most of the sources, so...\n      // (we’ll still often loose to the css-parser unless there's '//' comment,\n      // simply because 1 variable just can't beat 99 properties :)\n      {\n        begin: '@' + IDENT_RE + '\\\\s*:',\n        relevance: 15\n      },\n      { begin: '@' + IDENT_RE }\n    ],\n    starts: {\n      end: '[;}]',\n      returnEnd: true,\n      contains: VALUE_WITH_RULESETS\n    }\n  };\n\n  const SELECTOR_MODE = {\n    // first parse unambiguous selectors (i.e. those not starting with tag)\n    // then fall into the scary lookahead-discriminator variant.\n    // this mode also handles mixin definitions and calls\n    variants: [\n      {\n        begin: '[\\\\.#:&\\\\[>]',\n        end: '[;{}]' // mixin calls end with ';'\n      },\n      {\n        begin: INTERP_IDENT_RE,\n        end: /\\{/\n      }\n    ],\n    returnBegin: true,\n    returnEnd: true,\n    illegal: '[<=\\'$\"]',\n    relevance: 0,\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      MIXIN_GUARD_MODE,\n      IDENT_MODE('keyword', 'all\\\\b'),\n      IDENT_MODE('variable', '@\\\\{' + IDENT_RE + '\\\\}'), // otherwise it’s identified as tag\n      \n      {\n        begin: '\\\\b(' + TAGS.join('|') + ')\\\\b',\n        className: 'selector-tag'\n      },\n      modes.CSS_NUMBER_MODE,\n      IDENT_MODE('selector-tag', INTERP_IDENT_RE, 0),\n      IDENT_MODE('selector-id', '#' + INTERP_IDENT_RE),\n      IDENT_MODE('selector-class', '\\\\.' + INTERP_IDENT_RE, 0),\n      IDENT_MODE('selector-tag', '&', 0),\n      modes.ATTRIBUTE_SELECTOR_MODE,\n      {\n        className: 'selector-pseudo',\n        begin: ':(' + PSEUDO_CLASSES.join('|') + ')'\n      },\n      {\n        className: 'selector-pseudo',\n        begin: ':(:)?(' + PSEUDO_ELEMENTS.join('|') + ')'\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        relevance: 0,\n        contains: VALUE_WITH_RULESETS\n      }, // argument list of parametric mixins\n      { begin: '!important' }, // eat !important after mixin call or it will be colored as tag\n      modes.FUNCTION_DISPATCH\n    ]\n  };\n\n  const PSEUDO_SELECTOR_MODE = {\n    begin: IDENT_RE + ':(:)?' + `(${PSEUDO_SELECTORS$1.join('|')})`,\n    returnBegin: true,\n    contains: [ SELECTOR_MODE ]\n  };\n\n  RULES.push(\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    AT_RULE_MODE,\n    VAR_RULE_MODE,\n    PSEUDO_SELECTOR_MODE,\n    RULE_MODE,\n    SELECTOR_MODE,\n    MIXIN_GUARD_MODE,\n    modes.FUNCTION_DISPATCH\n  );\n\n  return {\n    name: 'Less',\n    case_insensitive: true,\n    illegal: '[=>\\'/<($\"]',\n    contains: RULES\n  };\n}\n\nmodule.exports = less;\n", "/*\nLanguage: Lua\nDescription: Lua is a powerful, efficient, lightweight, embeddable scripting language.\nAuthor: <PERSON> <<EMAIL>>\nCategory: common, scripting\nWebsite: https://www.lua.org\n*/\n\nfunction lua(hljs) {\n  const OPENING_LONG_BRACKET = '\\\\[=*\\\\[';\n  const CLOSING_LONG_BRACKET = '\\\\]=*\\\\]';\n  const LONG_BRACKETS = {\n    begin: OPENING_LONG_BRACKET,\n    end: CLOSING_LONG_BRACKET,\n    contains: [ 'self' ]\n  };\n  const COMMENTS = [\n    hljs.COMMENT('--(?!' + OPENING_LONG_BRACKET + ')', '$'),\n    hljs.COMMENT(\n      '--' + OPENING_LONG_BRACKET,\n      CLOSING_LONG_BRACKET,\n      {\n        contains: [ LONG_BRACKETS ],\n        relevance: 10\n      }\n    )\n  ];\n  return {\n    name: '<PERSON><PERSON>',\n    keywords: {\n      $pattern: hljs.UNDERSCORE_IDENT_RE,\n      literal: \"true false nil\",\n      keyword: \"and break do else elseif end for goto if in local not or repeat return then until while\",\n      built_in:\n        // Metatags and globals:\n        '_G _ENV _VERSION __index __newindex __mode __call __metatable __tostring __len '\n        + '__gc __add __sub __mul __div __mod __pow __concat __unm __eq __lt __le assert '\n        // Standard methods and properties:\n        + 'collectgarbage dofile error getfenv getmetatable ipairs load loadfile loadstring '\n        + 'module next pairs pcall print rawequal rawget rawset require select setfenv '\n        + 'setmetatable tonumber tostring type unpack xpcall arg self '\n        // Library methods and properties (one line per library):\n        + 'coroutine resume yield status wrap create running debug getupvalue '\n        + 'debug sethook getmetatable gethook setmetatable setlocal traceback setfenv getinfo setupvalue getlocal getregistry getfenv '\n        + 'io lines write close flush open output type read stderr stdin input stdout popen tmpfile '\n        + 'math log max acos huge ldexp pi cos tanh pow deg tan cosh sinh random randomseed frexp ceil floor rad abs sqrt modf asin min mod fmod log10 atan2 exp sin atan '\n        + 'os exit setlocale date getenv difftime remove time clock tmpname rename execute package preload loadlib loaded loaders cpath config path seeall '\n        + 'string sub upper len gfind rep find match char dump gmatch reverse byte format gsub lower '\n        + 'table setn insert getn foreachi maxn foreach concat sort remove'\n    },\n    contains: COMMENTS.concat([\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: '\\\\)',\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, { begin: '([_a-zA-Z]\\\\w*\\\\.)*([_a-zA-Z]\\\\w*:)?[_a-zA-Z]\\\\w*' }),\n          {\n            className: 'params',\n            begin: '\\\\(',\n            endsWithParent: true,\n            contains: COMMENTS\n          }\n        ].concat(COMMENTS)\n      },\n      hljs.C_NUMBER_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'string',\n        begin: OPENING_LONG_BRACKET,\n        end: CLOSING_LONG_BRACKET,\n        contains: [ LONG_BRACKETS ],\n        relevance: 5\n      }\n    ])\n  };\n}\n\nmodule.exports = lua;\n", "/*\nLanguage: Makefile\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/make/manual/html_node/Introduction.html\nCategory: common\n*/\n\nfunction makefile(hljs) {\n  /* Variables: simple (eg $(var)) and special (eg $@) */\n  const VARIABLE = {\n    className: 'variable',\n    variants: [\n      {\n        begin: '\\\\$\\\\(' + hljs.UNDERSCORE_IDENT_RE + '\\\\)',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      { begin: /\\$[@%<?\\^\\+\\*]/ }\n    ]\n  };\n  /* Quoted string with variables inside */\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VARIABLE\n    ]\n  };\n  /* Function: $(func arg,...) */\n  const FUNC = {\n    className: 'variable',\n    begin: /\\$\\([\\w-]+\\s/,\n    end: /\\)/,\n    keywords: { built_in:\n        'subst patsubst strip findstring filter filter-out sort '\n        + 'word wordlist firstword lastword dir notdir suffix basename '\n        + 'addsuffix addprefix join wildcard realpath abspath error warning '\n        + 'shell origin flavor foreach if or and call eval file value' },\n    contains: [ VARIABLE ]\n  };\n  /* Variable assignment */\n  const ASSIGNMENT = { begin: '^' + hljs.UNDERSCORE_IDENT_RE + '\\\\s*(?=[:+?]?=)' };\n  /* Meta targets (.PHONY) */\n  const META = {\n    className: 'meta',\n    begin: /^\\.PHONY:/,\n    end: /$/,\n    keywords: {\n      $pattern: /[\\.\\w]+/,\n      keyword: '.PHONY'\n    }\n  };\n  /* Targets */\n  const TARGET = {\n    className: 'section',\n    begin: /^[^\\s]+:/,\n    end: /$/,\n    contains: [ VARIABLE ]\n  };\n  return {\n    name: 'Makefile',\n    aliases: [\n      'mk',\n      'mak',\n      'make',\n    ],\n    keywords: {\n      $pattern: /[\\w-]+/,\n      keyword: 'define endef undefine ifdef ifndef ifeq ifneq else endif '\n      + 'include -include sinclude override export unexport private vpath'\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      VARIABLE,\n      QUOTE_STRING,\n      FUNC,\n      ASSIGNMENT,\n      META,\n      TARGET\n    ]\n  };\n}\n\nmodule.exports = makefile;\n", "/*\nLanguage: Perl\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.perl.org\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction perl(hljs) {\n  const regex = hljs.regex;\n  const KEYWORDS = [\n    'abs',\n    'accept',\n    'alarm',\n    'and',\n    'atan2',\n    'bind',\n    'binmode',\n    'bless',\n    'break',\n    'caller',\n    'chdir',\n    'chmod',\n    'chomp',\n    'chop',\n    'chown',\n    'chr',\n    'chroot',\n    'close',\n    'closedir',\n    'connect',\n    'continue',\n    'cos',\n    'crypt',\n    'dbmclose',\n    'dbmopen',\n    'defined',\n    'delete',\n    'die',\n    'do',\n    'dump',\n    'each',\n    'else',\n    'elsif',\n    'endgrent',\n    'endhostent',\n    'endnetent',\n    'endprotoent',\n    'endpwent',\n    'endservent',\n    'eof',\n    'eval',\n    'exec',\n    'exists',\n    'exit',\n    'exp',\n    'fcntl',\n    'fileno',\n    'flock',\n    'for',\n    'foreach',\n    'fork',\n    'format',\n    'formline',\n    'getc',\n    'getgrent',\n    'getgrgid',\n    'getgrnam',\n    'gethostbyaddr',\n    'gethostbyname',\n    'gethostent',\n    'getlogin',\n    'getnetbyaddr',\n    'getnetbyname',\n    'getnetent',\n    'getpeername',\n    'getpgrp',\n    'getpriority',\n    'getprotobyname',\n    'getprotobynumber',\n    'getprotoent',\n    'getpwent',\n    'getpwnam',\n    'getpwuid',\n    'getservbyname',\n    'getservbyport',\n    'getservent',\n    'getsockname',\n    'getsockopt',\n    'given',\n    'glob',\n    'gmtime',\n    'goto',\n    'grep',\n    'gt',\n    'hex',\n    'if',\n    'index',\n    'int',\n    'ioctl',\n    'join',\n    'keys',\n    'kill',\n    'last',\n    'lc',\n    'lcfirst',\n    'length',\n    'link',\n    'listen',\n    'local',\n    'localtime',\n    'log',\n    'lstat',\n    'lt',\n    'ma',\n    'map',\n    'mkdir',\n    'msgctl',\n    'msgget',\n    'msgrcv',\n    'msgsnd',\n    'my',\n    'ne',\n    'next',\n    'no',\n    'not',\n    'oct',\n    'open',\n    'opendir',\n    'or',\n    'ord',\n    'our',\n    'pack',\n    'package',\n    'pipe',\n    'pop',\n    'pos',\n    'print',\n    'printf',\n    'prototype',\n    'push',\n    'q|0',\n    'qq',\n    'quotemeta',\n    'qw',\n    'qx',\n    'rand',\n    'read',\n    'readdir',\n    'readline',\n    'readlink',\n    'readpipe',\n    'recv',\n    'redo',\n    'ref',\n    'rename',\n    'require',\n    'reset',\n    'return',\n    'reverse',\n    'rewinddir',\n    'rindex',\n    'rmdir',\n    'say',\n    'scalar',\n    'seek',\n    'seekdir',\n    'select',\n    'semctl',\n    'semget',\n    'semop',\n    'send',\n    'setgrent',\n    'sethostent',\n    'setnetent',\n    'setpgrp',\n    'setpriority',\n    'setprotoent',\n    'setpwent',\n    'setservent',\n    'setsockopt',\n    'shift',\n    'shmctl',\n    'shmget',\n    'shmread',\n    'shmwrite',\n    'shutdown',\n    'sin',\n    'sleep',\n    'socket',\n    'socketpair',\n    'sort',\n    'splice',\n    'split',\n    'sprintf',\n    'sqrt',\n    'srand',\n    'stat',\n    'state',\n    'study',\n    'sub',\n    'substr',\n    'symlink',\n    'syscall',\n    'sysopen',\n    'sysread',\n    'sysseek',\n    'system',\n    'syswrite',\n    'tell',\n    'telldir',\n    'tie',\n    'tied',\n    'time',\n    'times',\n    'tr',\n    'truncate',\n    'uc',\n    'ucfirst',\n    'umask',\n    'undef',\n    'unless',\n    'unlink',\n    'unpack',\n    'unshift',\n    'untie',\n    'until',\n    'use',\n    'utime',\n    'values',\n    'vec',\n    'wait',\n    'waitpid',\n    'wantarray',\n    'warn',\n    'when',\n    'while',\n    'write',\n    'x|0',\n    'xor',\n    'y|0'\n  ];\n\n  // https://perldoc.perl.org/perlre#Modifiers\n  const REGEX_MODIFIERS = /[dualxmsipngr]{0,12}/; // aa and xx are valid, making max length 12\n  const PERL_KEYWORDS = {\n    $pattern: /[\\w.]+/,\n    keyword: KEYWORDS.join(\" \")\n  };\n  const SUBST = {\n    className: 'subst',\n    begin: '[$@]\\\\{',\n    end: '\\\\}',\n    keywords: PERL_KEYWORDS\n  };\n  const METHOD = {\n    begin: /->\\{/,\n    end: /\\}/\n    // contains defined later\n  };\n  const VAR = { variants: [\n    { begin: /\\$\\d/ },\n    { begin: regex.concat(\n      /[$%@](\\^\\w\\b|#\\w+(::\\w+)*|\\{\\w+\\}|\\w+(::\\w*)*)/,\n      // negative look-ahead tries to avoid matching patterns that are not\n      // Perl at all like $ident$, @ident@, etc.\n      `(?![A-Za-z])(?![@$%])`\n    ) },\n    {\n      begin: /[$%@][^\\s\\w{]/,\n      relevance: 0\n    }\n  ] };\n  const STRING_CONTAINS = [\n    hljs.BACKSLASH_ESCAPE,\n    SUBST,\n    VAR\n  ];\n  const REGEX_DELIMS = [\n    /!/,\n    /\\//,\n    /\\|/,\n    /\\?/,\n    /'/,\n    /\"/, // valid but infrequent and weird\n    /#/ // valid but infrequent and weird\n  ];\n  /**\n   * @param {string|RegExp} prefix\n   * @param {string|RegExp} open\n   * @param {string|RegExp} close\n   */\n  const PAIRED_DOUBLE_RE = (prefix, open, close = '\\\\1') => {\n    const middle = (close === '\\\\1')\n      ? close\n      : regex.concat(close, open);\n    return regex.concat(\n      regex.concat(\"(?:\", prefix, \")\"),\n      open,\n      /(?:\\\\.|[^\\\\\\/])*?/,\n      middle,\n      /(?:\\\\.|[^\\\\\\/])*?/,\n      close,\n      REGEX_MODIFIERS\n    );\n  };\n  /**\n   * @param {string|RegExp} prefix\n   * @param {string|RegExp} open\n   * @param {string|RegExp} close\n   */\n  const PAIRED_RE = (prefix, open, close) => {\n    return regex.concat(\n      regex.concat(\"(?:\", prefix, \")\"),\n      open,\n      /(?:\\\\.|[^\\\\\\/])*?/,\n      close,\n      REGEX_MODIFIERS\n    );\n  };\n  const PERL_DEFAULT_CONTAINS = [\n    VAR,\n    hljs.HASH_COMMENT_MODE,\n    hljs.COMMENT(\n      /^=\\w/,\n      /=cut/,\n      { endsWithParent: true }\n    ),\n    METHOD,\n    {\n      className: 'string',\n      contains: STRING_CONTAINS,\n      variants: [\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\(',\n          end: '\\\\)',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\[',\n          end: '\\\\]',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\{',\n          end: '\\\\}',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\|',\n          end: '\\\\|',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*<',\n          end: '>',\n          relevance: 5\n        },\n        {\n          begin: 'qw\\\\s+q',\n          end: 'q',\n          relevance: 5\n        },\n        {\n          begin: '\\'',\n          end: '\\'',\n          contains: [ hljs.BACKSLASH_ESCAPE ]\n        },\n        {\n          begin: '\"',\n          end: '\"'\n        },\n        {\n          begin: '`',\n          end: '`',\n          contains: [ hljs.BACKSLASH_ESCAPE ]\n        },\n        {\n          begin: /\\{\\w+\\}/,\n          relevance: 0\n        },\n        {\n          begin: '-?\\\\w+\\\\s*=>',\n          relevance: 0\n        }\n      ]\n    },\n    {\n      className: 'number',\n      begin: '(\\\\b0[0-7_]+)|(\\\\b0x[0-9a-fA-F_]+)|(\\\\b[1-9][0-9_]*(\\\\.[0-9_]+)?)|[0_]\\\\b',\n      relevance: 0\n    },\n    { // regexp container\n      begin: '(\\\\/\\\\/|' + hljs.RE_STARTERS_RE + '|\\\\b(split|return|print|reverse|grep)\\\\b)\\\\s*',\n      keywords: 'split return print reverse grep',\n      relevance: 0,\n      contains: [\n        hljs.HASH_COMMENT_MODE,\n        {\n          className: 'regexp',\n          variants: [\n            // allow matching common delimiters\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", regex.either(...REGEX_DELIMS, { capture: true })) },\n            // and then paired delmis\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\(\", \"\\\\)\") },\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\[\", \"\\\\]\") },\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\{\", \"\\\\}\") }\n          ],\n          relevance: 2\n        },\n        {\n          className: 'regexp',\n          variants: [\n            {\n              // could be a comment in many languages so do not count\n              // as relevant\n              begin: /(m|qr)\\/\\//,\n              relevance: 0\n            },\n            // prefix is optional with /regex/\n            { begin: PAIRED_RE(\"(?:m|qr)?\", /\\//, /\\//) },\n            // allow matching common delimiters\n            { begin: PAIRED_RE(\"m|qr\", regex.either(...REGEX_DELIMS, { capture: true }), /\\1/) },\n            // allow common paired delmins\n            { begin: PAIRED_RE(\"m|qr\", /\\(/, /\\)/) },\n            { begin: PAIRED_RE(\"m|qr\", /\\[/, /\\]/) },\n            { begin: PAIRED_RE(\"m|qr\", /\\{/, /\\}/) }\n          ]\n        }\n      ]\n    },\n    {\n      className: 'function',\n      beginKeywords: 'sub',\n      end: '(\\\\s*\\\\(.*?\\\\))?[;{]',\n      excludeEnd: true,\n      relevance: 5,\n      contains: [ hljs.TITLE_MODE ]\n    },\n    {\n      begin: '-\\\\w\\\\b',\n      relevance: 0\n    },\n    {\n      begin: \"^__DATA__$\",\n      end: \"^__END__$\",\n      subLanguage: 'mojolicious',\n      contains: [\n        {\n          begin: \"^@@.*\",\n          end: \"$\",\n          className: \"comment\"\n        }\n      ]\n    }\n  ];\n  SUBST.contains = PERL_DEFAULT_CONTAINS;\n  METHOD.contains = PERL_DEFAULT_CONTAINS;\n\n  return {\n    name: 'Perl',\n    aliases: [\n      'pl',\n      'pm'\n    ],\n    keywords: PERL_KEYWORDS,\n    contains: PERL_DEFAULT_CONTAINS\n  };\n}\n\nmodule.exports = perl;\n", "/*\nLanguage: Objective-C\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <ahfar<PERSON>@gmail.com>, <PERSON> <<EMAIL>>\nWebsite: https://developer.apple.com/documentation/objectivec\nCategory: common\n*/\n\nfunction objectivec(hljs) {\n  const API_CLASS = {\n    className: 'built_in',\n    begin: '\\\\b(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)\\\\w+'\n  };\n  const IDENTIFIER_RE = /[a-zA-Z@][a-zA-Z0-9_]*/;\n  const TYPES = [\n    \"int\",\n    \"float\",\n    \"char\",\n    \"unsigned\",\n    \"signed\",\n    \"short\",\n    \"long\",\n    \"double\",\n    \"wchar_t\",\n    \"unichar\",\n    \"void\",\n    \"bool\",\n    \"BOOL\",\n    \"id|0\",\n    \"_Bool\"\n  ];\n  const KWS = [\n    \"while\",\n    \"export\",\n    \"sizeof\",\n    \"typedef\",\n    \"const\",\n    \"struct\",\n    \"for\",\n    \"union\",\n    \"volatile\",\n    \"static\",\n    \"mutable\",\n    \"if\",\n    \"do\",\n    \"return\",\n    \"goto\",\n    \"enum\",\n    \"else\",\n    \"break\",\n    \"extern\",\n    \"asm\",\n    \"case\",\n    \"default\",\n    \"register\",\n    \"explicit\",\n    \"typename\",\n    \"switch\",\n    \"continue\",\n    \"inline\",\n    \"readonly\",\n    \"assign\",\n    \"readwrite\",\n    \"self\",\n    \"@synchronized\",\n    \"id\",\n    \"typeof\",\n    \"nonatomic\",\n    \"IBOutlet\",\n    \"IBAction\",\n    \"strong\",\n    \"weak\",\n    \"copy\",\n    \"in\",\n    \"out\",\n    \"inout\",\n    \"bycopy\",\n    \"byref\",\n    \"oneway\",\n    \"__strong\",\n    \"__weak\",\n    \"__block\",\n    \"__autoreleasing\",\n    \"@private\",\n    \"@protected\",\n    \"@public\",\n    \"@try\",\n    \"@property\",\n    \"@end\",\n    \"@throw\",\n    \"@catch\",\n    \"@finally\",\n    \"@autoreleasepool\",\n    \"@synthesize\",\n    \"@dynamic\",\n    \"@selector\",\n    \"@optional\",\n    \"@required\",\n    \"@encode\",\n    \"@package\",\n    \"@import\",\n    \"@defs\",\n    \"@compatibility_alias\",\n    \"__bridge\",\n    \"__bridge_transfer\",\n    \"__bridge_retained\",\n    \"__bridge_retain\",\n    \"__covariant\",\n    \"__contravariant\",\n    \"__kindof\",\n    \"_Nonnull\",\n    \"_Nullable\",\n    \"_Null_unspecified\",\n    \"__FUNCTION__\",\n    \"__PRETTY_FUNCTION__\",\n    \"__attribute__\",\n    \"getter\",\n    \"setter\",\n    \"retain\",\n    \"unsafe_unretained\",\n    \"nonnull\",\n    \"nullable\",\n    \"null_unspecified\",\n    \"null_resettable\",\n    \"class\",\n    \"instancetype\",\n    \"NS_DESIGNATED_INITIALIZER\",\n    \"NS_UNAVAILABLE\",\n    \"NS_REQUIRES_SUPER\",\n    \"NS_RETURNS_INNER_POINTER\",\n    \"NS_INLINE\",\n    \"NS_AVAILABLE\",\n    \"NS_DEPRECATED\",\n    \"NS_ENUM\",\n    \"NS_OPTIONS\",\n    \"NS_SWIFT_UNAVAILABLE\",\n    \"NS_ASSUME_NONNULL_BEGIN\",\n    \"NS_ASSUME_NONNULL_END\",\n    \"NS_REFINED_FOR_SWIFT\",\n    \"NS_SWIFT_NAME\",\n    \"NS_SWIFT_NOTHROW\",\n    \"NS_DURING\",\n    \"NS_HANDLER\",\n    \"NS_ENDHANDLER\",\n    \"NS_VALUERETURN\",\n    \"NS_VOIDRETURN\"\n  ];\n  const LITERALS = [\n    \"false\",\n    \"true\",\n    \"FALSE\",\n    \"TRUE\",\n    \"nil\",\n    \"YES\",\n    \"NO\",\n    \"NULL\"\n  ];\n  const BUILT_INS = [\n    \"dispatch_once_t\",\n    \"dispatch_queue_t\",\n    \"dispatch_sync\",\n    \"dispatch_async\",\n    \"dispatch_once\"\n  ];\n  const KEYWORDS = {\n    \"variable.language\": [\n      \"this\",\n      \"super\"\n    ],\n    $pattern: IDENTIFIER_RE,\n    keyword: KWS,\n    literal: LITERALS,\n    built_in: BUILT_INS,\n    type: TYPES\n  };\n  const CLASS_KEYWORDS = {\n    $pattern: IDENTIFIER_RE,\n    keyword: [\n      \"@interface\",\n      \"@class\",\n      \"@protocol\",\n      \"@implementation\"\n    ]\n  };\n  return {\n    name: 'Objective-C',\n    aliases: [\n      'mm',\n      'objc',\n      'obj-c',\n      'obj-c++',\n      'objective-c++'\n    ],\n    keywords: KEYWORDS,\n    illegal: '</',\n    contains: [\n      API_CLASS,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_NUMBER_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE,\n      {\n        className: 'string',\n        variants: [\n          {\n            begin: '@\"',\n            end: '\"',\n            illegal: '\\\\n',\n            contains: [ hljs.BACKSLASH_ESCAPE ]\n          }\n        ]\n      },\n      {\n        className: 'meta',\n        begin: /#\\s*[a-z]+\\b/,\n        end: /$/,\n        keywords: { keyword:\n            'if else elif endif define undef warning error line '\n            + 'pragma ifdef ifndef include' },\n        contains: [\n          {\n            begin: /\\\\\\n/,\n            relevance: 0\n          },\n          hljs.inherit(hljs.QUOTE_STRING_MODE, { className: 'string' }),\n          {\n            className: 'string',\n            begin: /<.*?>/,\n            end: /$/,\n            illegal: '\\\\n'\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        className: 'class',\n        begin: '(' + CLASS_KEYWORDS.keyword.join('|') + ')\\\\b',\n        end: /(\\{|$)/,\n        excludeEnd: true,\n        keywords: CLASS_KEYWORDS,\n        contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n      },\n      {\n        begin: '\\\\.' + hljs.UNDERSCORE_IDENT_RE,\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = objectivec;\n", "/*\nLanguage: PHP\nAuthor: <PERSON> <<PERSON>.<EMAIL>>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://www.php.net\nCategory: common\n*/\n\n/**\n * @param {H<PERSON><PERSON><PERSON><PERSON>} hljs\n * @returns {LanguageDetail}\n * */\nfunction php(hljs) {\n  const regex = hljs.regex;\n  // negative look-ahead tries to avoid matching patterns that are not\n  // Perl at all like $ident$, @ident@, etc.\n  const NOT_PERL_ETC = /(?![A-Za-z0-9])(?![$])/;\n  const IDENT_RE = regex.concat(\n    /[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*/,\n    NOT_PERL_ETC);\n  // Will not detect camelCase classes\n  const PASCAL_CASE_CLASS_NAME_RE = regex.concat(\n    /(\\\\?[A-Z][a-z0-9_\\x7f-\\xff]+|\\\\?[A-Z]+(?=[A-Z][a-z0-9_\\x7f-\\xff])){1,}/,\n    NOT_PERL_ETC);\n  const VARIABLE = {\n    scope: 'variable',\n    match: '\\\\$+' + IDENT_RE,\n  };\n  const PREPROCESSOR = {\n    scope: 'meta',\n    variants: [\n      { begin: /<\\?php/, relevance: 10 }, // boost for obvious PHP\n      { begin: /<\\?=/ },\n      // less relevant per PSR-1 which says not to use short-tags\n      { begin: /<\\?/, relevance: 0.1 },\n      { begin: /\\?>/ } // end php tag\n    ]\n  };\n  const SUBST = {\n    scope: 'subst',\n    variants: [\n      { begin: /\\$\\w+/ },\n      {\n        begin: /\\{\\$/,\n        end: /\\}/\n      }\n    ]\n  };\n  const SINGLE_QUOTED = hljs.inherit(hljs.APOS_STRING_MODE, { illegal: null, });\n  const DOUBLE_QUOTED = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null,\n    contains: hljs.QUOTE_STRING_MODE.contains.concat(SUBST),\n  });\n\n  const HEREDOC = {\n    begin: /<<<[ \\t]*(?:(\\w+)|\"(\\w+)\")\\n/,\n    end: /[ \\t]*(\\w+)\\b/,\n    contains: hljs.QUOTE_STRING_MODE.contains.concat(SUBST),\n    'on:begin': (m, resp) => { resp.data._beginMatch = m[1] || m[2]; },\n    'on:end': (m, resp) => { if (resp.data._beginMatch !== m[1]) resp.ignoreMatch(); },\n  };\n\n  const NOWDOC = hljs.END_SAME_AS_BEGIN({\n    begin: /<<<[ \\t]*'(\\w+)'\\n/,\n    end: /[ \\t]*(\\w+)\\b/,\n  });\n  // list of valid whitespaces because non-breaking space might be part of a IDENT_RE\n  const WHITESPACE = '[ \\t\\n]';\n  const STRING = {\n    scope: 'string',\n    variants: [\n      DOUBLE_QUOTED,\n      SINGLE_QUOTED,\n      HEREDOC,\n      NOWDOC\n    ]\n  };\n  const NUMBER = {\n    scope: 'number',\n    variants: [\n      { begin: `\\\\b0[bB][01]+(?:_[01]+)*\\\\b` }, // Binary w/ underscore support\n      { begin: `\\\\b0[oO][0-7]+(?:_[0-7]+)*\\\\b` }, // Octals w/ underscore support\n      { begin: `\\\\b0[xX][\\\\da-fA-F]+(?:_[\\\\da-fA-F]+)*\\\\b` }, // Hex w/ underscore support\n      // Decimals w/ underscore support, with optional fragments and scientific exponent (e) suffix.\n      { begin: `(?:\\\\b\\\\d+(?:_\\\\d+)*(\\\\.(?:\\\\d+(?:_\\\\d+)*))?|\\\\B\\\\.\\\\d+)(?:[eE][+-]?\\\\d+)?` }\n    ],\n    relevance: 0\n  };\n  const LITERALS = [\n    \"false\",\n    \"null\",\n    \"true\"\n  ];\n  const KWS = [\n    // Magic constants:\n    // <https://www.php.net/manual/en/language.constants.predefined.php>\n    \"__CLASS__\",\n    \"__DIR__\",\n    \"__FILE__\",\n    \"__FUNCTION__\",\n    \"__COMPILER_HALT_OFFSET__\",\n    \"__LINE__\",\n    \"__METHOD__\",\n    \"__NAMESPACE__\",\n    \"__TRAIT__\",\n    // Function that look like language construct or language construct that look like function:\n    // List of keywords that may not require parenthesis\n    \"die\",\n    \"echo\",\n    \"exit\",\n    \"include\",\n    \"include_once\",\n    \"print\",\n    \"require\",\n    \"require_once\",\n    // These are not language construct (function) but operate on the currently-executing function and can access the current symbol table\n    // 'compact extract func_get_arg func_get_args func_num_args get_called_class get_parent_class ' +\n    // Other keywords:\n    // <https://www.php.net/manual/en/reserved.php>\n    // <https://www.php.net/manual/en/language.types.type-juggling.php>\n    \"array\",\n    \"abstract\",\n    \"and\",\n    \"as\",\n    \"binary\",\n    \"bool\",\n    \"boolean\",\n    \"break\",\n    \"callable\",\n    \"case\",\n    \"catch\",\n    \"class\",\n    \"clone\",\n    \"const\",\n    \"continue\",\n    \"declare\",\n    \"default\",\n    \"do\",\n    \"double\",\n    \"else\",\n    \"elseif\",\n    \"empty\",\n    \"enddeclare\",\n    \"endfor\",\n    \"endforeach\",\n    \"endif\",\n    \"endswitch\",\n    \"endwhile\",\n    \"enum\",\n    \"eval\",\n    \"extends\",\n    \"final\",\n    \"finally\",\n    \"float\",\n    \"for\",\n    \"foreach\",\n    \"from\",\n    \"global\",\n    \"goto\",\n    \"if\",\n    \"implements\",\n    \"instanceof\",\n    \"insteadof\",\n    \"int\",\n    \"integer\",\n    \"interface\",\n    \"isset\",\n    \"iterable\",\n    \"list\",\n    \"match|0\",\n    \"mixed\",\n    \"new\",\n    \"never\",\n    \"object\",\n    \"or\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"readonly\",\n    \"real\",\n    \"return\",\n    \"string\",\n    \"switch\",\n    \"throw\",\n    \"trait\",\n    \"try\",\n    \"unset\",\n    \"use\",\n    \"var\",\n    \"void\",\n    \"while\",\n    \"xor\",\n    \"yield\"\n  ];\n\n  const BUILT_INS = [\n    // Standard PHP library:\n    // <https://www.php.net/manual/en/book.spl.php>\n    \"Error|0\",\n    \"AppendIterator\",\n    \"ArgumentCountError\",\n    \"ArithmeticError\",\n    \"ArrayIterator\",\n    \"ArrayObject\",\n    \"AssertionError\",\n    \"BadFunctionCallException\",\n    \"BadMethodCallException\",\n    \"CachingIterator\",\n    \"CallbackFilterIterator\",\n    \"CompileError\",\n    \"Countable\",\n    \"DirectoryIterator\",\n    \"DivisionByZeroError\",\n    \"DomainException\",\n    \"EmptyIterator\",\n    \"ErrorException\",\n    \"Exception\",\n    \"FilesystemIterator\",\n    \"FilterIterator\",\n    \"GlobIterator\",\n    \"InfiniteIterator\",\n    \"InvalidArgumentException\",\n    \"IteratorIterator\",\n    \"LengthException\",\n    \"LimitIterator\",\n    \"LogicException\",\n    \"MultipleIterator\",\n    \"NoRewindIterator\",\n    \"OutOfBoundsException\",\n    \"OutOfRangeException\",\n    \"OuterIterator\",\n    \"OverflowException\",\n    \"ParentIterator\",\n    \"ParseError\",\n    \"RangeException\",\n    \"RecursiveArrayIterator\",\n    \"RecursiveCachingIterator\",\n    \"RecursiveCallbackFilterIterator\",\n    \"RecursiveDirectoryIterator\",\n    \"RecursiveFilterIterator\",\n    \"RecursiveIterator\",\n    \"RecursiveIteratorIterator\",\n    \"RecursiveRegexIterator\",\n    \"RecursiveTreeIterator\",\n    \"RegexIterator\",\n    \"RuntimeException\",\n    \"SeekableIterator\",\n    \"SplDoublyLinkedList\",\n    \"SplFileInfo\",\n    \"SplFileObject\",\n    \"SplFixedArray\",\n    \"SplHeap\",\n    \"SplMaxHeap\",\n    \"SplMinHeap\",\n    \"SplObjectStorage\",\n    \"SplObserver\",\n    \"SplPriorityQueue\",\n    \"SplQueue\",\n    \"SplStack\",\n    \"SplSubject\",\n    \"SplTempFileObject\",\n    \"TypeError\",\n    \"UnderflowException\",\n    \"UnexpectedValueException\",\n    \"UnhandledMatchError\",\n    // Reserved interfaces:\n    // <https://www.php.net/manual/en/reserved.interfaces.php>\n    \"ArrayAccess\",\n    \"BackedEnum\",\n    \"Closure\",\n    \"Fiber\",\n    \"Generator\",\n    \"Iterator\",\n    \"IteratorAggregate\",\n    \"Serializable\",\n    \"Stringable\",\n    \"Throwable\",\n    \"Traversable\",\n    \"UnitEnum\",\n    \"WeakReference\",\n    \"WeakMap\",\n    // Reserved classes:\n    // <https://www.php.net/manual/en/reserved.classes.php>\n    \"Directory\",\n    \"__PHP_Incomplete_Class\",\n    \"parent\",\n    \"php_user_filter\",\n    \"self\",\n    \"static\",\n    \"stdClass\"\n  ];\n\n  /** Dual-case keywords\n   *\n   * [\"then\",\"FILE\"] =>\n   *     [\"then\", \"THEN\", \"FILE\", \"file\"]\n   *\n   * @param {string[]} items */\n  const dualCase = (items) => {\n    /** @type string[] */\n    const result = [];\n    items.forEach(item => {\n      result.push(item);\n      if (item.toLowerCase() === item) {\n        result.push(item.toUpperCase());\n      } else {\n        result.push(item.toLowerCase());\n      }\n    });\n    return result;\n  };\n\n  const KEYWORDS = {\n    keyword: KWS,\n    literal: dualCase(LITERALS),\n    built_in: BUILT_INS,\n  };\n\n  /**\n   * @param {string[]} items */\n  const normalizeKeywords = (items) => {\n    return items.map(item => {\n      return item.replace(/\\|\\d+$/, \"\");\n    });\n  };\n\n  const CONSTRUCTOR_CALL = { variants: [\n    {\n      match: [\n        /new/,\n        regex.concat(WHITESPACE, \"+\"),\n        // to prevent built ins from being confused as the class constructor call\n        regex.concat(\"(?!\", normalizeKeywords(BUILT_INS).join(\"\\\\b|\"), \"\\\\b)\"),\n        PASCAL_CASE_CLASS_NAME_RE,\n      ],\n      scope: {\n        1: \"keyword\",\n        4: \"title.class\",\n      },\n    }\n  ] };\n\n  const CONSTANT_REFERENCE = regex.concat(IDENT_RE, \"\\\\b(?!\\\\()\");\n\n  const LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON = { variants: [\n    {\n      match: [\n        regex.concat(\n          /::/,\n          regex.lookahead(/(?!class\\b)/)\n        ),\n        CONSTANT_REFERENCE,\n      ],\n      scope: { 2: \"variable.constant\", },\n    },\n    {\n      match: [\n        /::/,\n        /class/,\n      ],\n      scope: { 2: \"variable.language\", },\n    },\n    {\n      match: [\n        PASCAL_CASE_CLASS_NAME_RE,\n        regex.concat(\n          /::/,\n          regex.lookahead(/(?!class\\b)/)\n        ),\n        CONSTANT_REFERENCE,\n      ],\n      scope: {\n        1: \"title.class\",\n        3: \"variable.constant\",\n      },\n    },\n    {\n      match: [\n        PASCAL_CASE_CLASS_NAME_RE,\n        regex.concat(\n          \"::\",\n          regex.lookahead(/(?!class\\b)/)\n        ),\n      ],\n      scope: { 1: \"title.class\", },\n    },\n    {\n      match: [\n        PASCAL_CASE_CLASS_NAME_RE,\n        /::/,\n        /class/,\n      ],\n      scope: {\n        1: \"title.class\",\n        3: \"variable.language\",\n      },\n    }\n  ] };\n\n  const NAMED_ARGUMENT = {\n    scope: 'attr',\n    match: regex.concat(IDENT_RE, regex.lookahead(':'), regex.lookahead(/(?!::)/)),\n  };\n  const PARAMS_MODE = {\n    relevance: 0,\n    begin: /\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS,\n    contains: [\n      NAMED_ARGUMENT,\n      VARIABLE,\n      LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON,\n      hljs.C_BLOCK_COMMENT_MODE,\n      STRING,\n      NUMBER,\n      CONSTRUCTOR_CALL,\n    ],\n  };\n  const FUNCTION_INVOKE = {\n    relevance: 0,\n    match: [\n      /\\b/,\n      // to prevent keywords from being confused as the function title\n      regex.concat(\"(?!fn\\\\b|function\\\\b|\", normalizeKeywords(KWS).join(\"\\\\b|\"), \"|\", normalizeKeywords(BUILT_INS).join(\"\\\\b|\"), \"\\\\b)\"),\n      IDENT_RE,\n      regex.concat(WHITESPACE, \"*\"),\n      regex.lookahead(/(?=\\()/)\n    ],\n    scope: { 3: \"title.function.invoke\", },\n    contains: [ PARAMS_MODE ]\n  };\n  PARAMS_MODE.contains.push(FUNCTION_INVOKE);\n\n  const ATTRIBUTE_CONTAINS = [\n    NAMED_ARGUMENT,\n    LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON,\n    hljs.C_BLOCK_COMMENT_MODE,\n    STRING,\n    NUMBER,\n    CONSTRUCTOR_CALL,\n  ];\n\n  const ATTRIBUTES = {\n    begin: regex.concat(/#\\[\\s*/, PASCAL_CASE_CLASS_NAME_RE),\n    beginScope: \"meta\",\n    end: /]/,\n    endScope: \"meta\",\n    keywords: {\n      literal: LITERALS,\n      keyword: [\n        'new',\n        'array',\n      ]\n    },\n    contains: [\n      {\n        begin: /\\[/,\n        end: /]/,\n        keywords: {\n          literal: LITERALS,\n          keyword: [\n            'new',\n            'array',\n          ]\n        },\n        contains: [\n          'self',\n          ...ATTRIBUTE_CONTAINS,\n        ]\n      },\n      ...ATTRIBUTE_CONTAINS,\n      {\n        scope: 'meta',\n        match: PASCAL_CASE_CLASS_NAME_RE\n      }\n    ]\n  };\n\n  return {\n    case_insensitive: false,\n    keywords: KEYWORDS,\n    contains: [\n      ATTRIBUTES,\n      hljs.HASH_COMMENT_MODE,\n      hljs.COMMENT('//', '$'),\n      hljs.COMMENT(\n        '/\\\\*',\n        '\\\\*/',\n        { contains: [\n          {\n            scope: 'doctag',\n            match: '@[A-Za-z]+'\n          }\n        ] }\n      ),\n      {\n        match: /__halt_compiler\\(\\);/,\n        keywords: '__halt_compiler',\n        starts: {\n          scope: \"comment\",\n          end: hljs.MATCH_NOTHING_RE,\n          contains: [\n            {\n              match: /\\?>/,\n              scope: \"meta\",\n              endsParent: true\n            }\n          ]\n        }\n      },\n      PREPROCESSOR,\n      {\n        scope: 'variable.language',\n        match: /\\$this\\b/\n      },\n      VARIABLE,\n      FUNCTION_INVOKE,\n      LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON,\n      {\n        match: [\n          /const/,\n          /\\s/,\n          IDENT_RE,\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"variable.constant\",\n        },\n      },\n      CONSTRUCTOR_CALL,\n      {\n        scope: 'function',\n        relevance: 0,\n        beginKeywords: 'fn function',\n        end: /[;{]/,\n        excludeEnd: true,\n        illegal: '[$%\\\\[]',\n        contains: [\n          { beginKeywords: 'use', },\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            begin: '=>', // No markup, just a relevance booster\n            endsParent: true\n          },\n          {\n            scope: 'params',\n            begin: '\\\\(',\n            end: '\\\\)',\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            contains: [\n              'self',\n              VARIABLE,\n              LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRING,\n              NUMBER\n            ]\n          },\n        ]\n      },\n      {\n        scope: 'class',\n        variants: [\n          {\n            beginKeywords: \"enum\",\n            illegal: /[($\"]/\n          },\n          {\n            beginKeywords: \"class interface trait\",\n            illegal: /[:($\"]/\n          }\n        ],\n        relevance: 0,\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          { beginKeywords: 'extends implements' },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      // both use and namespace still use \"old style\" rules (vs multi-match)\n      // because the namespace name can include `\\` and we still want each\n      // element to be treated as its own *individual* title\n      {\n        beginKeywords: 'namespace',\n        relevance: 0,\n        end: ';',\n        illegal: /[.']/,\n        contains: [ hljs.inherit(hljs.UNDERSCORE_TITLE_MODE, { scope: \"title.class\" }) ]\n      },\n      {\n        beginKeywords: 'use',\n        relevance: 0,\n        end: ';',\n        contains: [\n          // TODO: title.function vs title.class\n          {\n            match: /\\b(as|const|function)\\b/,\n            scope: \"keyword\"\n          },\n          // TODO: could be title.class or title.function\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      STRING,\n      NUMBER,\n    ]\n  };\n}\n\nmodule.exports = php;\n", "/*\nLanguage: PHP Template\nRequires: xml.js, php.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.php.net\nCategory: common\n*/\n\nfunction phpTemplate(hljs) {\n  return {\n    name: \"PHP template\",\n    subLanguage: 'xml',\n    contains: [\n      {\n        begin: /<\\?(php|=)?/,\n        end: /\\?>/,\n        subLanguage: 'php',\n        contains: [\n          // We don't want the php closing tag ?> to close the PHP block when\n          // inside any of the following blocks:\n          {\n            begin: '/\\\\*',\n            end: '\\\\*/',\n            skip: true\n          },\n          {\n            begin: 'b\"',\n            end: '\"',\n            skip: true\n          },\n          {\n            begin: 'b\\'',\n            end: '\\'',\n            skip: true\n          },\n          hljs.inherit(hljs.APOS_STRING_MODE, {\n            illegal: null,\n            className: null,\n            contains: null,\n            skip: true\n          }),\n          hljs.inherit(hljs.QUOTE_STRING_MODE, {\n            illegal: null,\n            className: null,\n            contains: null,\n            skip: true\n          })\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = phpTemplate;\n", "/*\nLanguage: Plain text\nAuthor: <PERSON><PERSON> (<EMAIL>)\nDescription: Plain text without any highlighting.\nCategory: common\n*/\n\nfunction plaintext(hljs) {\n  return {\n    name: 'Plain text',\n    aliases: [\n      'text',\n      'txt'\n    ],\n    disableAutodetect: true\n  };\n}\n\nmodule.exports = plaintext;\n", "/*\nLanguage: Python\nDescription: Python is an interpreted, object-oriented, high-level programming language with dynamic semantics.\nWebsite: https://www.python.org\nCategory: common\n*/\n\nfunction python(hljs) {\n  const regex = hljs.regex;\n  const IDENT_RE = /[\\p{XID_Start}_]\\p{XID_Continue}*/u;\n  const RESERVED_WORDS = [\n    'and',\n    'as',\n    'assert',\n    'async',\n    'await',\n    'break',\n    'case',\n    'class',\n    'continue',\n    'def',\n    'del',\n    'elif',\n    'else',\n    'except',\n    'finally',\n    'for',\n    'from',\n    'global',\n    'if',\n    'import',\n    'in',\n    'is',\n    'lambda',\n    'match',\n    'nonlocal|10',\n    'not',\n    'or',\n    'pass',\n    'raise',\n    'return',\n    'try',\n    'while',\n    'with',\n    'yield'\n  ];\n\n  const BUILT_INS = [\n    '__import__',\n    'abs',\n    'all',\n    'any',\n    'ascii',\n    'bin',\n    'bool',\n    'breakpoint',\n    'bytearray',\n    'bytes',\n    'callable',\n    'chr',\n    'classmethod',\n    'compile',\n    'complex',\n    'delattr',\n    'dict',\n    'dir',\n    'divmod',\n    'enumerate',\n    'eval',\n    'exec',\n    'filter',\n    'float',\n    'format',\n    'frozenset',\n    'getattr',\n    'globals',\n    'hasattr',\n    'hash',\n    'help',\n    'hex',\n    'id',\n    'input',\n    'int',\n    'isinstance',\n    'issubclass',\n    'iter',\n    'len',\n    'list',\n    'locals',\n    'map',\n    'max',\n    'memoryview',\n    'min',\n    'next',\n    'object',\n    'oct',\n    'open',\n    'ord',\n    'pow',\n    'print',\n    'property',\n    'range',\n    'repr',\n    'reversed',\n    'round',\n    'set',\n    'setattr',\n    'slice',\n    'sorted',\n    'staticmethod',\n    'str',\n    'sum',\n    'super',\n    'tuple',\n    'type',\n    'vars',\n    'zip'\n  ];\n\n  const LITERALS = [\n    '__debug__',\n    'Ellipsis',\n    'False',\n    'None',\n    'NotImplemented',\n    'True'\n  ];\n\n  // https://docs.python.org/3/library/typing.html\n  // TODO: Could these be supplemented by a CamelCase matcher in certain\n  // contexts, leaving these remaining only for relevance hinting?\n  const TYPES = [\n    \"Any\",\n    \"Callable\",\n    \"Coroutine\",\n    \"Dict\",\n    \"List\",\n    \"Literal\",\n    \"Generic\",\n    \"Optional\",\n    \"Sequence\",\n    \"Set\",\n    \"Tuple\",\n    \"Type\",\n    \"Union\"\n  ];\n\n  const KEYWORDS = {\n    $pattern: /[A-Za-z]\\w+|__\\w+__/,\n    keyword: RESERVED_WORDS,\n    built_in: BUILT_INS,\n    literal: LITERALS,\n    type: TYPES\n  };\n\n  const PROMPT = {\n    className: 'meta',\n    begin: /^(>>>|\\.\\.\\.) /\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS,\n    illegal: /#/\n  };\n\n  const LITERAL_BRACKET = {\n    begin: /\\{\\{/,\n    relevance: 0\n  };\n\n  const STRING = {\n    className: 'string',\n    contains: [ hljs.BACKSLASH_ESCAPE ],\n    variants: [\n      {\n        begin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?'''/,\n        end: /'''/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT\n        ],\n        relevance: 10\n      },\n      {\n        begin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?\"\"\"/,\n        end: /\"\"\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT\n        ],\n        relevance: 10\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])'''/,\n        end: /'''/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])\"\"\"/,\n        end: /\"\"\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([uU]|[rR])'/,\n        end: /'/,\n        relevance: 10\n      },\n      {\n        begin: /([uU]|[rR])\"/,\n        end: /\"/,\n        relevance: 10\n      },\n      {\n        begin: /([bB]|[bB][rR]|[rR][bB])'/,\n        end: /'/\n      },\n      {\n        begin: /([bB]|[bB][rR]|[rR][bB])\"/,\n        end: /\"/\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])'/,\n        end: /'/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])\"/,\n        end: /\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n\n  // https://docs.python.org/3.9/reference/lexical_analysis.html#numeric-literals\n  const digitpart = '[0-9](_?[0-9])*';\n  const pointfloat = `(\\\\b(${digitpart}))?\\\\.(${digitpart})|\\\\b(${digitpart})\\\\.`;\n  // Whitespace after a number (or any lexical token) is needed only if its absence\n  // would change the tokenization\n  // https://docs.python.org/3.9/reference/lexical_analysis.html#whitespace-between-tokens\n  // We deviate slightly, requiring a word boundary or a keyword\n  // to avoid accidentally recognizing *prefixes* (e.g., `0` in `0x41` or `08` or `0__1`)\n  const lookahead = `\\\\b|${RESERVED_WORDS.join('|')}`;\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      // exponentfloat, pointfloat\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#floating-point-literals\n      // optionally imaginary\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      // Note: no leading \\b because floats can start with a decimal point\n      // and we don't want to mishandle e.g. `fn(.5)`,\n      // no trailing \\b for pointfloat because it can end with a decimal point\n      // and we don't want to mishandle e.g. `0..hex()`; this should be safe\n      // because both MUST contain a decimal point and so cannot be confused with\n      // the interior part of an identifier\n      {\n        begin: `(\\\\b(${digitpart})|(${pointfloat}))[eE][+-]?(${digitpart})[jJ]?(?=${lookahead})`\n      },\n      {\n        begin: `(${pointfloat})[jJ]?`\n      },\n\n      // decinteger, bininteger, octinteger, hexinteger\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#integer-literals\n      // optionally \"long\" in Python 2\n      // https://docs.python.org/2.7/reference/lexical_analysis.html#integer-and-long-integer-literals\n      // decinteger is optionally imaginary\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      {\n        begin: `\\\\b([1-9](_?[0-9])*|0+(_?0)*)[lLjJ]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[bB](_?[01])+[lL]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[oO](_?[0-7])+[lL]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[xX](_?[0-9a-fA-F])+[lL]?(?=${lookahead})`\n      },\n\n      // imagnumber (digitpart-based)\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      {\n        begin: `\\\\b(${digitpart})[jJ](?=${lookahead})`\n      }\n    ]\n  };\n  const COMMENT_TYPE = {\n    className: \"comment\",\n    begin: regex.lookahead(/# type:/),\n    end: /$/,\n    keywords: KEYWORDS,\n    contains: [\n      { // prevent keywords from coloring `type`\n        begin: /# type:/\n      },\n      // comment within a datatype comment includes no keywords\n      {\n        begin: /#/,\n        end: /\\b\\B/,\n        endsWithParent: true\n      }\n    ]\n  };\n  const PARAMS = {\n    className: 'params',\n    variants: [\n      // Exclude params in functions without params\n      {\n        className: \"\",\n        begin: /\\(\\s*\\)/,\n        skip: true\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        excludeBegin: true,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          'self',\n          PROMPT,\n          NUMBER,\n          STRING,\n          hljs.HASH_COMMENT_MODE\n        ]\n      }\n    ]\n  };\n  SUBST.contains = [\n    STRING,\n    NUMBER,\n    PROMPT\n  ];\n\n  return {\n    name: 'Python',\n    aliases: [\n      'py',\n      'gyp',\n      'ipython'\n    ],\n    unicodeRegex: true,\n    keywords: KEYWORDS,\n    illegal: /(<\\/|\\?)|=>/,\n    contains: [\n      PROMPT,\n      NUMBER,\n      {\n        // very common convention\n        begin: /\\bself\\b/\n      },\n      {\n        // eat \"if\" prior to string so that it won't accidentally be\n        // labeled as an f-string\n        beginKeywords: \"if\",\n        relevance: 0\n      },\n      STRING,\n      COMMENT_TYPE,\n      hljs.HASH_COMMENT_MODE,\n      {\n        match: [\n          /\\bdef/, /\\s+/,\n          IDENT_RE,\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.function\"\n        },\n        contains: [ PARAMS ]\n      },\n      {\n        variants: [\n          {\n            match: [\n              /\\bclass/, /\\s+/,\n              IDENT_RE, /\\s*/,\n              /\\(\\s*/, IDENT_RE,/\\s*\\)/\n            ],\n          },\n          {\n            match: [\n              /\\bclass/, /\\s+/,\n              IDENT_RE\n            ],\n          }\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\",\n          6: \"title.class.inherited\",\n        }\n      },\n      {\n        className: 'meta',\n        begin: /^[\\t ]*@/,\n        end: /(?=#)|$/,\n        contains: [\n          NUMBER,\n          PARAMS,\n          STRING\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = python;\n", "/*\nLanguage: Python REPL\nRequires: python.js\nAuthor: <PERSON> <<EMAIL>>\nCategory: common\n*/\n\nfunction pythonRepl(hljs) {\n  return {\n    aliases: [ 'pycon' ],\n    contains: [\n      {\n        className: 'meta.prompt',\n        starts: {\n          // a space separates the REPL prefix from the actual code\n          // this is purely for cleaner HTML output\n          end: / |$/,\n          starts: {\n            end: '$',\n            subLanguage: 'python'\n          }\n        },\n        variants: [\n          { begin: /^>>>(?=[ ]|$)/ },\n          { begin: /^\\.\\.\\.(?=[ ]|$)/ }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = pythonRepl;\n", "/*\nLanguage: R\nDescription: R is a free software environment for statistical computing and graphics.\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://www.r-project.org\nCategory: common,scientific\n*/\n\n/** @type LanguageFn */\nfunction r(hljs) {\n  const regex = hljs.regex;\n  // Identifiers in R cannot start with `_`, but they can start with `.` if it\n  // is not immediately followed by a digit.\n  // R also supports quoted identifiers, which are near-arbitrary sequences\n  // delimited by backticks (`…`), which may contain escape sequences. These are\n  // handled in a separate mode. See `test/markup/r/names.txt` for examples.\n  // FIXME: Support Unicode identifiers.\n  const IDENT_RE = /(?:(?:[a-zA-Z]|\\.[._a-zA-Z])[._a-zA-Z0-9]*)|\\.(?!\\d)/;\n  const NUMBER_TYPES_RE = regex.either(\n    // Special case: only hexadecimal binary powers can contain fractions\n    /0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/,\n    // Hexadecimal numbers without fraction and optional binary power\n    /0[xX][0-9a-fA-F]+(?:[pP][+-]?\\d+)?[Li]?/,\n    // Decimal numbers\n    /(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eE][+-]?\\d+)?[Li]?/\n  );\n  const OPERATORS_RE = /[=!<>:]=|\\|\\||&&|:::?|<-|<<-|->>|->|\\|>|[-+*\\/?!$&|:<=>@^~]|\\*\\*/;\n  const PUNCTUATION_RE = regex.either(\n    /[()]/,\n    /[{}]/,\n    /\\[\\[/,\n    /[[\\]]/,\n    /\\\\/,\n    /,/\n  );\n\n  return {\n    name: 'R',\n\n    keywords: {\n      $pattern: IDENT_RE,\n      keyword:\n        'function if in break next repeat else for while',\n      literal:\n        'NULL NA TRUE FALSE Inf NaN NA_integer_|10 NA_real_|10 '\n        + 'NA_character_|10 NA_complex_|10',\n      built_in:\n        // Builtin constants\n        'LETTERS letters month.abb month.name pi T F '\n        // Primitive functions\n        // These are all the functions in `base` that are implemented as a\n        // `.Primitive`, minus those functions that are also keywords.\n        + 'abs acos acosh all any anyNA Arg as.call as.character '\n        + 'as.complex as.double as.environment as.integer as.logical '\n        + 'as.null.default as.numeric as.raw asin asinh atan atanh attr '\n        + 'attributes baseenv browser c call ceiling class Conj cos cosh '\n        + 'cospi cummax cummin cumprod cumsum digamma dim dimnames '\n        + 'emptyenv exp expression floor forceAndCall gamma gc.time '\n        + 'globalenv Im interactive invisible is.array is.atomic is.call '\n        + 'is.character is.complex is.double is.environment is.expression '\n        + 'is.finite is.function is.infinite is.integer is.language '\n        + 'is.list is.logical is.matrix is.na is.name is.nan is.null '\n        + 'is.numeric is.object is.pairlist is.raw is.recursive is.single '\n        + 'is.symbol lazyLoadDBfetch length lgamma list log max min '\n        + 'missing Mod names nargs nzchar oldClass on.exit pos.to.env '\n        + 'proc.time prod quote range Re rep retracemem return round '\n        + 'seq_along seq_len seq.int sign signif sin sinh sinpi sqrt '\n        + 'standardGeneric substitute sum switch tan tanh tanpi tracemem '\n        + 'trigamma trunc unclass untracemem UseMethod xtfrm',\n    },\n\n    contains: [\n      // Roxygen comments\n      hljs.COMMENT(\n        /#'/,\n        /$/,\n        { contains: [\n          {\n            // Handle `@examples` separately to cause all subsequent code\n            // until the next `@`-tag on its own line to be kept as-is,\n            // preventing highlighting. This code is example R code, so nested\n            // doctags shouldn’t be treated as such. See\n            // `test/markup/r/roxygen.txt` for an example.\n            scope: 'doctag',\n            match: /@examples/,\n            starts: {\n              end: regex.lookahead(regex.either(\n                // end if another doc comment\n                /\\n^#'\\s*(?=@[a-zA-Z]+)/,\n                // or a line with no comment\n                /\\n^(?!#')/\n              )),\n              endsParent: true\n            }\n          },\n          {\n            // Handle `@param` to highlight the parameter name following\n            // after.\n            scope: 'doctag',\n            begin: '@param',\n            end: /$/,\n            contains: [\n              {\n                scope: 'variable',\n                variants: [\n                  { match: IDENT_RE },\n                  { match: /`(?:\\\\.|[^`\\\\])+`/ }\n                ],\n                endsParent: true\n              }\n            ]\n          },\n          {\n            scope: 'doctag',\n            match: /@[a-zA-Z]+/\n          },\n          {\n            scope: 'keyword',\n            match: /\\\\[a-zA-Z]+/\n          }\n        ] }\n      ),\n\n      hljs.HASH_COMMENT_MODE,\n\n      {\n        scope: 'string',\n        contains: [ hljs.BACKSLASH_ESCAPE ],\n        variants: [\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]\"(-*)\\(/,\n            end: /\\)(-*)\"/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]\"(-*)\\{/,\n            end: /\\}(-*)\"/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]\"(-*)\\[/,\n            end: /\\](-*)\"/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]'(-*)\\(/,\n            end: /\\)(-*)'/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]'(-*)\\{/,\n            end: /\\}(-*)'/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]'(-*)\\[/,\n            end: /\\](-*)'/\n          }),\n          {\n            begin: '\"',\n            end: '\"',\n            relevance: 0\n          },\n          {\n            begin: \"'\",\n            end: \"'\",\n            relevance: 0\n          }\n        ],\n      },\n\n      // Matching numbers immediately following punctuation and operators is\n      // tricky since we need to look at the character ahead of a number to\n      // ensure the number is not part of an identifier, and we cannot use\n      // negative look-behind assertions. So instead we explicitly handle all\n      // possible combinations of (operator|punctuation), number.\n      // TODO: replace with negative look-behind when available\n      // { begin: /(?<![a-zA-Z0-9._])0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/ },\n      // { begin: /(?<![a-zA-Z0-9._])0[xX][0-9a-fA-F]+([pP][+-]?\\d+)?[Li]?/ },\n      // { begin: /(?<![a-zA-Z0-9._])(\\d+(\\.\\d*)?|\\.\\d+)([eE][+-]?\\d+)?[Li]?/ }\n      {\n        relevance: 0,\n        variants: [\n          {\n            scope: {\n              1: 'operator',\n              2: 'number'\n            },\n            match: [\n              OPERATORS_RE,\n              NUMBER_TYPES_RE\n            ]\n          },\n          {\n            scope: {\n              1: 'operator',\n              2: 'number'\n            },\n            match: [\n              /%[^%]*%/,\n              NUMBER_TYPES_RE\n            ]\n          },\n          {\n            scope: {\n              1: 'punctuation',\n              2: 'number'\n            },\n            match: [\n              PUNCTUATION_RE,\n              NUMBER_TYPES_RE\n            ]\n          },\n          {\n            scope: { 2: 'number' },\n            match: [\n              /[^a-zA-Z0-9._]|^/, // not part of an identifier, or start of document\n              NUMBER_TYPES_RE\n            ]\n          }\n        ]\n      },\n\n      // Operators/punctuation when they're not directly followed by numbers\n      {\n        // Relevance boost for the most common assignment form.\n        scope: { 3: 'operator' },\n        match: [\n          IDENT_RE,\n          /\\s+/,\n          /<-/,\n          /\\s+/\n        ]\n      },\n\n      {\n        scope: 'operator',\n        relevance: 0,\n        variants: [\n          { match: OPERATORS_RE },\n          { match: /%[^%]*%/ }\n        ]\n      },\n\n      {\n        scope: 'punctuation',\n        relevance: 0,\n        match: PUNCTUATION_RE\n      },\n\n      {\n        // Escaped identifier\n        begin: '`',\n        end: '`',\n        contains: [ { begin: /\\\\./ } ]\n      }\n    ]\n  };\n}\n\nmodule.exports = r;\n", "/*\nLanguage: Rust\nAuthor: <PERSON><PERSON> <andrey.vlas<PERSON><EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.rust-lang.org\nCategory: common, system\n*/\n\n/** @type LanguageFn */\nfunction rust(hljs) {\n  const regex = hljs.regex;\n  const FUNCTION_INVOKE = {\n    className: \"title.function.invoke\",\n    relevance: 0,\n    begin: regex.concat(\n      /\\b/,\n      /(?!let|for|while|if|else|match\\b)/,\n      hljs.IDENT_RE,\n      regex.lookahead(/\\s*\\(/))\n  };\n  const NUMBER_SUFFIX = '([ui](8|16|32|64|128|size)|f(32|64))\\?';\n  const KEYWORDS = [\n    \"abstract\",\n    \"as\",\n    \"async\",\n    \"await\",\n    \"become\",\n    \"box\",\n    \"break\",\n    \"const\",\n    \"continue\",\n    \"crate\",\n    \"do\",\n    \"dyn\",\n    \"else\",\n    \"enum\",\n    \"extern\",\n    \"false\",\n    \"final\",\n    \"fn\",\n    \"for\",\n    \"if\",\n    \"impl\",\n    \"in\",\n    \"let\",\n    \"loop\",\n    \"macro\",\n    \"match\",\n    \"mod\",\n    \"move\",\n    \"mut\",\n    \"override\",\n    \"priv\",\n    \"pub\",\n    \"ref\",\n    \"return\",\n    \"self\",\n    \"Self\",\n    \"static\",\n    \"struct\",\n    \"super\",\n    \"trait\",\n    \"true\",\n    \"try\",\n    \"type\",\n    \"typeof\",\n    \"unsafe\",\n    \"unsized\",\n    \"use\",\n    \"virtual\",\n    \"where\",\n    \"while\",\n    \"yield\"\n  ];\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"Some\",\n    \"None\",\n    \"Ok\",\n    \"Err\"\n  ];\n  const BUILTINS = [\n    // functions\n    'drop ',\n    // traits\n    \"Copy\",\n    \"Send\",\n    \"Sized\",\n    \"Sync\",\n    \"Drop\",\n    \"Fn\",\n    \"FnMut\",\n    \"FnOnce\",\n    \"ToOwned\",\n    \"Clone\",\n    \"Debug\",\n    \"PartialEq\",\n    \"PartialOrd\",\n    \"Eq\",\n    \"Ord\",\n    \"AsRef\",\n    \"AsMut\",\n    \"Into\",\n    \"From\",\n    \"Default\",\n    \"Iterator\",\n    \"Extend\",\n    \"IntoIterator\",\n    \"DoubleEndedIterator\",\n    \"ExactSizeIterator\",\n    \"SliceConcatExt\",\n    \"ToString\",\n    // macros\n    \"assert!\",\n    \"assert_eq!\",\n    \"bitflags!\",\n    \"bytes!\",\n    \"cfg!\",\n    \"col!\",\n    \"concat!\",\n    \"concat_idents!\",\n    \"debug_assert!\",\n    \"debug_assert_eq!\",\n    \"env!\",\n    \"eprintln!\",\n    \"panic!\",\n    \"file!\",\n    \"format!\",\n    \"format_args!\",\n    \"include_bytes!\",\n    \"include_str!\",\n    \"line!\",\n    \"local_data_key!\",\n    \"module_path!\",\n    \"option_env!\",\n    \"print!\",\n    \"println!\",\n    \"select!\",\n    \"stringify!\",\n    \"try!\",\n    \"unimplemented!\",\n    \"unreachable!\",\n    \"vec!\",\n    \"write!\",\n    \"writeln!\",\n    \"macro_rules!\",\n    \"assert_ne!\",\n    \"debug_assert_ne!\"\n  ];\n  const TYPES = [\n    \"i8\",\n    \"i16\",\n    \"i32\",\n    \"i64\",\n    \"i128\",\n    \"isize\",\n    \"u8\",\n    \"u16\",\n    \"u32\",\n    \"u64\",\n    \"u128\",\n    \"usize\",\n    \"f32\",\n    \"f64\",\n    \"str\",\n    \"char\",\n    \"bool\",\n    \"Box\",\n    \"Option\",\n    \"Result\",\n    \"String\",\n    \"Vec\"\n  ];\n  return {\n    name: 'Rust',\n    aliases: [ 'rs' ],\n    keywords: {\n      $pattern: hljs.IDENT_RE + '!?',\n      type: TYPES,\n      keyword: KEYWORDS,\n      literal: LITERALS,\n      built_in: BUILTINS\n    },\n    illegal: '</',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.COMMENT('/\\\\*', '\\\\*/', { contains: [ 'self' ] }),\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        begin: /b?\"/,\n        illegal: null\n      }),\n      {\n        className: 'string',\n        variants: [\n          { begin: /b?r(#*)\"(.|\\n)*?\"\\1(?!#)/ },\n          { begin: /b?'\\\\?(x\\w{2}|u\\w{4}|U\\w{8}|.)'/ }\n        ]\n      },\n      {\n        className: 'symbol',\n        begin: /'[a-zA-Z_][a-zA-Z0-9_]*/\n      },\n      {\n        className: 'number',\n        variants: [\n          { begin: '\\\\b0b([01_]+)' + NUMBER_SUFFIX },\n          { begin: '\\\\b0o([0-7_]+)' + NUMBER_SUFFIX },\n          { begin: '\\\\b0x([A-Fa-f0-9_]+)' + NUMBER_SUFFIX },\n          { begin: '\\\\b(\\\\d[\\\\d_]*(\\\\.[0-9_]+)?([eE][+-]?[0-9_]+)?)'\n                   + NUMBER_SUFFIX }\n        ],\n        relevance: 0\n      },\n      {\n        begin: [\n          /fn/,\n          /\\s+/,\n          hljs.UNDERSCORE_IDENT_RE\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"title.function\"\n        }\n      },\n      {\n        className: 'meta',\n        begin: '#!?\\\\[',\n        end: '\\\\]',\n        contains: [\n          {\n            className: 'string',\n            begin: /\"/,\n            end: /\"/\n          }\n        ]\n      },\n      {\n        begin: [\n          /let/,\n          /\\s+/,\n          /(?:mut\\s+)?/,\n          hljs.UNDERSCORE_IDENT_RE\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"keyword\",\n          4: \"variable\"\n        }\n      },\n      // must come before impl/for rule later\n      {\n        begin: [\n          /for/,\n          /\\s+/,\n          hljs.UNDERSCORE_IDENT_RE,\n          /\\s+/,\n          /in/\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"variable\",\n          5: \"keyword\"\n        }\n      },\n      {\n        begin: [\n          /type/,\n          /\\s+/,\n          hljs.UNDERSCORE_IDENT_RE\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n      {\n        begin: [\n          /(?:trait|enum|struct|union|impl|for)/,\n          /\\s+/,\n          hljs.UNDERSCORE_IDENT_RE\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n      {\n        begin: hljs.IDENT_RE + '::',\n        keywords: {\n          keyword: \"Self\",\n          built_in: BUILTINS,\n          type: TYPES\n        }\n      },\n      {\n        className: \"punctuation\",\n        begin: '->'\n      },\n      FUNCTION_INVOKE\n    ]\n  };\n}\n\nmodule.exports = rust;\n", "const MODES = (hljs) => {\n  return {\n    IMPORTANT: {\n      scope: 'meta',\n      begin: '!important'\n    },\n    BLOCK_COMMENT: hljs.C_BLOCK_COMMENT_MODE,\n    HEXCOLOR: {\n      scope: 'number',\n      begin: /#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\\b/\n    },\n    FUNCTION_DISPATCH: {\n      className: \"built_in\",\n      begin: /[\\w-]+(?=\\()/\n    },\n    ATTRIBUTE_SELECTOR_MODE: {\n      scope: 'selector-attr',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: '$',\n      contains: [\n        hljs.APOS_STRING_MODE,\n        hljs.QUOTE_STRING_MODE\n      ]\n    },\n    CSS_NUMBER_MODE: {\n      scope: 'number',\n      begin: hljs.NUMBER_RE + '(' +\n        '%|em|ex|ch|rem' +\n        '|vw|vh|vmin|vmax' +\n        '|cm|mm|in|pt|pc|px' +\n        '|deg|grad|rad|turn' +\n        '|s|ms' +\n        '|Hz|kHz' +\n        '|dpi|dpcm|dppx' +\n        ')?',\n      relevance: 0\n    },\n    CSS_VARIABLE: {\n      className: \"attr\",\n      begin: /--[A-Za-z_][A-Za-z0-9_-]*/\n    }\n  };\n};\n\nconst TAGS = [\n  'a',\n  'abbr',\n  'address',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'blockquote',\n  'body',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'mark',\n  'menu',\n  'nav',\n  'object',\n  'ol',\n  'p',\n  'q',\n  'quote',\n  'samp',\n  'section',\n  'span',\n  'strong',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'ul',\n  'var',\n  'video'\n];\n\nconst MEDIA_FEATURES = [\n  'any-hover',\n  'any-pointer',\n  'aspect-ratio',\n  'color',\n  'color-gamut',\n  'color-index',\n  'device-aspect-ratio',\n  'device-height',\n  'device-width',\n  'display-mode',\n  'forced-colors',\n  'grid',\n  'height',\n  'hover',\n  'inverted-colors',\n  'monochrome',\n  'orientation',\n  'overflow-block',\n  'overflow-inline',\n  'pointer',\n  'prefers-color-scheme',\n  'prefers-contrast',\n  'prefers-reduced-motion',\n  'prefers-reduced-transparency',\n  'resolution',\n  'scan',\n  'scripting',\n  'update',\n  'width',\n  // TODO: find a better solution?\n  'min-width',\n  'max-width',\n  'min-height',\n  'max-height'\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes\nconst PSEUDO_CLASSES = [\n  'active',\n  'any-link',\n  'blank',\n  'checked',\n  'current',\n  'default',\n  'defined',\n  'dir', // dir()\n  'disabled',\n  'drop',\n  'empty',\n  'enabled',\n  'first',\n  'first-child',\n  'first-of-type',\n  'fullscreen',\n  'future',\n  'focus',\n  'focus-visible',\n  'focus-within',\n  'has', // has()\n  'host', // host or host()\n  'host-context', // host-context()\n  'hover',\n  'indeterminate',\n  'in-range',\n  'invalid',\n  'is', // is()\n  'lang', // lang()\n  'last-child',\n  'last-of-type',\n  'left',\n  'link',\n  'local-link',\n  'not', // not()\n  'nth-child', // nth-child()\n  'nth-col', // nth-col()\n  'nth-last-child', // nth-last-child()\n  'nth-last-col', // nth-last-col()\n  'nth-last-of-type', //nth-last-of-type()\n  'nth-of-type', //nth-of-type()\n  'only-child',\n  'only-of-type',\n  'optional',\n  'out-of-range',\n  'past',\n  'placeholder-shown',\n  'read-only',\n  'read-write',\n  'required',\n  'right',\n  'root',\n  'scope',\n  'target',\n  'target-within',\n  'user-invalid',\n  'valid',\n  'visited',\n  'where' // where()\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-elements\nconst PSEUDO_ELEMENTS = [\n  'after',\n  'backdrop',\n  'before',\n  'cue',\n  'cue-region',\n  'first-letter',\n  'first-line',\n  'grammar-error',\n  'marker',\n  'part',\n  'placeholder',\n  'selection',\n  'slotted',\n  'spelling-error'\n];\n\nconst ATTRIBUTES = [\n  'align-content',\n  'align-items',\n  'align-self',\n  'all',\n  'animation',\n  'animation-delay',\n  'animation-direction',\n  'animation-duration',\n  'animation-fill-mode',\n  'animation-iteration-count',\n  'animation-name',\n  'animation-play-state',\n  'animation-timing-function',\n  'backface-visibility',\n  'background',\n  'background-attachment',\n  'background-blend-mode',\n  'background-clip',\n  'background-color',\n  'background-image',\n  'background-origin',\n  'background-position',\n  'background-repeat',\n  'background-size',\n  'block-size',\n  'border',\n  'border-block',\n  'border-block-color',\n  'border-block-end',\n  'border-block-end-color',\n  'border-block-end-style',\n  'border-block-end-width',\n  'border-block-start',\n  'border-block-start-color',\n  'border-block-start-style',\n  'border-block-start-width',\n  'border-block-style',\n  'border-block-width',\n  'border-bottom',\n  'border-bottom-color',\n  'border-bottom-left-radius',\n  'border-bottom-right-radius',\n  'border-bottom-style',\n  'border-bottom-width',\n  'border-collapse',\n  'border-color',\n  'border-image',\n  'border-image-outset',\n  'border-image-repeat',\n  'border-image-slice',\n  'border-image-source',\n  'border-image-width',\n  'border-inline',\n  'border-inline-color',\n  'border-inline-end',\n  'border-inline-end-color',\n  'border-inline-end-style',\n  'border-inline-end-width',\n  'border-inline-start',\n  'border-inline-start-color',\n  'border-inline-start-style',\n  'border-inline-start-width',\n  'border-inline-style',\n  'border-inline-width',\n  'border-left',\n  'border-left-color',\n  'border-left-style',\n  'border-left-width',\n  'border-radius',\n  'border-right',\n  'border-right-color',\n  'border-right-style',\n  'border-right-width',\n  'border-spacing',\n  'border-style',\n  'border-top',\n  'border-top-color',\n  'border-top-left-radius',\n  'border-top-right-radius',\n  'border-top-style',\n  'border-top-width',\n  'border-width',\n  'bottom',\n  'box-decoration-break',\n  'box-shadow',\n  'box-sizing',\n  'break-after',\n  'break-before',\n  'break-inside',\n  'caption-side',\n  'caret-color',\n  'clear',\n  'clip',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'column-count',\n  'column-fill',\n  'column-gap',\n  'column-rule',\n  'column-rule-color',\n  'column-rule-style',\n  'column-rule-width',\n  'column-span',\n  'column-width',\n  'columns',\n  'contain',\n  'content',\n  'content-visibility',\n  'counter-increment',\n  'counter-reset',\n  'cue',\n  'cue-after',\n  'cue-before',\n  'cursor',\n  'direction',\n  'display',\n  'empty-cells',\n  'filter',\n  'flex',\n  'flex-basis',\n  'flex-direction',\n  'flex-flow',\n  'flex-grow',\n  'flex-shrink',\n  'flex-wrap',\n  'float',\n  'flow',\n  'font',\n  'font-display',\n  'font-family',\n  'font-feature-settings',\n  'font-kerning',\n  'font-language-override',\n  'font-size',\n  'font-size-adjust',\n  'font-smoothing',\n  'font-stretch',\n  'font-style',\n  'font-synthesis',\n  'font-variant',\n  'font-variant-caps',\n  'font-variant-east-asian',\n  'font-variant-ligatures',\n  'font-variant-numeric',\n  'font-variant-position',\n  'font-variation-settings',\n  'font-weight',\n  'gap',\n  'glyph-orientation-vertical',\n  'grid',\n  'grid-area',\n  'grid-auto-columns',\n  'grid-auto-flow',\n  'grid-auto-rows',\n  'grid-column',\n  'grid-column-end',\n  'grid-column-start',\n  'grid-gap',\n  'grid-row',\n  'grid-row-end',\n  'grid-row-start',\n  'grid-template',\n  'grid-template-areas',\n  'grid-template-columns',\n  'grid-template-rows',\n  'hanging-punctuation',\n  'height',\n  'hyphens',\n  'icon',\n  'image-orientation',\n  'image-rendering',\n  'image-resolution',\n  'ime-mode',\n  'inline-size',\n  'isolation',\n  'justify-content',\n  'left',\n  'letter-spacing',\n  'line-break',\n  'line-height',\n  'list-style',\n  'list-style-image',\n  'list-style-position',\n  'list-style-type',\n  'margin',\n  'margin-block',\n  'margin-block-end',\n  'margin-block-start',\n  'margin-bottom',\n  'margin-inline',\n  'margin-inline-end',\n  'margin-inline-start',\n  'margin-left',\n  'margin-right',\n  'margin-top',\n  'marks',\n  'mask',\n  'mask-border',\n  'mask-border-mode',\n  'mask-border-outset',\n  'mask-border-repeat',\n  'mask-border-slice',\n  'mask-border-source',\n  'mask-border-width',\n  'mask-clip',\n  'mask-composite',\n  'mask-image',\n  'mask-mode',\n  'mask-origin',\n  'mask-position',\n  'mask-repeat',\n  'mask-size',\n  'mask-type',\n  'max-block-size',\n  'max-height',\n  'max-inline-size',\n  'max-width',\n  'min-block-size',\n  'min-height',\n  'min-inline-size',\n  'min-width',\n  'mix-blend-mode',\n  'nav-down',\n  'nav-index',\n  'nav-left',\n  'nav-right',\n  'nav-up',\n  'none',\n  'normal',\n  'object-fit',\n  'object-position',\n  'opacity',\n  'order',\n  'orphans',\n  'outline',\n  'outline-color',\n  'outline-offset',\n  'outline-style',\n  'outline-width',\n  'overflow',\n  'overflow-wrap',\n  'overflow-x',\n  'overflow-y',\n  'padding',\n  'padding-block',\n  'padding-block-end',\n  'padding-block-start',\n  'padding-bottom',\n  'padding-inline',\n  'padding-inline-end',\n  'padding-inline-start',\n  'padding-left',\n  'padding-right',\n  'padding-top',\n  'page-break-after',\n  'page-break-before',\n  'page-break-inside',\n  'pause',\n  'pause-after',\n  'pause-before',\n  'perspective',\n  'perspective-origin',\n  'pointer-events',\n  'position',\n  'quotes',\n  'resize',\n  'rest',\n  'rest-after',\n  'rest-before',\n  'right',\n  'row-gap',\n  'scroll-margin',\n  'scroll-margin-block',\n  'scroll-margin-block-end',\n  'scroll-margin-block-start',\n  'scroll-margin-bottom',\n  'scroll-margin-inline',\n  'scroll-margin-inline-end',\n  'scroll-margin-inline-start',\n  'scroll-margin-left',\n  'scroll-margin-right',\n  'scroll-margin-top',\n  'scroll-padding',\n  'scroll-padding-block',\n  'scroll-padding-block-end',\n  'scroll-padding-block-start',\n  'scroll-padding-bottom',\n  'scroll-padding-inline',\n  'scroll-padding-inline-end',\n  'scroll-padding-inline-start',\n  'scroll-padding-left',\n  'scroll-padding-right',\n  'scroll-padding-top',\n  'scroll-snap-align',\n  'scroll-snap-stop',\n  'scroll-snap-type',\n  'scrollbar-color',\n  'scrollbar-gutter',\n  'scrollbar-width',\n  'shape-image-threshold',\n  'shape-margin',\n  'shape-outside',\n  'speak',\n  'speak-as',\n  'src', // @font-face\n  'tab-size',\n  'table-layout',\n  'text-align',\n  'text-align-all',\n  'text-align-last',\n  'text-combine-upright',\n  'text-decoration',\n  'text-decoration-color',\n  'text-decoration-line',\n  'text-decoration-style',\n  'text-emphasis',\n  'text-emphasis-color',\n  'text-emphasis-position',\n  'text-emphasis-style',\n  'text-indent',\n  'text-justify',\n  'text-orientation',\n  'text-overflow',\n  'text-rendering',\n  'text-shadow',\n  'text-transform',\n  'text-underline-position',\n  'top',\n  'transform',\n  'transform-box',\n  'transform-origin',\n  'transform-style',\n  'transition',\n  'transition-delay',\n  'transition-duration',\n  'transition-property',\n  'transition-timing-function',\n  'unicode-bidi',\n  'vertical-align',\n  'visibility',\n  'voice-balance',\n  'voice-duration',\n  'voice-family',\n  'voice-pitch',\n  'voice-range',\n  'voice-rate',\n  'voice-stress',\n  'voice-volume',\n  'white-space',\n  'widows',\n  'width',\n  'will-change',\n  'word-break',\n  'word-spacing',\n  'word-wrap',\n  'writing-mode',\n  'z-index'\n  // reverse makes sure longer attributes `font-weight` are matched fully\n  // instead of getting false positives on say `font`\n].reverse();\n\n/*\nLanguage: SCSS\nDescription: Scss is an extension of the syntax of CSS.\nAuthor: Kurt Emch <<EMAIL>>\nWebsite: https://sass-lang.com\nCategory: common, css, web\n*/\n\n\n/** @type LanguageFn */\nfunction scss(hljs) {\n  const modes = MODES(hljs);\n  const PSEUDO_ELEMENTS$1 = PSEUDO_ELEMENTS;\n  const PSEUDO_CLASSES$1 = PSEUDO_CLASSES;\n\n  const AT_IDENTIFIER = '@[a-z-]+'; // @font-face\n  const AT_MODIFIERS = \"and or not only\";\n  const IDENT_RE = '[a-zA-Z-][a-zA-Z0-9_-]*';\n  const VARIABLE = {\n    className: 'variable',\n    begin: '(\\\\$' + IDENT_RE + ')\\\\b',\n    relevance: 0\n  };\n\n  return {\n    name: 'SCSS',\n    case_insensitive: true,\n    illegal: '[=/|\\']',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      // to recognize keyframe 40% etc which are outside the scope of our\n      // attribute value mode\n      modes.CSS_NUMBER_MODE,\n      {\n        className: 'selector-id',\n        begin: '#[A-Za-z0-9_-]+',\n        relevance: 0\n      },\n      {\n        className: 'selector-class',\n        begin: '\\\\.[A-Za-z0-9_-]+',\n        relevance: 0\n      },\n      modes.ATTRIBUTE_SELECTOR_MODE,\n      {\n        className: 'selector-tag',\n        begin: '\\\\b(' + TAGS.join('|') + ')\\\\b',\n        // was there, before, but why?\n        relevance: 0\n      },\n      {\n        className: 'selector-pseudo',\n        begin: ':(' + PSEUDO_CLASSES$1.join('|') + ')'\n      },\n      {\n        className: 'selector-pseudo',\n        begin: ':(:)?(' + PSEUDO_ELEMENTS$1.join('|') + ')'\n      },\n      VARIABLE,\n      { // pseudo-selector params\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [ modes.CSS_NUMBER_MODE ]\n      },\n      modes.CSS_VARIABLE,\n      {\n        className: 'attribute',\n        begin: '\\\\b(' + ATTRIBUTES.join('|') + ')\\\\b'\n      },\n      { begin: '\\\\b(whitespace|wait|w-resize|visible|vertical-text|vertical-ideographic|uppercase|upper-roman|upper-alpha|underline|transparent|top|thin|thick|text|text-top|text-bottom|tb-rl|table-header-group|table-footer-group|sw-resize|super|strict|static|square|solid|small-caps|separate|se-resize|scroll|s-resize|rtl|row-resize|ridge|right|repeat|repeat-y|repeat-x|relative|progress|pointer|overline|outside|outset|oblique|nowrap|not-allowed|normal|none|nw-resize|no-repeat|no-drop|newspaper|ne-resize|n-resize|move|middle|medium|ltr|lr-tb|lowercase|lower-roman|lower-alpha|loose|list-item|line|line-through|line-edge|lighter|left|keep-all|justify|italic|inter-word|inter-ideograph|inside|inset|inline|inline-block|inherit|inactive|ideograph-space|ideograph-parenthesis|ideograph-numeric|ideograph-alpha|horizontal|hidden|help|hand|groove|fixed|ellipsis|e-resize|double|dotted|distribute|distribute-space|distribute-letter|distribute-all-lines|disc|disabled|default|decimal|dashed|crosshair|collapse|col-resize|circle|char|center|capitalize|break-word|break-all|bottom|both|bolder|bold|block|bidi-override|below|baseline|auto|always|all-scroll|absolute|table|table-cell)\\\\b' },\n      {\n        begin: /:/,\n        end: /[;}{]/,\n        relevance: 0,\n        contains: [\n          modes.BLOCK_COMMENT,\n          VARIABLE,\n          modes.HEXCOLOR,\n          modes.CSS_NUMBER_MODE,\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          modes.IMPORTANT,\n          modes.FUNCTION_DISPATCH\n        ]\n      },\n      // matching these here allows us to treat them more like regular CSS\n      // rules so everything between the {} gets regular rule highlighting,\n      // which is what we want for page and font-face\n      {\n        begin: '@(page|font-face)',\n        keywords: {\n          $pattern: AT_IDENTIFIER,\n          keyword: '@page @font-face'\n        }\n      },\n      {\n        begin: '@',\n        end: '[{;]',\n        returnBegin: true,\n        keywords: {\n          $pattern: /[a-z-]+/,\n          keyword: AT_MODIFIERS,\n          attribute: MEDIA_FEATURES.join(\" \")\n        },\n        contains: [\n          {\n            begin: AT_IDENTIFIER,\n            className: \"keyword\"\n          },\n          {\n            begin: /[a-z-]+(?=:)/,\n            className: \"attribute\"\n          },\n          VARIABLE,\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          modes.HEXCOLOR,\n          modes.CSS_NUMBER_MODE\n        ]\n      },\n      modes.FUNCTION_DISPATCH\n    ]\n  };\n}\n\nmodule.exports = scss;\n", "/*\nLanguage: Shell Session\nRequires: bash.js\nAuthor: T<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nCategory: common\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction shell(hljs) {\n  return {\n    name: 'Shell Session',\n    aliases: [\n      'console',\n      'shellsession'\n    ],\n    contains: [\n      {\n        className: 'meta.prompt',\n        // We cannot add \\s (spaces) in the regular expression otherwise it will be too broad and produce unexpected result.\n        // For instance, in the following example, it would match \"echo /path/to/home >\" as a prompt:\n        // echo /path/to/home > t.exe\n        begin: /^\\s{0,3}[/~\\w\\d[\\]()@-]*[>%$#][ ]?/,\n        starts: {\n          end: /[^\\\\](?=\\s*$)/,\n          subLanguage: 'bash'\n        }\n      }\n    ]\n  };\n}\n\nmodule.exports = shell;\n", "/*\n Language: SQL\n Website: https://en.wikipedia.org/wiki/SQL\n Category: common, database\n */\n\n/*\n\nGoals:\n\nSQL is intended to highlight basic/common SQL keywords and expressions\n\n- If pretty much every single SQL server includes supports, then it's a canidate.\n- It is NOT intended to include tons of vendor specific keywords (Oracle, MySQL,\n  PostgreSQL) although the list of data types is purposely a bit more expansive.\n- For more specific SQL grammars please see:\n  - PostgreSQL and PL/pgSQL - core\n  - T-SQL - https://github.com/highlightjs/highlightjs-tsql\n  - sql_more (core)\n\n */\n\nfunction sql(hljs) {\n  const regex = hljs.regex;\n  const COMMENT_MODE = hljs.COMMENT('--', '$');\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: /'/,\n        end: /'/,\n        contains: [ { begin: /''/ } ]\n      }\n    ]\n  };\n  const QUOTED_IDENTIFIER = {\n    begin: /\"/,\n    end: /\"/,\n    contains: [ { begin: /\"\"/ } ]\n  };\n\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    // Not sure it's correct to call NULL literal, and clauses like IS [NOT] NULL look strange that way.\n    // \"null\",\n    \"unknown\"\n  ];\n\n  const MULTI_WORD_TYPES = [\n    \"double precision\",\n    \"large object\",\n    \"with timezone\",\n    \"without timezone\"\n  ];\n\n  const TYPES = [\n    'bigint',\n    'binary',\n    'blob',\n    'boolean',\n    'char',\n    'character',\n    'clob',\n    'date',\n    'dec',\n    'decfloat',\n    'decimal',\n    'float',\n    'int',\n    'integer',\n    'interval',\n    'nchar',\n    'nclob',\n    'national',\n    'numeric',\n    'real',\n    'row',\n    'smallint',\n    'time',\n    'timestamp',\n    'varchar',\n    'varying', // modifier (character varying)\n    'varbinary'\n  ];\n\n  const NON_RESERVED_WORDS = [\n    \"add\",\n    \"asc\",\n    \"collation\",\n    \"desc\",\n    \"final\",\n    \"first\",\n    \"last\",\n    \"view\"\n  ];\n\n  // https://jakewheat.github.io/sql-overview/sql-2016-foundation-grammar.html#reserved-word\n  const RESERVED_WORDS = [\n    \"abs\",\n    \"acos\",\n    \"all\",\n    \"allocate\",\n    \"alter\",\n    \"and\",\n    \"any\",\n    \"are\",\n    \"array\",\n    \"array_agg\",\n    \"array_max_cardinality\",\n    \"as\",\n    \"asensitive\",\n    \"asin\",\n    \"asymmetric\",\n    \"at\",\n    \"atan\",\n    \"atomic\",\n    \"authorization\",\n    \"avg\",\n    \"begin\",\n    \"begin_frame\",\n    \"begin_partition\",\n    \"between\",\n    \"bigint\",\n    \"binary\",\n    \"blob\",\n    \"boolean\",\n    \"both\",\n    \"by\",\n    \"call\",\n    \"called\",\n    \"cardinality\",\n    \"cascaded\",\n    \"case\",\n    \"cast\",\n    \"ceil\",\n    \"ceiling\",\n    \"char\",\n    \"char_length\",\n    \"character\",\n    \"character_length\",\n    \"check\",\n    \"classifier\",\n    \"clob\",\n    \"close\",\n    \"coalesce\",\n    \"collate\",\n    \"collect\",\n    \"column\",\n    \"commit\",\n    \"condition\",\n    \"connect\",\n    \"constraint\",\n    \"contains\",\n    \"convert\",\n    \"copy\",\n    \"corr\",\n    \"corresponding\",\n    \"cos\",\n    \"cosh\",\n    \"count\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"create\",\n    \"cross\",\n    \"cube\",\n    \"cume_dist\",\n    \"current\",\n    \"current_catalog\",\n    \"current_date\",\n    \"current_default_transform_group\",\n    \"current_path\",\n    \"current_role\",\n    \"current_row\",\n    \"current_schema\",\n    \"current_time\",\n    \"current_timestamp\",\n    \"current_path\",\n    \"current_role\",\n    \"current_transform_group_for_type\",\n    \"current_user\",\n    \"cursor\",\n    \"cycle\",\n    \"date\",\n    \"day\",\n    \"deallocate\",\n    \"dec\",\n    \"decimal\",\n    \"decfloat\",\n    \"declare\",\n    \"default\",\n    \"define\",\n    \"delete\",\n    \"dense_rank\",\n    \"deref\",\n    \"describe\",\n    \"deterministic\",\n    \"disconnect\",\n    \"distinct\",\n    \"double\",\n    \"drop\",\n    \"dynamic\",\n    \"each\",\n    \"element\",\n    \"else\",\n    \"empty\",\n    \"end\",\n    \"end_frame\",\n    \"end_partition\",\n    \"end-exec\",\n    \"equals\",\n    \"escape\",\n    \"every\",\n    \"except\",\n    \"exec\",\n    \"execute\",\n    \"exists\",\n    \"exp\",\n    \"external\",\n    \"extract\",\n    \"false\",\n    \"fetch\",\n    \"filter\",\n    \"first_value\",\n    \"float\",\n    \"floor\",\n    \"for\",\n    \"foreign\",\n    \"frame_row\",\n    \"free\",\n    \"from\",\n    \"full\",\n    \"function\",\n    \"fusion\",\n    \"get\",\n    \"global\",\n    \"grant\",\n    \"group\",\n    \"grouping\",\n    \"groups\",\n    \"having\",\n    \"hold\",\n    \"hour\",\n    \"identity\",\n    \"in\",\n    \"indicator\",\n    \"initial\",\n    \"inner\",\n    \"inout\",\n    \"insensitive\",\n    \"insert\",\n    \"int\",\n    \"integer\",\n    \"intersect\",\n    \"intersection\",\n    \"interval\",\n    \"into\",\n    \"is\",\n    \"join\",\n    \"json_array\",\n    \"json_arrayagg\",\n    \"json_exists\",\n    \"json_object\",\n    \"json_objectagg\",\n    \"json_query\",\n    \"json_table\",\n    \"json_table_primitive\",\n    \"json_value\",\n    \"lag\",\n    \"language\",\n    \"large\",\n    \"last_value\",\n    \"lateral\",\n    \"lead\",\n    \"leading\",\n    \"left\",\n    \"like\",\n    \"like_regex\",\n    \"listagg\",\n    \"ln\",\n    \"local\",\n    \"localtime\",\n    \"localtimestamp\",\n    \"log\",\n    \"log10\",\n    \"lower\",\n    \"match\",\n    \"match_number\",\n    \"match_recognize\",\n    \"matches\",\n    \"max\",\n    \"member\",\n    \"merge\",\n    \"method\",\n    \"min\",\n    \"minute\",\n    \"mod\",\n    \"modifies\",\n    \"module\",\n    \"month\",\n    \"multiset\",\n    \"national\",\n    \"natural\",\n    \"nchar\",\n    \"nclob\",\n    \"new\",\n    \"no\",\n    \"none\",\n    \"normalize\",\n    \"not\",\n    \"nth_value\",\n    \"ntile\",\n    \"null\",\n    \"nullif\",\n    \"numeric\",\n    \"octet_length\",\n    \"occurrences_regex\",\n    \"of\",\n    \"offset\",\n    \"old\",\n    \"omit\",\n    \"on\",\n    \"one\",\n    \"only\",\n    \"open\",\n    \"or\",\n    \"order\",\n    \"out\",\n    \"outer\",\n    \"over\",\n    \"overlaps\",\n    \"overlay\",\n    \"parameter\",\n    \"partition\",\n    \"pattern\",\n    \"per\",\n    \"percent\",\n    \"percent_rank\",\n    \"percentile_cont\",\n    \"percentile_disc\",\n    \"period\",\n    \"portion\",\n    \"position\",\n    \"position_regex\",\n    \"power\",\n    \"precedes\",\n    \"precision\",\n    \"prepare\",\n    \"primary\",\n    \"procedure\",\n    \"ptf\",\n    \"range\",\n    \"rank\",\n    \"reads\",\n    \"real\",\n    \"recursive\",\n    \"ref\",\n    \"references\",\n    \"referencing\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"release\",\n    \"result\",\n    \"return\",\n    \"returns\",\n    \"revoke\",\n    \"right\",\n    \"rollback\",\n    \"rollup\",\n    \"row\",\n    \"row_number\",\n    \"rows\",\n    \"running\",\n    \"savepoint\",\n    \"scope\",\n    \"scroll\",\n    \"search\",\n    \"second\",\n    \"seek\",\n    \"select\",\n    \"sensitive\",\n    \"session_user\",\n    \"set\",\n    \"show\",\n    \"similar\",\n    \"sin\",\n    \"sinh\",\n    \"skip\",\n    \"smallint\",\n    \"some\",\n    \"specific\",\n    \"specifictype\",\n    \"sql\",\n    \"sqlexception\",\n    \"sqlstate\",\n    \"sqlwarning\",\n    \"sqrt\",\n    \"start\",\n    \"static\",\n    \"stddev_pop\",\n    \"stddev_samp\",\n    \"submultiset\",\n    \"subset\",\n    \"substring\",\n    \"substring_regex\",\n    \"succeeds\",\n    \"sum\",\n    \"symmetric\",\n    \"system\",\n    \"system_time\",\n    \"system_user\",\n    \"table\",\n    \"tablesample\",\n    \"tan\",\n    \"tanh\",\n    \"then\",\n    \"time\",\n    \"timestamp\",\n    \"timezone_hour\",\n    \"timezone_minute\",\n    \"to\",\n    \"trailing\",\n    \"translate\",\n    \"translate_regex\",\n    \"translation\",\n    \"treat\",\n    \"trigger\",\n    \"trim\",\n    \"trim_array\",\n    \"true\",\n    \"truncate\",\n    \"uescape\",\n    \"union\",\n    \"unique\",\n    \"unknown\",\n    \"unnest\",\n    \"update\",\n    \"upper\",\n    \"user\",\n    \"using\",\n    \"value\",\n    \"values\",\n    \"value_of\",\n    \"var_pop\",\n    \"var_samp\",\n    \"varbinary\",\n    \"varchar\",\n    \"varying\",\n    \"versioning\",\n    \"when\",\n    \"whenever\",\n    \"where\",\n    \"width_bucket\",\n    \"window\",\n    \"with\",\n    \"within\",\n    \"without\",\n    \"year\",\n  ];\n\n  // these are reserved words we have identified to be functions\n  // and should only be highlighted in a dispatch-like context\n  // ie, array_agg(...), etc.\n  const RESERVED_FUNCTIONS = [\n    \"abs\",\n    \"acos\",\n    \"array_agg\",\n    \"asin\",\n    \"atan\",\n    \"avg\",\n    \"cast\",\n    \"ceil\",\n    \"ceiling\",\n    \"coalesce\",\n    \"corr\",\n    \"cos\",\n    \"cosh\",\n    \"count\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"cume_dist\",\n    \"dense_rank\",\n    \"deref\",\n    \"element\",\n    \"exp\",\n    \"extract\",\n    \"first_value\",\n    \"floor\",\n    \"json_array\",\n    \"json_arrayagg\",\n    \"json_exists\",\n    \"json_object\",\n    \"json_objectagg\",\n    \"json_query\",\n    \"json_table\",\n    \"json_table_primitive\",\n    \"json_value\",\n    \"lag\",\n    \"last_value\",\n    \"lead\",\n    \"listagg\",\n    \"ln\",\n    \"log\",\n    \"log10\",\n    \"lower\",\n    \"max\",\n    \"min\",\n    \"mod\",\n    \"nth_value\",\n    \"ntile\",\n    \"nullif\",\n    \"percent_rank\",\n    \"percentile_cont\",\n    \"percentile_disc\",\n    \"position\",\n    \"position_regex\",\n    \"power\",\n    \"rank\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"row_number\",\n    \"sin\",\n    \"sinh\",\n    \"sqrt\",\n    \"stddev_pop\",\n    \"stddev_samp\",\n    \"substring\",\n    \"substring_regex\",\n    \"sum\",\n    \"tan\",\n    \"tanh\",\n    \"translate\",\n    \"translate_regex\",\n    \"treat\",\n    \"trim\",\n    \"trim_array\",\n    \"unnest\",\n    \"upper\",\n    \"value_of\",\n    \"var_pop\",\n    \"var_samp\",\n    \"width_bucket\",\n  ];\n\n  // these functions can\n  const POSSIBLE_WITHOUT_PARENS = [\n    \"current_catalog\",\n    \"current_date\",\n    \"current_default_transform_group\",\n    \"current_path\",\n    \"current_role\",\n    \"current_schema\",\n    \"current_transform_group_for_type\",\n    \"current_user\",\n    \"session_user\",\n    \"system_time\",\n    \"system_user\",\n    \"current_time\",\n    \"localtime\",\n    \"current_timestamp\",\n    \"localtimestamp\"\n  ];\n\n  // those exist to boost relevance making these very\n  // \"SQL like\" keyword combos worth +1 extra relevance\n  const COMBOS = [\n    \"create table\",\n    \"insert into\",\n    \"primary key\",\n    \"foreign key\",\n    \"not null\",\n    \"alter table\",\n    \"add constraint\",\n    \"grouping sets\",\n    \"on overflow\",\n    \"character set\",\n    \"respect nulls\",\n    \"ignore nulls\",\n    \"nulls first\",\n    \"nulls last\",\n    \"depth first\",\n    \"breadth first\"\n  ];\n\n  const FUNCTIONS = RESERVED_FUNCTIONS;\n\n  const KEYWORDS = [\n    ...RESERVED_WORDS,\n    ...NON_RESERVED_WORDS\n  ].filter((keyword) => {\n    return !RESERVED_FUNCTIONS.includes(keyword);\n  });\n\n  const VARIABLE = {\n    className: \"variable\",\n    begin: /@[a-z0-9][a-z0-9_]*/,\n  };\n\n  const OPERATOR = {\n    className: \"operator\",\n    begin: /[-+*/=%^~]|&&?|\\|\\|?|!=?|<(?:=>?|<|>)?|>[>=]?/,\n    relevance: 0,\n  };\n\n  const FUNCTION_CALL = {\n    begin: regex.concat(/\\b/, regex.either(...FUNCTIONS), /\\s*\\(/),\n    relevance: 0,\n    keywords: { built_in: FUNCTIONS }\n  };\n\n  // keywords with less than 3 letters are reduced in relevancy\n  function reduceRelevancy(list, {\n    exceptions, when\n  } = {}) {\n    const qualifyFn = when;\n    exceptions = exceptions || [];\n    return list.map((item) => {\n      if (item.match(/\\|\\d+$/) || exceptions.includes(item)) {\n        return item;\n      } else if (qualifyFn(item)) {\n        return `${item}|0`;\n      } else {\n        return item;\n      }\n    });\n  }\n\n  return {\n    name: 'SQL',\n    case_insensitive: true,\n    // does not include {} or HTML tags `</`\n    illegal: /[{}]|<\\//,\n    keywords: {\n      $pattern: /\\b[\\w\\.]+/,\n      keyword:\n        reduceRelevancy(KEYWORDS, { when: (x) => x.length < 3 }),\n      literal: LITERALS,\n      type: TYPES,\n      built_in: POSSIBLE_WITHOUT_PARENS\n    },\n    contains: [\n      {\n        begin: regex.either(...COMBOS),\n        relevance: 0,\n        keywords: {\n          $pattern: /[\\w\\.]+/,\n          keyword: KEYWORDS.concat(COMBOS),\n          literal: LITERALS,\n          type: TYPES\n        },\n      },\n      {\n        className: \"type\",\n        begin: regex.either(...MULTI_WORD_TYPES)\n      },\n      FUNCTION_CALL,\n      VARIABLE,\n      STRING,\n      QUOTED_IDENTIFIER,\n      hljs.C_NUMBER_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      COMMENT_MODE,\n      OPERATOR\n    ]\n  };\n}\n\nmodule.exports = sql;\n", "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * @param { Array<string | RegExp | Object> } args\n * @returns {object}\n */\nfunction stripOptionsFromArgs(args) {\n  const opts = args[args.length - 1];\n\n  if (typeof opts === 'object' && opts.constructor === Object) {\n    args.splice(args.length - 1, 1);\n    return opts;\n  } else {\n    return {};\n  }\n}\n\n/** @typedef { {capture?: boolean} } RegexEitherOptions */\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] | [...(RegExp | string)[], RegexEitherOptions]} args\n * @returns {string}\n */\nfunction either(...args) {\n  /** @type { object & {capture?: boolean} }  */\n  const opts = stripOptionsFromArgs(args);\n  const joined = '('\n    + (opts.capture ? \"\" : \"?:\")\n    + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\nconst keywordWrapper = keyword => concat(\n  /\\b/,\n  keyword,\n  /\\w$/.test(keyword) ? /\\b/ : /\\B/\n);\n\n// Keywords that require a leading dot.\nconst dotKeywords = [\n  'Protocol', // contextual\n  'Type' // contextual\n].map(keywordWrapper);\n\n// Keywords that may have a leading dot.\nconst optionalDotKeywords = [\n  'init',\n  'self'\n].map(keywordWrapper);\n\n// should register as keyword, not type\nconst keywordTypes = [\n  'Any',\n  'Self'\n];\n\n// Regular keywords and literals.\nconst keywords = [\n  // strings below will be fed into the regular `keywords` engine while regex\n  // will result in additional modes being created to scan for those keywords to\n  // avoid conflicts with other rules\n  'actor',\n  'any', // contextual\n  'associatedtype',\n  'async',\n  'await',\n  /as\\?/, // operator\n  /as!/, // operator\n  'as', // operator\n  'borrowing', // contextual\n  'break',\n  'case',\n  'catch',\n  'class',\n  'consume', // contextual\n  'consuming', // contextual\n  'continue',\n  'convenience', // contextual\n  'copy', // contextual\n  'default',\n  'defer',\n  'deinit',\n  'didSet', // contextual\n  'distributed',\n  'do',\n  'dynamic', // contextual\n  'each',\n  'else',\n  'enum',\n  'extension',\n  'fallthrough',\n  /fileprivate\\(set\\)/,\n  'fileprivate',\n  'final', // contextual\n  'for',\n  'func',\n  'get', // contextual\n  'guard',\n  'if',\n  'import',\n  'indirect', // contextual\n  'infix', // contextual\n  /init\\?/,\n  /init!/,\n  'inout',\n  /internal\\(set\\)/,\n  'internal',\n  'in',\n  'is', // operator\n  'isolated', // contextual\n  'nonisolated', // contextual\n  'lazy', // contextual\n  'let',\n  'macro',\n  'mutating', // contextual\n  'nonmutating', // contextual\n  /open\\(set\\)/, // contextual\n  'open', // contextual\n  'operator',\n  'optional', // contextual\n  'override', // contextual\n  'postfix', // contextual\n  'precedencegroup',\n  'prefix', // contextual\n  /private\\(set\\)/,\n  'private',\n  'protocol',\n  /public\\(set\\)/,\n  'public',\n  'repeat',\n  'required', // contextual\n  'rethrows',\n  'return',\n  'set', // contextual\n  'some', // contextual\n  'static',\n  'struct',\n  'subscript',\n  'super',\n  'switch',\n  'throws',\n  'throw',\n  /try\\?/, // operator\n  /try!/, // operator\n  'try', // operator\n  'typealias',\n  /unowned\\(safe\\)/, // contextual\n  /unowned\\(unsafe\\)/, // contextual\n  'unowned', // contextual\n  'var',\n  'weak', // contextual\n  'where',\n  'while',\n  'willSet' // contextual\n];\n\n// NOTE: Contextual keywords are reserved only in specific contexts.\n// Ideally, these should be matched using modes to avoid false positives.\n\n// Literals.\nconst literals = [\n  'false',\n  'nil',\n  'true'\n];\n\n// Keywords used in precedence groups.\nconst precedencegroupKeywords = [\n  'assignment',\n  'associativity',\n  'higherThan',\n  'left',\n  'lowerThan',\n  'none',\n  'right'\n];\n\n// Keywords that start with a number sign (#).\n// #(un)available is handled separately.\nconst numberSignKeywords = [\n  '#colorLiteral',\n  '#column',\n  '#dsohandle',\n  '#else',\n  '#elseif',\n  '#endif',\n  '#error',\n  '#file',\n  '#fileID',\n  '#fileLiteral',\n  '#filePath',\n  '#function',\n  '#if',\n  '#imageLiteral',\n  '#keyPath',\n  '#line',\n  '#selector',\n  '#sourceLocation',\n  '#warning'\n];\n\n// Global functions in the Standard Library.\nconst builtIns = [\n  'abs',\n  'all',\n  'any',\n  'assert',\n  'assertionFailure',\n  'debugPrint',\n  'dump',\n  'fatalError',\n  'getVaList',\n  'isKnownUniquelyReferenced',\n  'max',\n  'min',\n  'numericCast',\n  'pointwiseMax',\n  'pointwiseMin',\n  'precondition',\n  'preconditionFailure',\n  'print',\n  'readLine',\n  'repeatElement',\n  'sequence',\n  'stride',\n  'swap',\n  'swift_unboxFromSwiftValueWithType',\n  'transcode',\n  'type',\n  'unsafeBitCast',\n  'unsafeDowncast',\n  'withExtendedLifetime',\n  'withUnsafeMutablePointer',\n  'withUnsafePointer',\n  'withVaList',\n  'withoutActuallyEscaping',\n  'zip'\n];\n\n// Valid first characters for operators.\nconst operatorHead = either(\n  /[/=\\-+!*%<>&|^~?]/,\n  /[\\u00A1-\\u00A7]/,\n  /[\\u00A9\\u00AB]/,\n  /[\\u00AC\\u00AE]/,\n  /[\\u00B0\\u00B1]/,\n  /[\\u00B6\\u00BB\\u00BF\\u00D7\\u00F7]/,\n  /[\\u2016-\\u2017]/,\n  /[\\u2020-\\u2027]/,\n  /[\\u2030-\\u203E]/,\n  /[\\u2041-\\u2053]/,\n  /[\\u2055-\\u205E]/,\n  /[\\u2190-\\u23FF]/,\n  /[\\u2500-\\u2775]/,\n  /[\\u2794-\\u2BFF]/,\n  /[\\u2E00-\\u2E7F]/,\n  /[\\u3001-\\u3003]/,\n  /[\\u3008-\\u3020]/,\n  /[\\u3030]/\n);\n\n// Valid characters for operators.\nconst operatorCharacter = either(\n  operatorHead,\n  /[\\u0300-\\u036F]/,\n  /[\\u1DC0-\\u1DFF]/,\n  /[\\u20D0-\\u20FF]/,\n  /[\\uFE00-\\uFE0F]/,\n  /[\\uFE20-\\uFE2F]/\n  // TODO: The following characters are also allowed, but the regex isn't supported yet.\n  // /[\\u{E0100}-\\u{E01EF}]/u\n);\n\n// Valid operator.\nconst operator = concat(operatorHead, operatorCharacter, '*');\n\n// Valid first characters for identifiers.\nconst identifierHead = either(\n  /[a-zA-Z_]/,\n  /[\\u00A8\\u00AA\\u00AD\\u00AF\\u00B2-\\u00B5\\u00B7-\\u00BA]/,\n  /[\\u00BC-\\u00BE\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u00FF]/,\n  /[\\u0100-\\u02FF\\u0370-\\u167F\\u1681-\\u180D\\u180F-\\u1DBF]/,\n  /[\\u1E00-\\u1FFF]/,\n  /[\\u200B-\\u200D\\u202A-\\u202E\\u203F-\\u2040\\u2054\\u2060-\\u206F]/,\n  /[\\u2070-\\u20CF\\u2100-\\u218F\\u2460-\\u24FF\\u2776-\\u2793]/,\n  /[\\u2C00-\\u2DFF\\u2E80-\\u2FFF]/,\n  /[\\u3004-\\u3007\\u3021-\\u302F\\u3031-\\u303F\\u3040-\\uD7FF]/,\n  /[\\uF900-\\uFD3D\\uFD40-\\uFDCF\\uFDF0-\\uFE1F\\uFE30-\\uFE44]/,\n  /[\\uFE47-\\uFEFE\\uFF00-\\uFFFD]/ // Should be /[\\uFE47-\\uFFFD]/, but we have to exclude FEFF.\n  // The following characters are also allowed, but the regexes aren't supported yet.\n  // /[\\u{10000}-\\u{1FFFD}\\u{20000-\\u{2FFFD}\\u{30000}-\\u{3FFFD}\\u{40000}-\\u{4FFFD}]/u,\n  // /[\\u{50000}-\\u{5FFFD}\\u{60000-\\u{6FFFD}\\u{70000}-\\u{7FFFD}\\u{80000}-\\u{8FFFD}]/u,\n  // /[\\u{90000}-\\u{9FFFD}\\u{A0000-\\u{AFFFD}\\u{B0000}-\\u{BFFFD}\\u{C0000}-\\u{CFFFD}]/u,\n  // /[\\u{D0000}-\\u{DFFFD}\\u{E0000-\\u{EFFFD}]/u\n);\n\n// Valid characters for identifiers.\nconst identifierCharacter = either(\n  identifierHead,\n  /\\d/,\n  /[\\u0300-\\u036F\\u1DC0-\\u1DFF\\u20D0-\\u20FF\\uFE20-\\uFE2F]/\n);\n\n// Valid identifier.\nconst identifier = concat(identifierHead, identifierCharacter, '*');\n\n// Valid type identifier.\nconst typeIdentifier = concat(/[A-Z]/, identifierCharacter, '*');\n\n// Built-in attributes, which are highlighted as keywords.\n// @available is handled separately.\n// https://docs.swift.org/swift-book/documentation/the-swift-programming-language/attributes\nconst keywordAttributes = [\n  'attached',\n  'autoclosure',\n  concat(/convention\\(/, either('swift', 'block', 'c'), /\\)/),\n  'discardableResult',\n  'dynamicCallable',\n  'dynamicMemberLookup',\n  'escaping',\n  'freestanding',\n  'frozen',\n  'GKInspectable',\n  'IBAction',\n  'IBDesignable',\n  'IBInspectable',\n  'IBOutlet',\n  'IBSegueAction',\n  'inlinable',\n  'main',\n  'nonobjc',\n  'NSApplicationMain',\n  'NSCopying',\n  'NSManaged',\n  concat(/objc\\(/, identifier, /\\)/),\n  'objc',\n  'objcMembers',\n  'propertyWrapper',\n  'requires_stored_property_inits',\n  'resultBuilder',\n  'Sendable',\n  'testable',\n  'UIApplicationMain',\n  'unchecked',\n  'unknown',\n  'usableFromInline',\n  'warn_unqualified_access'\n];\n\n// Contextual keywords used in @available and #(un)available.\nconst availabilityKeywords = [\n  'iOS',\n  'iOSApplicationExtension',\n  'macOS',\n  'macOSApplicationExtension',\n  'macCatalyst',\n  'macCatalystApplicationExtension',\n  'watchOS',\n  'watchOSApplicationExtension',\n  'tvOS',\n  'tvOSApplicationExtension',\n  'swift'\n];\n\n/*\nLanguage: Swift\nDescription: Swift is a general-purpose programming language built using a modern approach to safety, performance, and software design patterns.\nAuthor: Steven Van Impe <<EMAIL>>\nContributors: Chris Eidhof <<EMAIL>>, Nate Cook <<EMAIL>>, Alexander Lichter <<EMAIL>>, Richard Gibson <gibson042@github>\nWebsite: https://swift.org\nCategory: common, system\n*/\n\n\n/** @type LanguageFn */\nfunction swift(hljs) {\n  const WHITESPACE = {\n    match: /\\s+/,\n    relevance: 0\n  };\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID411\n  const BLOCK_COMMENT = hljs.COMMENT(\n    '/\\\\*',\n    '\\\\*/',\n    { contains: [ 'self' ] }\n  );\n  const COMMENTS = [\n    hljs.C_LINE_COMMENT_MODE,\n    BLOCK_COMMENT\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID413\n  // https://docs.swift.org/swift-book/ReferenceManual/zzSummaryOfTheGrammar.html\n  const DOT_KEYWORD = {\n    match: [\n      /\\./,\n      either(...dotKeywords, ...optionalDotKeywords)\n    ],\n    className: { 2: \"keyword\" }\n  };\n  const KEYWORD_GUARD = {\n    // Consume .keyword to prevent highlighting properties and methods as keywords.\n    match: concat(/\\./, either(...keywords)),\n    relevance: 0\n  };\n  const PLAIN_KEYWORDS = keywords\n    .filter(kw => typeof kw === 'string')\n    .concat([ \"_|0\" ]); // seems common, so 0 relevance\n  const REGEX_KEYWORDS = keywords\n    .filter(kw => typeof kw !== 'string') // find regex\n    .concat(keywordTypes)\n    .map(keywordWrapper);\n  const KEYWORD = { variants: [\n    {\n      className: 'keyword',\n      match: either(...REGEX_KEYWORDS, ...optionalDotKeywords)\n    }\n  ] };\n  // find all the regular keywords\n  const KEYWORDS = {\n    $pattern: either(\n      /\\b\\w+/, // regular keywords\n      /#\\w+/ // number keywords\n    ),\n    keyword: PLAIN_KEYWORDS\n      .concat(numberSignKeywords),\n    literal: literals\n  };\n  const KEYWORD_MODES = [\n    DOT_KEYWORD,\n    KEYWORD_GUARD,\n    KEYWORD\n  ];\n\n  // https://github.com/apple/swift/tree/main/stdlib/public/core\n  const BUILT_IN_GUARD = {\n    // Consume .built_in to prevent highlighting properties and methods.\n    match: concat(/\\./, either(...builtIns)),\n    relevance: 0\n  };\n  const BUILT_IN = {\n    className: 'built_in',\n    match: concat(/\\b/, either(...builtIns), /(?=\\()/)\n  };\n  const BUILT_INS = [\n    BUILT_IN_GUARD,\n    BUILT_IN\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID418\n  const OPERATOR_GUARD = {\n    // Prevent -> from being highlighting as an operator.\n    match: /->/,\n    relevance: 0\n  };\n  const OPERATOR = {\n    className: 'operator',\n    relevance: 0,\n    variants: [\n      { match: operator },\n      {\n        // dot-operator: only operators that start with a dot are allowed to use dots as\n        // characters (..., ...<, .*, etc). So there rule here is: a dot followed by one or more\n        // characters that may also include dots.\n        match: `\\\\.(\\\\.|${operatorCharacter})+` }\n    ]\n  };\n  const OPERATORS = [\n    OPERATOR_GUARD,\n    OPERATOR\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#grammar_numeric-literal\n  // TODO: Update for leading `-` after lookbehind is supported everywhere\n  const decimalDigits = '([0-9]_*)+';\n  const hexDigits = '([0-9a-fA-F]_*)+';\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      // decimal floating-point-literal (subsumes decimal-literal)\n      { match: `\\\\b(${decimalDigits})(\\\\.(${decimalDigits}))?` + `([eE][+-]?(${decimalDigits}))?\\\\b` },\n      // hexadecimal floating-point-literal (subsumes hexadecimal-literal)\n      { match: `\\\\b0x(${hexDigits})(\\\\.(${hexDigits}))?` + `([pP][+-]?(${decimalDigits}))?\\\\b` },\n      // octal-literal\n      { match: /\\b0o([0-7]_*)+\\b/ },\n      // binary-literal\n      { match: /\\b0b([01]_*)+\\b/ }\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#grammar_string-literal\n  const ESCAPED_CHARACTER = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    variants: [\n      { match: concat(/\\\\/, rawDelimiter, /[0\\\\tnr\"']/) },\n      { match: concat(/\\\\/, rawDelimiter, /u\\{[0-9a-fA-F]{1,8}\\}/) }\n    ]\n  });\n  const ESCAPED_NEWLINE = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    match: concat(/\\\\/, rawDelimiter, /[\\t ]*(?:[\\r\\n]|\\r\\n)/)\n  });\n  const INTERPOLATION = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    label: \"interpol\",\n    begin: concat(/\\\\/, rawDelimiter, /\\(/),\n    end: /\\)/\n  });\n  const MULTILINE_STRING = (rawDelimiter = \"\") => ({\n    begin: concat(rawDelimiter, /\"\"\"/),\n    end: concat(/\"\"\"/, rawDelimiter),\n    contains: [\n      ESCAPED_CHARACTER(rawDelimiter),\n      ESCAPED_NEWLINE(rawDelimiter),\n      INTERPOLATION(rawDelimiter)\n    ]\n  });\n  const SINGLE_LINE_STRING = (rawDelimiter = \"\") => ({\n    begin: concat(rawDelimiter, /\"/),\n    end: concat(/\"/, rawDelimiter),\n    contains: [\n      ESCAPED_CHARACTER(rawDelimiter),\n      INTERPOLATION(rawDelimiter)\n    ]\n  });\n  const STRING = {\n    className: 'string',\n    variants: [\n      MULTILINE_STRING(),\n      MULTILINE_STRING(\"#\"),\n      MULTILINE_STRING(\"##\"),\n      MULTILINE_STRING(\"###\"),\n      SINGLE_LINE_STRING(),\n      SINGLE_LINE_STRING(\"#\"),\n      SINGLE_LINE_STRING(\"##\"),\n      SINGLE_LINE_STRING(\"###\")\n    ]\n  };\n\n  const REGEXP_CONTENTS = [\n    hljs.BACKSLASH_ESCAPE,\n    {\n      begin: /\\[/,\n      end: /\\]/,\n      relevance: 0,\n      contains: [ hljs.BACKSLASH_ESCAPE ]\n    }\n  ];\n\n  const BARE_REGEXP_LITERAL = {\n    begin: /\\/[^\\s](?=[^/\\n]*\\/)/,\n    end: /\\//,\n    contains: REGEXP_CONTENTS\n  };\n\n  const EXTENDED_REGEXP_LITERAL = (rawDelimiter) => {\n    const begin = concat(rawDelimiter, /\\//);\n    const end = concat(/\\//, rawDelimiter);\n    return {\n      begin,\n      end,\n      contains: [\n        ...REGEXP_CONTENTS,\n        {\n          scope: \"comment\",\n          begin: `#(?!.*${end})`,\n          end: /$/,\n        },\n      ],\n    };\n  };\n\n  // https://docs.swift.org/swift-book/documentation/the-swift-programming-language/lexicalstructure/#Regular-Expression-Literals\n  const REGEXP = {\n    scope: \"regexp\",\n    variants: [\n      EXTENDED_REGEXP_LITERAL('###'),\n      EXTENDED_REGEXP_LITERAL('##'),\n      EXTENDED_REGEXP_LITERAL('#'),\n      BARE_REGEXP_LITERAL\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID412\n  const QUOTED_IDENTIFIER = { match: concat(/`/, identifier, /`/) };\n  const IMPLICIT_PARAMETER = {\n    className: 'variable',\n    match: /\\$\\d+/\n  };\n  const PROPERTY_WRAPPER_PROJECTION = {\n    className: 'variable',\n    match: `\\\\$${identifierCharacter}+`\n  };\n  const IDENTIFIERS = [\n    QUOTED_IDENTIFIER,\n    IMPLICIT_PARAMETER,\n    PROPERTY_WRAPPER_PROJECTION\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Attributes.html\n  const AVAILABLE_ATTRIBUTE = {\n    match: /(@|#(un)?)available/,\n    scope: 'keyword',\n    starts: { contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: availabilityKeywords,\n        contains: [\n          ...OPERATORS,\n          NUMBER,\n          STRING\n        ]\n      }\n    ] }\n  };\n  const KEYWORD_ATTRIBUTE = {\n    scope: 'keyword',\n    match: concat(/@/, either(...keywordAttributes))\n  };\n  const USER_DEFINED_ATTRIBUTE = {\n    scope: 'meta',\n    match: concat(/@/, identifier)\n  };\n  const ATTRIBUTES = [\n    AVAILABLE_ATTRIBUTE,\n    KEYWORD_ATTRIBUTE,\n    USER_DEFINED_ATTRIBUTE\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Types.html\n  const TYPE = {\n    match: lookahead(/\\b[A-Z]/),\n    relevance: 0,\n    contains: [\n      { // Common Apple frameworks, for relevance boost\n        className: 'type',\n        match: concat(/(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)/, identifierCharacter, '+')\n      },\n      { // Type identifier\n        className: 'type',\n        match: typeIdentifier,\n        relevance: 0\n      },\n      { // Optional type\n        match: /[?!]+/,\n        relevance: 0\n      },\n      { // Variadic parameter\n        match: /\\.\\.\\./,\n        relevance: 0\n      },\n      { // Protocol composition\n        match: concat(/\\s+&\\s+/, lookahead(typeIdentifier)),\n        relevance: 0\n      }\n    ]\n  };\n  const GENERIC_ARGUMENTS = {\n    begin: /</,\n    end: />/,\n    keywords: KEYWORDS,\n    contains: [\n      ...COMMENTS,\n      ...KEYWORD_MODES,\n      ...ATTRIBUTES,\n      OPERATOR_GUARD,\n      TYPE\n    ]\n  };\n  TYPE.contains.push(GENERIC_ARGUMENTS);\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Expressions.html#ID552\n  // Prevents element names from being highlighted as keywords.\n  const TUPLE_ELEMENT_NAME = {\n    match: concat(identifier, /\\s*:/),\n    keywords: \"_|0\",\n    relevance: 0\n  };\n  // Matches tuples as well as the parameter list of a function type.\n  const TUPLE = {\n    begin: /\\(/,\n    end: /\\)/,\n    relevance: 0,\n    keywords: KEYWORDS,\n    contains: [\n      'self',\n      TUPLE_ELEMENT_NAME,\n      ...COMMENTS,\n      REGEXP,\n      ...KEYWORD_MODES,\n      ...BUILT_INS,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...IDENTIFIERS,\n      ...ATTRIBUTES,\n      TYPE\n    ]\n  };\n\n  const GENERIC_PARAMETERS = {\n    begin: /</,\n    end: />/,\n    keywords: 'repeat each',\n    contains: [\n      ...COMMENTS,\n      TYPE\n    ]\n  };\n  const FUNCTION_PARAMETER_NAME = {\n    begin: either(\n      lookahead(concat(identifier, /\\s*:/)),\n      lookahead(concat(identifier, /\\s+/, identifier, /\\s*:/))\n    ),\n    end: /:/,\n    relevance: 0,\n    contains: [\n      {\n        className: 'keyword',\n        match: /\\b_\\b/\n      },\n      {\n        className: 'params',\n        match: identifier\n      }\n    ]\n  };\n  const FUNCTION_PARAMETERS = {\n    begin: /\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS,\n    contains: [\n      FUNCTION_PARAMETER_NAME,\n      ...COMMENTS,\n      ...KEYWORD_MODES,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...ATTRIBUTES,\n      TYPE,\n      TUPLE\n    ],\n    endsParent: true,\n    illegal: /[\"']/\n  };\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID362\n  // https://docs.swift.org/swift-book/documentation/the-swift-programming-language/declarations/#Macro-Declaration\n  const FUNCTION_OR_MACRO = {\n    match: [\n      /(func|macro)/,\n      /\\s+/,\n      either(QUOTED_IDENTIFIER.match, identifier, operator)\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      GENERIC_PARAMETERS,\n      FUNCTION_PARAMETERS,\n      WHITESPACE\n    ],\n    illegal: [\n      /\\[/,\n      /%/\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID375\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID379\n  const INIT_SUBSCRIPT = {\n    match: [\n      /\\b(?:subscript|init[?!]?)/,\n      /\\s*(?=[<(])/,\n    ],\n    className: { 1: \"keyword\" },\n    contains: [\n      GENERIC_PARAMETERS,\n      FUNCTION_PARAMETERS,\n      WHITESPACE\n    ],\n    illegal: /\\[|%/\n  };\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID380\n  const OPERATOR_DECLARATION = {\n    match: [\n      /operator/,\n      /\\s+/,\n      operator\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title\"\n    }\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID550\n  const PRECEDENCEGROUP = {\n    begin: [\n      /precedencegroup/,\n      /\\s+/,\n      typeIdentifier\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title\"\n    },\n    contains: [ TYPE ],\n    keywords: [\n      ...precedencegroupKeywords,\n      ...literals\n    ],\n    end: /}/\n  };\n\n  // Add supported submodes to string interpolation.\n  for (const variant of STRING.variants) {\n    const interpolation = variant.contains.find(mode => mode.label === \"interpol\");\n    // TODO: Interpolation can contain any expression, so there's room for improvement here.\n    interpolation.keywords = KEYWORDS;\n    const submodes = [\n      ...KEYWORD_MODES,\n      ...BUILT_INS,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...IDENTIFIERS\n    ];\n    interpolation.contains = [\n      ...submodes,\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [\n          'self',\n          ...submodes\n        ]\n      }\n    ];\n  }\n\n  return {\n    name: 'Swift',\n    keywords: KEYWORDS,\n    contains: [\n      ...COMMENTS,\n      FUNCTION_OR_MACRO,\n      INIT_SUBSCRIPT,\n      {\n        beginKeywords: 'struct protocol class extension enum actor',\n        end: '\\\\{',\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            className: \"title.class\",\n            begin: /[A-Za-z$_][\\u00C0-\\u02B80-9A-Za-z$_]*/\n          }),\n          ...KEYWORD_MODES\n        ]\n      },\n      OPERATOR_DECLARATION,\n      PRECEDENCEGROUP,\n      {\n        beginKeywords: 'import',\n        end: /$/,\n        contains: [ ...COMMENTS ],\n        relevance: 0\n      },\n      REGEXP,\n      ...KEYWORD_MODES,\n      ...BUILT_INS,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...IDENTIFIERS,\n      ...ATTRIBUTES,\n      TYPE,\n      TUPLE\n    ]\n  };\n}\n\nmodule.exports = swift;\n", "/*\nLanguage: YAML\nDescription: Yet Another Markdown Language\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nRequires: ruby.js\nWebsite: https://yaml.org\nCategory: common, config\n*/\nfunction yaml(hljs) {\n  const LITERALS = 'true false yes no null';\n\n  // YAML spec allows non-reserved URI characters in tags.\n  const URI_CHARACTERS = '[\\\\w#;/?:@&=+$,.~*\\'()[\\\\]]+';\n\n  // Define keys as starting with a word character\n  // ...containing word chars, spaces, colons, forward-slashes, hyphens and periods\n  // ...and ending with a colon followed immediately by a space, tab or newline.\n  // The YAML spec allows for much more than this, but this covers most use-cases.\n  const KEY = {\n    className: 'attr',\n    variants: [\n      { begin: '\\\\w[\\\\w :\\\\/.-]*:(?=[ \\t]|$)' },\n      { // double quoted keys\n        begin: '\"\\\\w[\\\\w :\\\\/.-]*\":(?=[ \\t]|$)' },\n      { // single quoted keys\n        begin: '\\'\\\\w[\\\\w :\\\\/.-]*\\':(?=[ \\t]|$)' }\n    ]\n  };\n\n  const TEMPLATE_VARIABLES = {\n    className: 'template-variable',\n    variants: [\n      { // jinja templates Ansible\n        begin: /\\{\\{/,\n        end: /\\}\\}/\n      },\n      { // Ruby i18n\n        begin: /%\\{/,\n        end: /\\}/\n      }\n    ]\n  };\n  const STRING = {\n    className: 'string',\n    relevance: 0,\n    variants: [\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\"/,\n        end: /\"/\n      },\n      { begin: /\\S+/ }\n    ],\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      TEMPLATE_VARIABLES\n    ]\n  };\n\n  // Strings inside of value containers (objects) can't contain braces,\n  // brackets, or commas\n  const CONTAINER_STRING = hljs.inherit(STRING, { variants: [\n    {\n      begin: /'/,\n      end: /'/\n    },\n    {\n      begin: /\"/,\n      end: /\"/\n    },\n    { begin: /[^\\s,{}[\\]]+/ }\n  ] });\n\n  const DATE_RE = '[0-9]{4}(-[0-9][0-9]){0,2}';\n  const TIME_RE = '([Tt \\\\t][0-9][0-9]?(:[0-9][0-9]){2})?';\n  const FRACTION_RE = '(\\\\.[0-9]*)?';\n  const ZONE_RE = '([ \\\\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?';\n  const TIMESTAMP = {\n    className: 'number',\n    begin: '\\\\b' + DATE_RE + TIME_RE + FRACTION_RE + ZONE_RE + '\\\\b'\n  };\n\n  const VALUE_CONTAINER = {\n    end: ',',\n    endsWithParent: true,\n    excludeEnd: true,\n    keywords: LITERALS,\n    relevance: 0\n  };\n  const OBJECT = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: [ VALUE_CONTAINER ],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n  const ARRAY = {\n    begin: '\\\\[',\n    end: '\\\\]',\n    contains: [ VALUE_CONTAINER ],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n\n  const MODES = [\n    KEY,\n    {\n      className: 'meta',\n      begin: '^---\\\\s*$',\n      relevance: 10\n    },\n    { // multi line string\n      // Blocks start with a | or > followed by a newline\n      //\n      // Indentation of subsequent lines must be the same to\n      // be considered part of the block\n      className: 'string',\n      begin: '[\\\\|>]([1-9]?[+-])?[ ]*\\\\n( +)[^ ][^\\\\n]*\\\\n(\\\\2[^\\\\n]+\\\\n?)*'\n    },\n    { // Ruby/Rails erb\n      begin: '<%[%=-]?',\n      end: '[%-]?%>',\n      subLanguage: 'ruby',\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0\n    },\n    { // named tags\n      className: 'type',\n      begin: '!\\\\w+!' + URI_CHARACTERS\n    },\n    // https://yaml.org/spec/1.2/spec.html#id2784064\n    { // verbatim tags\n      className: 'type',\n      begin: '!<' + URI_CHARACTERS + \">\"\n    },\n    { // primary tags\n      className: 'type',\n      begin: '!' + URI_CHARACTERS\n    },\n    { // secondary tags\n      className: 'type',\n      begin: '!!' + URI_CHARACTERS\n    },\n    { // fragment id &ref\n      className: 'meta',\n      begin: '&' + hljs.UNDERSCORE_IDENT_RE + '$'\n    },\n    { // fragment reference *ref\n      className: 'meta',\n      begin: '\\\\*' + hljs.UNDERSCORE_IDENT_RE + '$'\n    },\n    { // array listing\n      className: 'bullet',\n      // TODO: remove |$ hack when we have proper look-ahead support\n      begin: '-(?=[ ]|$)',\n      relevance: 0\n    },\n    hljs.HASH_COMMENT_MODE,\n    {\n      beginKeywords: LITERALS,\n      keywords: { literal: LITERALS }\n    },\n    TIMESTAMP,\n    // numbers are any valid C-style number that\n    // sit isolated from other words\n    {\n      className: 'number',\n      begin: hljs.C_NUMBER_RE + '\\\\b',\n      relevance: 0\n    },\n    OBJECT,\n    ARRAY,\n    STRING\n  ];\n\n  const VALUE_MODES = [ ...MODES ];\n  VALUE_MODES.pop();\n  VALUE_MODES.push(CONTAINER_STRING);\n  VALUE_CONTAINER.contains = VALUE_MODES;\n\n  return {\n    name: 'YAML',\n    case_insensitive: true,\n    aliases: [ 'yml' ],\n    contains: MODES\n  };\n}\n\nmodule.exports = yaml;\n", "const IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\nconst KEYWORDS = [\n  \"as\", // for exports\n  \"in\",\n  \"of\",\n  \"if\",\n  \"for\",\n  \"while\",\n  \"finally\",\n  \"var\",\n  \"new\",\n  \"function\",\n  \"do\",\n  \"return\",\n  \"void\",\n  \"else\",\n  \"break\",\n  \"catch\",\n  \"instanceof\",\n  \"with\",\n  \"throw\",\n  \"case\",\n  \"default\",\n  \"try\",\n  \"switch\",\n  \"continue\",\n  \"typeof\",\n  \"delete\",\n  \"let\",\n  \"yield\",\n  \"const\",\n  \"class\",\n  // J<PERSON> handles these with a special rule\n  // \"get\",\n  // \"set\",\n  \"debugger\",\n  \"async\",\n  \"await\",\n  \"static\",\n  \"import\",\n  \"from\",\n  \"export\",\n  \"extends\"\n];\nconst LITERALS = [\n  \"true\",\n  \"false\",\n  \"null\",\n  \"undefined\",\n  \"NaN\",\n  \"Infinity\"\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects\nconst TYPES = [\n  // Fundamental objects\n  \"Object\",\n  \"Function\",\n  \"Boolean\",\n  \"Symbol\",\n  // numbers and dates\n  \"Math\",\n  \"Date\",\n  \"Number\",\n  \"BigInt\",\n  // text\n  \"String\",\n  \"RegExp\",\n  // Indexed collections\n  \"Array\",\n  \"Float32Array\",\n  \"Float64Array\",\n  \"Int8Array\",\n  \"Uint8Array\",\n  \"Uint8ClampedArray\",\n  \"Int16Array\",\n  \"Int32Array\",\n  \"Uint16Array\",\n  \"Uint32Array\",\n  \"BigInt64Array\",\n  \"BigUint64Array\",\n  // Keyed collections\n  \"Set\",\n  \"Map\",\n  \"WeakSet\",\n  \"WeakMap\",\n  // Structured data\n  \"ArrayBuffer\",\n  \"SharedArrayBuffer\",\n  \"Atomics\",\n  \"DataView\",\n  \"JSON\",\n  // Control abstraction objects\n  \"Promise\",\n  \"Generator\",\n  \"GeneratorFunction\",\n  \"AsyncFunction\",\n  // Reflection\n  \"Reflect\",\n  \"Proxy\",\n  // Internationalization\n  \"Intl\",\n  // WebAssembly\n  \"WebAssembly\"\n];\n\nconst ERROR_TYPES = [\n  \"Error\",\n  \"EvalError\",\n  \"InternalError\",\n  \"RangeError\",\n  \"ReferenceError\",\n  \"SyntaxError\",\n  \"TypeError\",\n  \"URIError\"\n];\n\nconst BUILT_IN_GLOBALS = [\n  \"setInterval\",\n  \"setTimeout\",\n  \"clearInterval\",\n  \"clearTimeout\",\n\n  \"require\",\n  \"exports\",\n\n  \"eval\",\n  \"isFinite\",\n  \"isNaN\",\n  \"parseFloat\",\n  \"parseInt\",\n  \"decodeURI\",\n  \"decodeURIComponent\",\n  \"encodeURI\",\n  \"encodeURIComponent\",\n  \"escape\",\n  \"unescape\"\n];\n\nconst BUILT_IN_VARIABLES = [\n  \"arguments\",\n  \"this\",\n  \"super\",\n  \"console\",\n  \"window\",\n  \"document\",\n  \"localStorage\",\n  \"sessionStorage\",\n  \"module\",\n  \"global\" // Node.js\n];\n\nconst BUILT_INS = [].concat(\n  BUILT_IN_GLOBALS,\n  TYPES,\n  ERROR_TYPES\n);\n\n/*\nLanguage: JavaScript\nDescription: JavaScript (JS) is a lightweight, interpreted, or just-in-time compiled programming language with first-class functions.\nCategory: common, scripting, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/JavaScript\n*/\n\n\n/** @type LanguageFn */\nfunction javascript(hljs) {\n  const regex = hljs.regex;\n  /**\n   * Takes a string like \"<Booger\" and checks to see\n   * if we can find a matching \"</Booger\" later in the\n   * content.\n   * @param {RegExpMatchArray} match\n   * @param {{after:number}} param1\n   */\n  const hasClosingTag = (match, { after }) => {\n    const tag = \"</\" + match[0].slice(1);\n    const pos = match.input.indexOf(tag, after);\n    return pos !== -1;\n  };\n\n  const IDENT_RE$1 = IDENT_RE;\n  const FRAGMENT = {\n    begin: '<>',\n    end: '</>'\n  };\n  // to avoid some special cases inside isTrulyOpeningTag\n  const XML_SELF_CLOSING = /<[A-Za-z0-9\\\\._:-]+\\s*\\/>/;\n  const XML_TAG = {\n    begin: /<[A-Za-z0-9\\\\._:-]+/,\n    end: /\\/[A-Za-z0-9\\\\._:-]+>|\\/>/,\n    /**\n     * @param {RegExpMatchArray} match\n     * @param {CallbackResponse} response\n     */\n    isTrulyOpeningTag: (match, response) => {\n      const afterMatchIndex = match[0].length + match.index;\n      const nextChar = match.input[afterMatchIndex];\n      if (\n        // HTML should not include another raw `<` inside a tag\n        // nested type?\n        // `<Array<Array<number>>`, etc.\n        nextChar === \"<\" ||\n        // the , gives away that this is not HTML\n        // `<T, A extends keyof T, V>`\n        nextChar === \",\"\n        ) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<something>`\n      // Quite possibly a tag, lets look for a matching closing tag...\n      if (nextChar === \">\") {\n        // if we cannot find a matching closing tag, then we\n        // will ignore it\n        if (!hasClosingTag(match, { after: afterMatchIndex })) {\n          response.ignoreMatch();\n        }\n      }\n\n      // `<blah />` (self-closing)\n      // handled by simpleSelfClosing rule\n\n      let m;\n      const afterMatch = match.input.substring(afterMatchIndex);\n\n      // some more template typing stuff\n      //  <T = any>(key?: string) => Modify<\n      if ((m = afterMatch.match(/^\\s*=/))) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<From extends string>`\n      // technically this could be HTML, but it smells like a type\n      // NOTE: This is ugh, but added specifically for https://github.com/highlightjs/highlight.js/issues/3276\n      if ((m = afterMatch.match(/^\\s+extends\\s+/))) {\n        if (m.index === 0) {\n          response.ignoreMatch();\n          // eslint-disable-next-line no-useless-return\n          return;\n        }\n      }\n    }\n  };\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_INS,\n    \"variable.language\": BUILT_IN_VARIABLES\n  };\n\n  // https://tc39.es/ecma262/#sec-literals-numeric-literals\n  const decimalDigits = '[0-9](_?[0-9])*';\n  const frac = `\\\\.(${decimalDigits})`;\n  // DecimalIntegerLiteral, including Annex B NonOctalDecimalIntegerLiteral\n  // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n  const decimalInteger = `0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*`;\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      // DecimalLiteral\n      { begin: `(\\\\b(${decimalInteger})((${frac})|\\\\.)?|(${frac}))` +\n        `[eE][+-]?(${decimalDigits})\\\\b` },\n      { begin: `\\\\b(${decimalInteger})\\\\b((${frac})\\\\b|\\\\.)?|(${frac})\\\\b` },\n\n      // DecimalBigIntegerLiteral\n      { begin: `\\\\b(0|[1-9](_?[0-9])*)n\\\\b` },\n\n      // NonDecimalIntegerLiteral\n      { begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\\\b\" },\n      { begin: \"\\\\b0[bB][0-1](_?[0-1])*n?\\\\b\" },\n      { begin: \"\\\\b0[oO][0-7](_?[0-7])*n?\\\\b\" },\n\n      // LegacyOctalIntegerLiteral (does not include underscore separators)\n      // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n      { begin: \"\\\\b0[0-7]+n?\\\\b\" },\n    ],\n    relevance: 0\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS$1,\n    contains: [] // defined later\n  };\n  const HTML_TEMPLATE = {\n    begin: 'html`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'xml'\n    }\n  };\n  const CSS_TEMPLATE = {\n    begin: 'css`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'css'\n    }\n  };\n  const GRAPHQL_TEMPLATE = {\n    begin: 'gql`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'graphql'\n    }\n  };\n  const TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  const JSDOC_COMMENT = hljs.COMMENT(\n    /\\/\\*\\*(?!\\/)/,\n    '\\\\*/',\n    {\n      relevance: 0,\n      contains: [\n        {\n          begin: '(?=@[A-Za-z]+)',\n          relevance: 0,\n          contains: [\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            },\n            {\n              className: 'type',\n              begin: '\\\\{',\n              end: '\\\\}',\n              excludeEnd: true,\n              excludeBegin: true,\n              relevance: 0\n            },\n            {\n              className: 'variable',\n              begin: IDENT_RE$1 + '(?=\\\\s*(-)|$)',\n              endsParent: true,\n              relevance: 0\n            },\n            // eat spaces (not newlines) so we can find\n            // types or variables\n            {\n              begin: /(?=[^\\n])\\s/,\n              relevance: 0\n            }\n          ]\n        }\n      ]\n    }\n  );\n  const COMMENT = {\n    className: \"comment\",\n    variants: [\n      JSDOC_COMMENT,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_LINE_COMMENT_MODE\n    ]\n  };\n  const SUBST_INTERNALS = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    HTML_TEMPLATE,\n    CSS_TEMPLATE,\n    GRAPHQL_TEMPLATE,\n    TEMPLATE_STRING,\n    // Skip numbers when they are part of a variable name\n    { match: /\\$\\d+/ },\n    NUMBER,\n    // This is intentional:\n    // See https://github.com/highlightjs/highlight.js/issues/3288\n    // hljs.REGEXP_MODE\n  ];\n  SUBST.contains = SUBST_INTERNALS\n    .concat({\n      // we need to pair up {} inside our subst to prevent\n      // it from ending too early by matching another }\n      begin: /\\{/,\n      end: /\\}/,\n      keywords: KEYWORDS$1,\n      contains: [\n        \"self\"\n      ].concat(SUBST_INTERNALS)\n    });\n  const SUBST_AND_COMMENTS = [].concat(COMMENT, SUBST.contains);\n  const PARAMS_CONTAINS = SUBST_AND_COMMENTS.concat([\n    // eat recursive parens in sub expressions\n    {\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS$1,\n      contains: [\"self\"].concat(SUBST_AND_COMMENTS)\n    }\n  ]);\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true,\n    keywords: KEYWORDS$1,\n    contains: PARAMS_CONTAINS\n  };\n\n  // ES6 classes\n  const CLASS_OR_EXTENDS = {\n    variants: [\n      // class Car extends vehicle\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1,\n          /\\s+/,\n          /extends/,\n          /\\s+/,\n          regex.concat(IDENT_RE$1, \"(\", regex.concat(/\\./, IDENT_RE$1), \")*\")\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\",\n          5: \"keyword\",\n          7: \"title.class.inherited\"\n        }\n      },\n      // class Car\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n\n    ]\n  };\n\n  const CLASS_REFERENCE = {\n    relevance: 0,\n    match:\n    regex.either(\n      // Hard coded exceptions\n      /\\bJSON/,\n      // Float32Array, OutT\n      /\\b[A-Z][a-z]+([A-Z][a-z]*|\\d)*/,\n      // CSSFactory, CSSFactoryT\n      /\\b[A-Z]{2,}([A-Z][a-z]+|\\d)+([A-Z][a-z]*)*/,\n      // FPs, FPsT\n      /\\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\\d)*([A-Z][a-z]*)*/,\n      // P\n      // single letters are not highlighted\n      // BLAH\n      // this will be flagged as a UPPER_CASE_CONSTANT instead\n    ),\n    className: \"title.class\",\n    keywords: {\n      _: [\n        // se we still get relevance credit for JS library classes\n        ...TYPES,\n        ...ERROR_TYPES\n      ]\n    }\n  };\n\n  const USE_STRICT = {\n    label: \"use_strict\",\n    className: 'meta',\n    relevance: 10,\n    begin: /^\\s*['\"]use (strict|asm)['\"]/\n  };\n\n  const FUNCTION_DEFINITION = {\n    variants: [\n      {\n        match: [\n          /function/,\n          /\\s+/,\n          IDENT_RE$1,\n          /(?=\\s*\\()/\n        ]\n      },\n      // anonymous function\n      {\n        match: [\n          /function/,\n          /\\s*(?=\\()/\n        ]\n      }\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    label: \"func.def\",\n    contains: [ PARAMS ],\n    illegal: /%/\n  };\n\n  const UPPER_CASE_CONSTANT = {\n    relevance: 0,\n    match: /\\b[A-Z][A-Z_0-9]+\\b/,\n    className: \"variable.constant\"\n  };\n\n  function noneOf(list) {\n    return regex.concat(\"(?!\", list.join(\"|\"), \")\");\n  }\n\n  const FUNCTION_CALL = {\n    match: regex.concat(\n      /\\b/,\n      noneOf([\n        ...BUILT_IN_GLOBALS,\n        \"super\",\n        \"import\"\n      ]),\n      IDENT_RE$1, regex.lookahead(/\\(/)),\n    className: \"title.function\",\n    relevance: 0\n  };\n\n  const PROPERTY_ACCESS = {\n    begin: regex.concat(/\\./, regex.lookahead(\n      regex.concat(IDENT_RE$1, /(?![0-9A-Za-z$_(])/)\n    )),\n    end: IDENT_RE$1,\n    excludeBegin: true,\n    keywords: \"prototype\",\n    className: \"property\",\n    relevance: 0\n  };\n\n  const GETTER_OR_SETTER = {\n    match: [\n      /get|set/,\n      /\\s+/,\n      IDENT_RE$1,\n      /(?=\\()/\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      { // eat to avoid empty params\n        begin: /\\(\\)/\n      },\n      PARAMS\n    ]\n  };\n\n  const FUNC_LEAD_IN_RE = '(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)|' + hljs.UNDERSCORE_IDENT_RE + ')\\\\s*=>';\n\n  const FUNCTION_VARIABLE = {\n    match: [\n      /const|var|let/, /\\s+/,\n      IDENT_RE$1, /\\s*/,\n      /=\\s*/,\n      /(async\\s*)?/, // async is optional\n      regex.lookahead(FUNC_LEAD_IN_RE)\n    ],\n    keywords: \"async\",\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      PARAMS\n    ]\n  };\n\n  return {\n    name: 'JavaScript',\n    aliases: ['js', 'jsx', 'mjs', 'cjs'],\n    keywords: KEYWORDS$1,\n    // this will be extended by TypeScript\n    exports: { PARAMS_CONTAINS, CLASS_REFERENCE },\n    illegal: /#(?![$_A-z])/,\n    contains: [\n      hljs.SHEBANG({\n        label: \"shebang\",\n        binary: \"node\",\n        relevance: 5\n      }),\n      USE_STRICT,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      HTML_TEMPLATE,\n      CSS_TEMPLATE,\n      GRAPHQL_TEMPLATE,\n      TEMPLATE_STRING,\n      COMMENT,\n      // Skip numbers when they are part of a variable name\n      { match: /\\$\\d+/ },\n      NUMBER,\n      CLASS_REFERENCE,\n      {\n        className: 'attr',\n        begin: IDENT_RE$1 + regex.lookahead(':'),\n        relevance: 0\n      },\n      FUNCTION_VARIABLE,\n      { // \"value\" container\n        begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n        keywords: 'return throw case',\n        relevance: 0,\n        contains: [\n          COMMENT,\n          hljs.REGEXP_MODE,\n          {\n            className: 'function',\n            // we have to count the parens to make sure we actually have the\n            // correct bounding ( ) before the =>.  There could be any number of\n            // sub-expressions inside also surrounded by parens.\n            begin: FUNC_LEAD_IN_RE,\n            returnBegin: true,\n            end: '\\\\s*=>',\n            contains: [\n              {\n                className: 'params',\n                variants: [\n                  {\n                    begin: hljs.UNDERSCORE_IDENT_RE,\n                    relevance: 0\n                  },\n                  {\n                    className: null,\n                    begin: /\\(\\s*\\)/,\n                    skip: true\n                  },\n                  {\n                    begin: /\\(/,\n                    end: /\\)/,\n                    excludeBegin: true,\n                    excludeEnd: true,\n                    keywords: KEYWORDS$1,\n                    contains: PARAMS_CONTAINS\n                  }\n                ]\n              }\n            ]\n          },\n          { // could be a comma delimited list of params to a function call\n            begin: /,/,\n            relevance: 0\n          },\n          {\n            match: /\\s+/,\n            relevance: 0\n          },\n          { // JSX\n            variants: [\n              { begin: FRAGMENT.begin, end: FRAGMENT.end },\n              { match: XML_SELF_CLOSING },\n              {\n                begin: XML_TAG.begin,\n                // we carefully check the opening tag to see if it truly\n                // is a tag and not a false positive\n                'on:begin': XML_TAG.isTrulyOpeningTag,\n                end: XML_TAG.end\n              }\n            ],\n            subLanguage: 'xml',\n            contains: [\n              {\n                begin: XML_TAG.begin,\n                end: XML_TAG.end,\n                skip: true,\n                contains: ['self']\n              }\n            ]\n          }\n        ],\n      },\n      FUNCTION_DEFINITION,\n      {\n        // prevent this from getting swallowed up by function\n        // since they appear \"function like\"\n        beginKeywords: \"while if switch catch for\"\n      },\n      {\n        // we have to count the parens to make sure we actually have the correct\n        // bounding ( ).  There could be any number of sub-expressions inside\n        // also surrounded by parens.\n        begin: '\\\\b(?!function)' + hljs.UNDERSCORE_IDENT_RE +\n          '\\\\(' + // first parens\n          '[^()]*(\\\\(' +\n            '[^()]*(\\\\(' +\n              '[^()]*' +\n            '\\\\)[^()]*)*' +\n          '\\\\)[^()]*)*' +\n          '\\\\)\\\\s*\\\\{', // end parens\n        returnBegin:true,\n        label: \"func.def\",\n        contains: [\n          PARAMS,\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1, className: \"title.function\" })\n        ]\n      },\n      // catch ... so it won't trigger the property rule below\n      {\n        match: /\\.\\.\\./,\n        relevance: 0\n      },\n      PROPERTY_ACCESS,\n      // hack: prevents detection of keywords in some circumstances\n      // .keyword()\n      // $keyword = x\n      {\n        match: '\\\\$' + IDENT_RE$1,\n        relevance: 0\n      },\n      {\n        match: [ /\\bconstructor(?=\\s*\\()/ ],\n        className: { 1: \"title.function\" },\n        contains: [ PARAMS ]\n      },\n      FUNCTION_CALL,\n      UPPER_CASE_CONSTANT,\n      CLASS_OR_EXTENDS,\n      GETTER_OR_SETTER,\n      {\n        match: /\\$[(.]/ // relevance booster for a pattern common to JS libs: `$(something)` and `$.something`\n      }\n    ]\n  };\n}\n\n/*\nLanguage: TypeScript\nAuthor: Panu Horsmalahti <<EMAIL>>\nContributors: Ike Ku <<EMAIL>>\nDescription: TypeScript is a strict superset of JavaScript\nWebsite: https://www.typescriptlang.org\nCategory: common, scripting\n*/\n\n\n/** @type LanguageFn */\nfunction typescript(hljs) {\n  const tsLanguage = javascript(hljs);\n\n  const IDENT_RE$1 = IDENT_RE;\n  const TYPES = [\n    \"any\",\n    \"void\",\n    \"number\",\n    \"boolean\",\n    \"string\",\n    \"object\",\n    \"never\",\n    \"symbol\",\n    \"bigint\",\n    \"unknown\"\n  ];\n  const NAMESPACE = {\n    beginKeywords: 'namespace',\n    end: /\\{/,\n    excludeEnd: true,\n    contains: [ tsLanguage.exports.CLASS_REFERENCE ]\n  };\n  const INTERFACE = {\n    beginKeywords: 'interface',\n    end: /\\{/,\n    excludeEnd: true,\n    keywords: {\n      keyword: 'interface extends',\n      built_in: TYPES\n    },\n    contains: [ tsLanguage.exports.CLASS_REFERENCE ]\n  };\n  const USE_STRICT = {\n    className: 'meta',\n    relevance: 10,\n    begin: /^\\s*['\"]use strict['\"]/\n  };\n  const TS_SPECIFIC_KEYWORDS = [\n    \"type\",\n    \"namespace\",\n    \"interface\",\n    \"public\",\n    \"private\",\n    \"protected\",\n    \"implements\",\n    \"declare\",\n    \"abstract\",\n    \"readonly\",\n    \"enum\",\n    \"override\"\n  ];\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS.concat(TS_SPECIFIC_KEYWORDS),\n    literal: LITERALS,\n    built_in: BUILT_INS.concat(TYPES),\n    \"variable.language\": BUILT_IN_VARIABLES\n  };\n  const DECORATOR = {\n    className: 'meta',\n    begin: '@' + IDENT_RE$1,\n  };\n\n  const swapMode = (mode, label, replacement) => {\n    const indx = mode.contains.findIndex(m => m.label === label);\n    if (indx === -1) { throw new Error(\"can not find mode to replace\"); }\n\n    mode.contains.splice(indx, 1, replacement);\n  };\n\n\n  // this should update anywhere keywords is used since\n  // it will be the same actual JS object\n  Object.assign(tsLanguage.keywords, KEYWORDS$1);\n\n  tsLanguage.exports.PARAMS_CONTAINS.push(DECORATOR);\n  tsLanguage.contains = tsLanguage.contains.concat([\n    DECORATOR,\n    NAMESPACE,\n    INTERFACE,\n  ]);\n\n  // TS gets a simpler shebang rule than JS\n  swapMode(tsLanguage, \"shebang\", hljs.SHEBANG());\n  // JS use strict rule purposely excludes `asm` which makes no sense\n  swapMode(tsLanguage, \"use_strict\", USE_STRICT);\n\n  const functionDeclaration = tsLanguage.contains.find(m => m.label === \"func.def\");\n  functionDeclaration.relevance = 0; // () => {} is more typical in TypeScript\n\n  Object.assign(tsLanguage, {\n    name: 'TypeScript',\n    aliases: [\n      'ts',\n      'tsx',\n      'mts',\n      'cts'\n    ]\n  });\n\n  return tsLanguage;\n}\n\nmodule.exports = typescript;\n", "/*\nLanguage: Visual Basic .NET\nDescription: Visual Basic .NET (VB.NET) is a multi-paradigm, object-oriented programming language, implemented on the .NET Framework.\nAuthors: <AUTHORS>\nWebsite: https://docs.microsoft.com/dotnet/visual-basic/getting-started\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction vbnet(hljs) {\n  const regex = hljs.regex;\n  /**\n   * Character Literal\n   * Either a single character (\"a\"C) or an escaped double quote (\"\"\"\"C).\n   */\n  const CHARACTER = {\n    className: 'string',\n    begin: /\"(\"\"|[^/n])\"C\\b/\n  };\n\n  const STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    illegal: /\\n/,\n    contains: [\n      {\n        // double quote escape\n        begin: /\"\"/ }\n    ]\n  };\n\n  /** Date Literals consist of a date, a time, or both separated by whitespace, surrounded by # */\n  const MM_DD_YYYY = /\\d{1,2}\\/\\d{1,2}\\/\\d{4}/;\n  const YYYY_MM_DD = /\\d{4}-\\d{1,2}-\\d{1,2}/;\n  const TIME_12H = /(\\d|1[012])(:\\d+){0,2} *(AM|PM)/;\n  const TIME_24H = /\\d{1,2}(:\\d{1,2}){1,2}/;\n  const DATE = {\n    className: 'literal',\n    variants: [\n      {\n        // #YYYY-MM-DD# (ISO-Date) or #M/D/YYYY# (US-Date)\n        begin: regex.concat(/# */, regex.either(YYYY_MM_DD, MM_DD_YYYY), / *#/) },\n      {\n        // #H:mm[:ss]# (24h Time)\n        begin: regex.concat(/# */, TIME_24H, / *#/) },\n      {\n        // #h[:mm[:ss]] A# (12h Time)\n        begin: regex.concat(/# */, TIME_12H, / *#/) },\n      {\n        // date plus time\n        begin: regex.concat(\n          /# */,\n          regex.either(YYYY_MM_DD, MM_DD_YYYY),\n          / +/,\n          regex.either(TIME_12H, TIME_24H),\n          / *#/\n        ) }\n    ]\n  };\n\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      {\n        // Float\n        begin: /\\b\\d[\\d_]*((\\.[\\d_]+(E[+-]?[\\d_]+)?)|(E[+-]?[\\d_]+))[RFD@!#]?/ },\n      {\n        // Integer (base 10)\n        begin: /\\b\\d[\\d_]*((U?[SIL])|[%&])?/ },\n      {\n        // Integer (base 16)\n        begin: /&H[\\dA-F_]+((U?[SIL])|[%&])?/ },\n      {\n        // Integer (base 8)\n        begin: /&O[0-7_]+((U?[SIL])|[%&])?/ },\n      {\n        // Integer (base 2)\n        begin: /&B[01_]+((U?[SIL])|[%&])?/ }\n    ]\n  };\n\n  const LABEL = {\n    className: 'label',\n    begin: /^\\w+:/\n  };\n\n  const DOC_COMMENT = hljs.COMMENT(/'''/, /$/, { contains: [\n    {\n      className: 'doctag',\n      begin: /<\\/?/,\n      end: />/\n    }\n  ] });\n\n  const COMMENT = hljs.COMMENT(null, /$/, { variants: [\n    { begin: /'/ },\n    {\n      // TODO: Use multi-class for leading spaces\n      begin: /([\\t ]|^)REM(?=\\s)/ }\n  ] });\n\n  const DIRECTIVES = {\n    className: 'meta',\n    // TODO: Use multi-class for indentation once available\n    begin: /[\\t ]*#(const|disable|else|elseif|enable|end|externalsource|if|region)\\b/,\n    end: /$/,\n    keywords: { keyword:\n        'const disable else elseif enable end externalsource if region then' },\n    contains: [ COMMENT ]\n  };\n\n  return {\n    name: 'Visual Basic .NET',\n    aliases: [ 'vb' ],\n    case_insensitive: true,\n    classNameAliases: { label: 'symbol' },\n    keywords: {\n      keyword:\n        'addhandler alias aggregate ansi as async assembly auto binary by byref byval ' /* a-b */\n        + 'call case catch class compare const continue custom declare default delegate dim distinct do ' /* c-d */\n        + 'each equals else elseif end enum erase error event exit explicit finally for friend from function ' /* e-f */\n        + 'get global goto group handles if implements imports in inherits interface into iterator ' /* g-i */\n        + 'join key let lib loop me mid module mustinherit mustoverride mybase myclass ' /* j-m */\n        + 'namespace narrowing new next notinheritable notoverridable ' /* n */\n        + 'of off on operator option optional order overloads overridable overrides ' /* o */\n        + 'paramarray partial preserve private property protected public ' /* p */\n        + 'raiseevent readonly redim removehandler resume return ' /* r */\n        + 'select set shadows shared skip static step stop structure strict sub synclock ' /* s */\n        + 'take text then throw to try unicode until using when where while widening with withevents writeonly yield' /* t-y */,\n      built_in:\n        // Operators https://docs.microsoft.com/dotnet/visual-basic/language-reference/operators\n        'addressof and andalso await directcast gettype getxmlnamespace is isfalse isnot istrue like mod nameof new not or orelse trycast typeof xor '\n        // Type Conversion Functions https://docs.microsoft.com/dotnet/visual-basic/language-reference/functions/type-conversion-functions\n        + 'cbool cbyte cchar cdate cdbl cdec cint clng cobj csbyte cshort csng cstr cuint culng cushort',\n      type:\n        // Data types https://docs.microsoft.com/dotnet/visual-basic/language-reference/data-types\n        'boolean byte char date decimal double integer long object sbyte short single string uinteger ulong ushort',\n      literal: 'true false nothing'\n    },\n    illegal:\n      '//|\\\\{|\\\\}|endif|gosub|variant|wend|^\\\\$ ' /* reserved deprecated keywords */,\n    contains: [\n      CHARACTER,\n      STRING,\n      DATE,\n      NUMBER,\n      LABEL,\n      DOC_COMMENT,\n      COMMENT,\n      DIRECTIVES\n    ]\n  };\n}\n\nmodule.exports = vbnet;\n", "/*\nLanguage: WebAssembly\nWebsite: https://webassembly.org\nDescription:  Wasm is designed as a portable compilation target for programming languages, enabling deployment on the web for client and server applications.\nCategory: web, common\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction wasm(hljs) {\n  hljs.regex;\n  const BLOCK_COMMENT = hljs.COMMENT(/\\(;/, /;\\)/);\n  BLOCK_COMMENT.contains.push(\"self\");\n  const LINE_COMMENT = hljs.COMMENT(/;;/, /$/);\n\n  const KWS = [\n    \"anyfunc\",\n    \"block\",\n    \"br\",\n    \"br_if\",\n    \"br_table\",\n    \"call\",\n    \"call_indirect\",\n    \"data\",\n    \"drop\",\n    \"elem\",\n    \"else\",\n    \"end\",\n    \"export\",\n    \"func\",\n    \"global.get\",\n    \"global.set\",\n    \"local.get\",\n    \"local.set\",\n    \"local.tee\",\n    \"get_global\",\n    \"get_local\",\n    \"global\",\n    \"if\",\n    \"import\",\n    \"local\",\n    \"loop\",\n    \"memory\",\n    \"memory.grow\",\n    \"memory.size\",\n    \"module\",\n    \"mut\",\n    \"nop\",\n    \"offset\",\n    \"param\",\n    \"result\",\n    \"return\",\n    \"select\",\n    \"set_global\",\n    \"set_local\",\n    \"start\",\n    \"table\",\n    \"tee_local\",\n    \"then\",\n    \"type\",\n    \"unreachable\"\n  ];\n\n  const FUNCTION_REFERENCE = {\n    begin: [\n      /(?:func|call|call_indirect)/,\n      /\\s+/,\n      /\\$[^\\s)]+/\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    }\n  };\n\n  const ARGUMENT = {\n    className: \"variable\",\n    begin: /\\$[\\w_]+/\n  };\n\n  const PARENS = {\n    match: /(\\((?!;)|\\))+/,\n    className: \"punctuation\",\n    relevance: 0\n  };\n\n  const NUMBER = {\n    className: \"number\",\n    relevance: 0,\n    // borrowed from Prism, TODO: split out into variants\n    match: /[+-]?\\b(?:\\d(?:_?\\d)*(?:\\.\\d(?:_?\\d)*)?(?:[eE][+-]?\\d(?:_?\\d)*)?|0x[\\da-fA-F](?:_?[\\da-fA-F])*(?:\\.[\\da-fA-F](?:_?[\\da-fA-D])*)?(?:[pP][+-]?\\d(?:_?\\d)*)?)\\b|\\binf\\b|\\bnan(?::0x[\\da-fA-F](?:_?[\\da-fA-D])*)?\\b/\n  };\n\n  const TYPE = {\n    // look-ahead prevents us from gobbling up opcodes\n    match: /(i32|i64|f32|f64)(?!\\.)/,\n    className: \"type\"\n  };\n\n  const MATH_OPERATIONS = {\n    className: \"keyword\",\n    // borrowed from Prism, TODO: split out into variants\n    match: /\\b(f32|f64|i32|i64)(?:\\.(?:abs|add|and|ceil|clz|const|convert_[su]\\/i(?:32|64)|copysign|ctz|demote\\/f64|div(?:_[su])?|eqz?|extend_[su]\\/i32|floor|ge(?:_[su])?|gt(?:_[su])?|le(?:_[su])?|load(?:(?:8|16|32)_[su])?|lt(?:_[su])?|max|min|mul|nearest|neg?|or|popcnt|promote\\/f32|reinterpret\\/[fi](?:32|64)|rem_[su]|rot[lr]|shl|shr_[su]|store(?:8|16|32)?|sqrt|sub|trunc(?:_[su]\\/f(?:32|64))?|wrap\\/i64|xor))\\b/\n  };\n\n  const OFFSET_ALIGN = {\n    match: [\n      /(?:offset|align)/,\n      /\\s*/,\n      /=/\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"operator\"\n    }\n  };\n\n  return {\n    name: 'WebAssembly',\n    keywords: {\n      $pattern: /[\\w.]+/,\n      keyword: KWS\n    },\n    contains: [\n      LINE_COMMENT,\n      BLOCK_COMMENT,\n      OFFSET_ALIGN,\n      ARGUMENT,\n      PARENS,\n      FUNCTION_REFERENCE,\n      hljs.QUOTE_STRING_MODE,\n      TYPE,\n      MATH_OPERATIONS,\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = wasm;\n", "var hljs = require('./core');\n\nhljs.registerLanguage('xml', require('./languages/xml'));\nhljs.registerLanguage('bash', require('./languages/bash'));\nhljs.registerLanguage('c', require('./languages/c'));\nhljs.registerLanguage('cpp', require('./languages/cpp'));\nhljs.registerLanguage('csharp', require('./languages/csharp'));\nhljs.registerLanguage('css', require('./languages/css'));\nhljs.registerLanguage('markdown', require('./languages/markdown'));\nhljs.registerLanguage('diff', require('./languages/diff'));\nhljs.registerLanguage('ruby', require('./languages/ruby'));\nhljs.registerLanguage('go', require('./languages/go'));\nhljs.registerLanguage('graphql', require('./languages/graphql'));\nhljs.registerLanguage('ini', require('./languages/ini'));\nhljs.registerLanguage('java', require('./languages/java'));\nhljs.registerLanguage('javascript', require('./languages/javascript'));\nhljs.registerLanguage('json', require('./languages/json'));\nhljs.registerLanguage('kotlin', require('./languages/kotlin'));\nhljs.registerLanguage('less', require('./languages/less'));\nhljs.registerLanguage('lua', require('./languages/lua'));\nhljs.registerLanguage('makefile', require('./languages/makefile'));\nhljs.registerLanguage('perl', require('./languages/perl'));\nhljs.registerLanguage('objectivec', require('./languages/objectivec'));\nhljs.registerLanguage('php', require('./languages/php'));\nhljs.registerLanguage('php-template', require('./languages/php-template'));\nhljs.registerLanguage('plaintext', require('./languages/plaintext'));\nhljs.registerLanguage('python', require('./languages/python'));\nhljs.registerLanguage('python-repl', require('./languages/python-repl'));\nhljs.registerLanguage('r', require('./languages/r'));\nhljs.registerLanguage('rust', require('./languages/rust'));\nhljs.registerLanguage('scss', require('./languages/scss'));\nhljs.registerLanguage('shell', require('./languages/shell'));\nhljs.registerLanguage('sql', require('./languages/sql'));\nhljs.registerLanguage('swift', require('./languages/swift'));\nhljs.registerLanguage('yaml', require('./languages/yaml'));\nhljs.registerLanguage('typescript', require('./languages/typescript'));\nhljs.registerLanguage('vbnet', require('./languages/vbnet'));\nhljs.registerLanguage('wasm', require('./languages/wasm'));\n\nhljs.HighlightJS = hljs\nhljs.default = hljs\nmodule.exports = hljs;", "// https://nodejs.org/api/packages.html#packages_writing_dual_packages_while_avoiding_or_minimizing_hazards\nimport HighlightJS from '../lib/common.js';\nexport { HighlightJS };\nexport default HighlightJS;\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAQA,aAAS,IAAI,MAAM;AACjB,YAAM,QAAQ,KAAK;AAQnB,YAAM,cAAc,MAAM,OAAO,aAAa,MAAM,SAAS,kBAAkB,GAAG,iBAAiB;AACnG,YAAM,eAAe;AACrB,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,oBAAoB;AAAA,QACxB,OAAO;AAAA,QACP,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,SAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AACA,YAAM,wBAAwB,KAAK,QAAQ,mBAAmB;AAAA,QAC5D,OAAO;AAAA,QACP,KAAK;AAAA,MACP,CAAC;AACD,YAAM,wBAAwB,KAAK,QAAQ,KAAK,kBAAkB,EAAE,WAAW,SAAS,CAAC;AACzF,YAAM,yBAAyB,KAAK,QAAQ,KAAK,mBAAmB,EAAE,WAAW,SAAS,CAAC;AAC3F,YAAM,gBAAgB;AAAA,QACpB,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,YAAY;AAAA,gBACZ,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,oBACL,UAAU,CAAE,YAAa;AAAA,kBAC3B;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,oBACL,UAAU,CAAE,YAAa;AAAA,kBAC3B;AAAA,kBACA,EAAE,OAAO,eAAe;AAAA,gBAC1B;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,kBACR;AAAA,oBACE,WAAW;AAAA,oBACX,OAAO;AAAA,oBACP,KAAK;AAAA,oBACL,UAAU;AAAA,sBACR;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA,EAAE,WAAW,GAAG;AAAA,UAClB;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UAEF;AAAA,UACA;AAAA,YACE,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAMX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,EAAE,MAAM,QAAQ;AAAA,YAC1B,UAAU,CAAE,aAAc;AAAA,YAC1B,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,WAAW;AAAA,cACX,aAAa;AAAA,gBACX;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA;AAAA,YAEX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,EAAE,MAAM,SAAS;AAAA,YAC3B,UAAU,CAAE,aAAc;AAAA,YAC1B,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,WAAW;AAAA,cACX,aAAa;AAAA,gBACX;AAAA,gBACA;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,MAAM;AAAA,cACX;AAAA,cACA,MAAM,UAAU,MAAM;AAAA,gBACpB;AAAA;AAAA;AAAA;AAAA,gBAIA,MAAM,OAAO,OAAO,KAAK,IAAI;AAAA,cAC/B,CAAC;AAAA,YACH;AAAA,YACA,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,WAAW;AAAA,gBACX,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,MAAM;AAAA,cACX;AAAA,cACA,MAAM,UAAU,MAAM;AAAA,gBACpB;AAAA,gBAAa;AAAA,cACf,CAAC;AAAA,YACH;AAAA,YACA,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,gBACX,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChPjB;AAAA;AASA,aAAS,KAAK,MAAM;AAClB,YAAM,QAAQ,KAAK;AACnB,YAAM,MAAM,CAAC;AACb,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,UAAU,CAAE,GAAI;AAAA,UAClB;AAAA;AAAA,QACF;AAAA,MACF;AACA,aAAO,OAAO,KAAK;AAAA,QACjB,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,MAAM;AAAA,YAAO;AAAA;AAAA;AAAA,YAGpB;AAAA,UAAqB,EAAE;AAAA,UACzB;AAAA,QACF;AAAA,MACF,CAAC;AAED,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,KAAK,gBAAiB;AAAA,MACpC;AACA,YAAM,WAAW;AAAA,QACf,OAAO;AAAA,QACP,QAAQ,EAAE,UAAU;AAAA,UAClB,KAAK,kBAAkB;AAAA,YACrB,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb,CAAC;AAAA,QACH,EAAE;AAAA,MACJ;AACA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS,KAAK,YAAY;AAChC,YAAM,gBAAgB;AAAA,QACpB,OAAO;AAAA,MACT;AACA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AACA,YAAM,eAAe;AAAA,QACnB,OAAO;AAAA,MACT;AACA,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,YAAM,iBAAiB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,gBAAgB,KAAK,QAAQ;AAAA,QACjC,QAAQ,IAAI,eAAe,KAAK,GAAG,CAAC;AAAA,QACpC,WAAW;AAAA,MACb,CAAC;AACD,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU,CAAE,KAAK,QAAQ,KAAK,YAAY,EAAE,OAAO,aAAa,CAAC,CAAE;AAAA,QACnE,WAAW;AAAA,MACb;AAEA,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,MACF;AAGA,YAAM,YAAY,EAAE,OAAO,iBAAiB;AAG5C,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,iBAAiB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,iBAAiB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,IAAK;AAAA,QAChB,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,YACR,GAAG;AAAA,YACH,GAAG;AAAA;AAAA,YAEH;AAAA,YACA;AAAA,YACA,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA;AAAA,UACA,KAAK,QAAQ;AAAA;AAAA,UACb;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtYjB;AAAA;AAOA,aAAS,EAAE,MAAM;AACf,YAAM,QAAQ,KAAK;AAInB,YAAM,sBAAsB,KAAK,QAAQ,MAAM,KAAK,EAAE,UAAU,CAAE,EAAE,OAAO,OAAO,CAAE,EAAE,CAAC;AACvF,YAAM,mBAAmB;AACzB,YAAM,eAAe;AACrB,YAAM,uBAAuB;AAC7B,YAAM,mBAAmB,MACrB,mBAAmB,MACnB,MAAM,SAAS,YAAY,IAC3B,kBAAkB,MAAM,SAAS,oBAAoB,IACvD;AAGF,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,qBAAqB;AAAA,UAC9B,EAAE,OAAO,wBAAwB;AAAA,QACnC;AAAA,MAEF;AAIA,YAAM,oBAAoB;AAC1B,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU,CAAE,KAAK,gBAAiB;AAAA,UACpC;AAAA,UACA;AAAA,YACE,OAAO,iBAAkB,oBAAoB;AAAA,YAC7C,KAAK;AAAA,YACL,SAAS;AAAA,UACX;AAAA,UACA,KAAK,kBAAkB;AAAA,YACrB,OAAO;AAAA,YACP,KAAK;AAAA,UACP,CAAC;AAAA,QACH;AAAA,MACF;AAEA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,gBAAiB;AAAA,UAC1B,EAAE,OAAO,wFAA2F;AAAA,UACpG,EAAE,OAAO,sFAA2F;AAAA,QACtG;AAAA,QACA,WAAW;AAAA,MACb;AAEA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,EAAE,SACR,yFACwC;AAAA,QAC5C,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA,KAAK,QAAQ,SAAS,EAAE,WAAW,SAAS,CAAC;AAAA,UAC7C;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,UACA,KAAK;AAAA,QACP;AAAA,MACF;AAEA,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,OAAO,MAAM,SAAS,YAAY,IAAI,KAAK;AAAA,QAC3C,WAAW;AAAA,MACb;AAEA,YAAM,iBAAiB,MAAM,SAAS,YAAY,IAAI,KAAK,WAAW;AAEtE,YAAM,aAAa;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,MACF;AAEA,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf,SAAS;AAAA,QACT,MAAM;AAAA,QACN,SAAS;AAAA;AAAA,QAET,UAAU;AAAA,MASZ;AAEA,YAAM,sBAAsB;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAEA,YAAM,qBAAqB;AAAA;AAAA;AAAA;AAAA,QAIzB,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,UAAU,oBAAoB,OAAO;AAAA,UACnC;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU,oBAAoB,OAAO,CAAE,MAAO,CAAC;AAAA,YAC/C,WAAW;AAAA,UACb;AAAA,QACF,CAAC;AAAA,QACD,WAAW;AAAA,MACb;AAEA,YAAM,uBAAuB;AAAA,QAC3B,OAAO,MAAM,mBAAmB,iBAAiB;AAAA,QACjD,aAAa;AAAA,QACb,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA;AAAA,YACE,OAAO;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,aAAa;AAAA,YACb,UAAU,CAAE,KAAK,QAAQ,YAAY,EAAE,WAAW,iBAAiB,CAAC,CAAE;AAAA,YACtE,WAAW;AAAA,UACb;AAAA;AAAA;AAAA,UAGA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,cACA,KAAK;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA;AAAA,cAEA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,kBACA;AAAA,kBACA,KAAK;AAAA,kBACL;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,GAAI;AAAA,QACf,UAAU;AAAA;AAAA;AAAA,QAGV,mBAAmB;AAAA,QACnB,SAAS;AAAA,QACT,UAAU,CAAC,EAAE;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO,KAAK,WAAW;AAAA,cACvB,UAAU;AAAA,YACZ;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,eAAe;AAAA,cACf,KAAK;AAAA,cACL,UAAU;AAAA,gBACR,EAAE,eAAe,qBAAqB;AAAA,gBACtC,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,QAAC;AAAA,QACH,SAAS;AAAA,UACP,cAAc;AAAA,UACd,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7TjB;AAAA;AAOA,aAAS,IAAI,MAAM;AACjB,YAAM,QAAQ,KAAK;AAInB,YAAM,sBAAsB,KAAK,QAAQ,MAAM,KAAK,EAAE,UAAU,CAAE,EAAE,OAAO,OAAO,CAAE,EAAE,CAAC;AACvF,YAAM,mBAAmB;AACzB,YAAM,eAAe;AACrB,YAAM,uBAAuB;AAC7B,YAAM,mBAAmB,gBACrB,mBAAmB,MACnB,MAAM,SAAS,YAAY,IAC3B,kBAAkB,MAAM,SAAS,oBAAoB,IACvD;AAEF,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAIA,YAAM,oBAAoB;AAC1B,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU,CAAE,KAAK,gBAAiB;AAAA,UACpC;AAAA,UACA;AAAA,YACE,OAAO,iBAAkB,oBAAoB;AAAA,YAC7C,KAAK;AAAA,YACL,SAAS;AAAA,UACX;AAAA,UACA,KAAK,kBAAkB;AAAA,YACrB,OAAO;AAAA,YACP,KAAK;AAAA,UACP,CAAC;AAAA,QACH;AAAA,MACF;AAEA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,gBAAiB;AAAA,UAC1B,EAAE,OAAO,wFAA2F;AAAA,UACpG,EAAE,OAAO,sFAA2F;AAAA,QACtG;AAAA,QACA,WAAW;AAAA,MACb;AAEA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,EAAE,SACR,yFACwC;AAAA,QAC5C,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA,KAAK,QAAQ,SAAS,EAAE,WAAW,SAAS,CAAC;AAAA,UAC7C;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,UACA,KAAK;AAAA,QACP;AAAA,MACF;AAEA,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,OAAO,MAAM,SAAS,YAAY,IAAI,KAAK;AAAA,QAC3C,WAAW;AAAA,MACb;AAEA,YAAM,iBAAiB,MAAM,SAAS,YAAY,IAAI,KAAK,WAAW;AAGtE,YAAM,oBAAoB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,iBAAiB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,aAAa;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,iBAAiB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,WAAW,CAAE,SAAU;AAE7B,YAAM,eAAe;AAAA,QACnB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAEA,YAAM,oBAAoB;AAAA,QACxB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA;AAAA,UAER,OAAO;AAAA,QAAe;AAAA,QACxB,OAAO,MAAM;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,MAAM,UAAU,kBAAkB;AAAA,QAAC;AAAA,MACvC;AAEA,YAAM,sBAAsB;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAEA,YAAM,qBAAqB;AAAA;AAAA;AAAA;AAAA,QAIzB,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,UAAU,oBAAoB,OAAO;AAAA,UACnC;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU,oBAAoB,OAAO,CAAE,MAAO,CAAC;AAAA,YAC/C,WAAW;AAAA,UACb;AAAA,QACF,CAAC;AAAA,QACD,WAAW;AAAA,MACb;AAEA,YAAM,uBAAuB;AAAA,QAC3B,WAAW;AAAA,QACX,OAAO,MAAM,mBAAmB,iBAAiB;AAAA,QACjD,aAAa;AAAA,QACb,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA;AAAA,YACE,OAAO;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,aAAa;AAAA,YACb,UAAU,CAAE,UAAW;AAAA,YACvB,WAAW;AAAA,UACb;AAAA;AAAA;AAAA,UAGA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,gBAAgB;AAAA,YAChB,UAAU;AAAA,cACR;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA;AAAA;AAAA,UAGA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,cACA,KAAK;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA;AAAA,cAEA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,kBACA;AAAA,kBACA,KAAK;AAAA,kBACL;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,QACT,kBAAkB,EAAE,qBAAqB,WAAW;AAAA,QACpD,UAAU,CAAC,EAAE;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU;AAAA,cACV,UAAU;AAAA,gBACR;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE,OAAO,KAAK,WAAW;AAAA,cACvB,UAAU;AAAA,YACZ;AAAA,YACA;AAAA,cACE,OAAO;AAAA;AAAA,gBAEL;AAAA,gBACA;AAAA,gBACA;AAAA,cACF;AAAA,cACA,WAAW;AAAA,gBACT,GAAG;AAAA,gBACH,GAAG;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,QAAC;AAAA,MACL;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvjBjB;AAAA;AASA,aAAS,OAAO,MAAM;AACpB,YAAM,oBAAoB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,qBAAqB;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,sBAAsB;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf,SAAS,gBAAgB,OAAO,mBAAmB;AAAA,QACnD,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AACA,YAAM,aAAa,KAAK,QAAQ,KAAK,YAAY,EAAE,OAAO,qBAAqB,CAAC;AAChF,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,gBAAiB;AAAA,UAC1B,EAAE,OAAO,kEAAqE;AAAA,UAC9E,EAAE,OAAO,sFAA2F;AAAA,QACtG;AAAA,QACA,WAAW;AAAA,MACb;AACA,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,EAAE,OAAO,KAAK,CAAE;AAAA,MAC9B;AACA,YAAM,wBAAwB,KAAK,QAAQ,iBAAiB,EAAE,SAAS,KAAK,CAAC;AAC7E,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,YAAM,cAAc,KAAK,QAAQ,OAAO,EAAE,SAAS,KAAK,CAAC;AACzD,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,UACR,EAAE,OAAO,OAAO;AAAA,UAChB,EAAE,OAAO,OAAO;AAAA,UAChB,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,YAAM,+BAA+B;AAAA,QACnC,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,EAAE,OAAO,OAAO;AAAA,UAChB,EAAE,OAAO,OAAO;AAAA,UAChB,EAAE,OAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,MACF;AACA,YAAM,qCAAqC,KAAK,QAAQ,8BAA8B;AAAA,QACpF,SAAS;AAAA,QACT,UAAU;AAAA,UACR,EAAE,OAAO,OAAO;AAAA,UAChB,EAAE,OAAO,OAAO;AAAA,UAChB,EAAE,OAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA,KAAK;AAAA,MACP;AACA,kBAAY,WAAW;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA,KAAK,QAAQ,KAAK,sBAAsB,EAAE,SAAS,KAAK,CAAC;AAAA,MAC3D;AACA,YAAM,SAAS,EAAE,UAAU;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,MACP,EAAE;AAEF,YAAM,mBAAmB;AAAA,QACvB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,EAAE,eAAe,SAAS;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AACA,YAAM,gBAAgB,KAAK,WAAW,OAAO,KAAK,WAAW,eAAe,KAAK,WAAW;AAC5F,YAAM,gBAAgB;AAAA;AAAA;AAAA,QAGpB,OAAO,MAAM,KAAK;AAAA,QAClB,WAAW;AAAA,MACb;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,aAAa;AAAA,cACb,UAAU;AAAA,gBACR;AAAA,kBACE,WAAW;AAAA,kBACX,UAAU;AAAA,oBACR;AAAA,sBACE,OAAO;AAAA,sBACP,WAAW;AAAA,oBACb;AAAA,oBACA,EAAE,OAAO,WAAW;AAAA,oBACpB;AAAA,sBACE,OAAO;AAAA,sBACP,KAAK;AAAA,oBACP;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,EAAE,SAAS,sFAAsF;AAAA,UAC7G;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,WAAW;AAAA,YACX,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR,EAAE,eAAe,cAAc;AAAA,cAC/B;AAAA,cACA;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,WAAW;AAAA,YACX,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,WAAW;AAAA,YACX,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,cAAc;AAAA,YACd,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA;AAAA;AAAA,YAGE,eAAe;AAAA,YACf,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,MAAM,gBAAgB,WAAW,KAAK,WAAW;AAAA,YACxD,aAAa;AAAA,YACb,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,UAAU;AAAA;AAAA,cAER;AAAA,gBACE,eAAe,mBAAmB,KAAK,GAAG;AAAA,gBAC1C,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,OAAO,KAAK,WAAW;AAAA,gBACvB,aAAa;AAAA,gBACb,UAAU;AAAA,kBACR,KAAK;AAAA,kBACL;AAAA,gBACF;AAAA,gBACA,WAAW;AAAA,cACb;AAAA,cACA,EAAE,OAAO,OAAO;AAAA,cAChB;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,kBACA;AAAA,kBACA,KAAK;AAAA,gBACP;AAAA,cACF;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/YjB;AAAA;AAAA,QAAM,QAAQ,CAAC,SAAS;AACtB,aAAO;AAAA,QACL,WAAW;AAAA,UACT,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,QACA,eAAe,KAAK;AAAA,QACpB,UAAU;AAAA,UACR,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA,yBAAyB;AAAA,UACvB,OAAO;AAAA,UACP,OAAO;AAAA,UACP,KAAK;AAAA,UACL,SAAS;AAAA,UACT,UAAU;AAAA,YACR,KAAK;AAAA,YACL,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,iBAAiB;AAAA,UACf,OAAO;AAAA,UACP,OAAO,KAAK,YAAY;AAAA,UASxB,WAAW;AAAA,QACb;AAAA,QACA,cAAc;AAAA,UACZ,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAEA,QAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,iBAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,iBAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,IACF;AAGA,QAAM,kBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA,IAGF,EAAE,QAAQ;AAUV,aAAS,IAAI,MAAM;AACjB,YAAM,QAAQ,KAAK;AACnB,YAAM,QAAQ,MAAM,IAAI;AACxB,YAAM,gBAAgB,EAAE,OAAO,+BAA+B;AAC9D,YAAM,eAAe;AACrB,YAAM,iBAAiB;AACvB,YAAM,WAAW;AACjB,YAAM,UAAU;AAAA,QACd,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU,EAAE,kBAAkB,UAAU;AAAA,QACxC,kBAAkB;AAAA;AAAA;AAAA,UAGhB,kBAAkB;AAAA,QAAe;AAAA,QACnC,UAAU;AAAA,UACR,MAAM;AAAA,UACN;AAAA;AAAA;AAAA,UAGA,MAAM;AAAA,UACN;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,QAAQ;AAAA,YACf,WAAW;AAAA,UACb;AAAA,UACA,MAAM;AAAA,UACN;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR,EAAE,OAAO,OAAO,eAAe,KAAK,GAAG,IAAI,IAAI;AAAA,cAC/C,EAAE,OAAO,WAAW,gBAAgB,KAAK,GAAG,IAAI,IAAI;AAAA,YACtD;AAAA,UACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOA,MAAM;AAAA,UACN;AAAA,YACE,WAAW;AAAA,YACX,OAAO,SAAS,WAAW,KAAK,GAAG,IAAI;AAAA,UACzC;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,GAAG;AAAA;AAAA;AAAA;AAAA,cAIH;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,WAAW;AAAA;AAAA,gBACX,UAAU,EAAE,UAAU,eAAe;AAAA,gBACrC,UAAU;AAAA,kBACR,GAAG;AAAA,kBACH;AAAA,oBACE,WAAW;AAAA;AAAA;AAAA,oBAGX,OAAO;AAAA,oBACP,gBAAgB;AAAA,oBAChB,YAAY;AAAA,kBACd;AAAA,gBACF;AAAA,cACF;AAAA,cACA,MAAM;AAAA,YACR;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO,MAAM,UAAU,GAAG;AAAA,YAC1B,KAAK;AAAA,YACL,WAAW;AAAA,YACX,SAAS;AAAA;AAAA,YACT,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,gBAAgB;AAAA,gBAChB,YAAY;AAAA,gBACZ,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR,UAAU;AAAA,kBACV,SAAS;AAAA,kBACT,WAAW,eAAe,KAAK,GAAG;AAAA,gBACpC;AAAA,gBACA,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO;AAAA,oBACP,WAAW;AAAA,kBACb;AAAA,kBACA,GAAG;AAAA,kBACH,MAAM;AAAA,gBACR;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,SAAS,KAAK,KAAK,GAAG,IAAI;AAAA,UACnC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjuBjB;AAAA;AAQA,aAAS,SAAS,MAAM;AACtB,YAAM,QAAQ,KAAK;AACnB,YAAM,cAAc;AAAA,QAClB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,aAAa;AAAA,QACb,WAAW;AAAA,MACb;AACA,YAAM,kBAAkB;AAAA,QACtB,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AACA,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA;AAAA,UAER,EAAE,OAAO,gCAAgC;AAAA,UACzC,EAAE,OAAO,gCAAgC;AAAA;AAAA,UAEzC;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA,EAAE,OAAO,QAAQ;AAAA,UACjB;AAAA,YACE,OAAO;AAAA;AAAA;AAAA,YAGP,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AACA,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,YAAY;AAAA,MACd;AACA,YAAM,iBAAiB;AAAA,QACrB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AACA,YAAM,aAAa;AACnB,YAAM,OAAO;AAAA,QACX,UAAU;AAAA;AAAA;AAAA,UAGR;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO,MAAM,OAAO,aAAa,YAAY,YAAY;AAAA,YACzD,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,QACF;AAAA,QACA,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAAA;AAAA,YAEE,OAAO;AAAA,UAAW;AAAA,UACpB;AAAA,YACE,WAAW;AAAA,YACX,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,YACd,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AACA,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,UAAU,CAAC;AAAA;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU,CAAC;AAAA;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAKA,YAAM,sBAAsB,KAAK,QAAQ,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC;AAC/D,YAAM,sBAAsB,KAAK,QAAQ,QAAQ,EAAE,UAAU,CAAC,EAAE,CAAC;AACjE,WAAK,SAAS,KAAK,mBAAmB;AACtC,aAAO,SAAS,KAAK,mBAAmB;AAExC,UAAI,cAAc;AAAA,QAChB;AAAA,QACA;AAAA,MACF;AAEA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,EAAE,QAAQ,OAAK;AACb,UAAE,WAAW,EAAE,SAAS,OAAO,WAAW;AAAA,MAC5C,CAAC;AAED,oBAAc,YAAY,OAAO,MAAM,MAAM;AAE7C,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,UAAU;AAAA,cACR,EAAE,OAAO,UAAU;AAAA,cACnB;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,UAAU;AAAA,QACV,KAAK;AAAA,MACP;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChPjB;AAAA;AASA,aAAS,KAAK,MAAM;AAClB,YAAM,QAAQ,KAAK;AACnB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,OAAQ;AAAA,QACnB,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,WAAW;AAAA,YACX,OAAO,MAAM;AAAA,cACX;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO,MAAM;AAAA,kBACX;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF;AAAA,gBACA,KAAK;AAAA,cACP;AAAA,cACA,EAAE,OAAO,WAAW;AAAA,YACtB;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7DjB;AAAA;AASA,aAAS,KAAK,MAAM;AAClB,YAAM,QAAQ,KAAK;AACnB,YAAM,iBAAiB;AAEvB,YAAM,gBAAgB,MAAM;AAAA,QAC1B;AAAA;AAAA,QAEA;AAAA,MACF;AAEA,YAAM,+BAA+B,MAAM,OAAO,eAAe,UAAU;AAG3E,YAAM,aAAa;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,gBAAgB;AAAA,QACpB,qBAAqB;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,qBAAqB;AAAA,UACnB;AAAA,UACA;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,GAAG;AAAA,QACL;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AACA,YAAM,gBAAgB;AAAA,QACpB,KAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA,EAAE,UAAU,CAAE,SAAU,EAAE;AAAA,QAC5B;AAAA,QACA,KAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,YACE,UAAU,CAAE,SAAU;AAAA,YACtB,WAAW;AAAA,UACb;AAAA,QACF;AAAA,QACA,KAAK,QAAQ,YAAY,KAAK,gBAAgB;AAAA,MAChD;AACA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA;AAAA;AAAA,UAGA,EAAE,OAAO,kBAAkB;AAAA,UAC3B,EAAE,OAAO,4BAA4B;AAAA,UACrC,EAAE,OAAO,kCAAkC;AAAA,UAC3C,EAAE,OAAO,0DAA0D;AAAA,UACnE,EAAE,OAAO,0BAA0B;AAAA,UACnC,EAAE,OAAO,YAAY;AAAA;AAAA,UAErB;AAAA;AAAA;AAAA,YAGE,OAAO,MAAM;AAAA,cACX;AAAA,cACA,MAAM,UAAU,0CAA0C;AAAA,YAC5D;AAAA,YACA,UAAU;AAAA,cACR,KAAK,kBAAkB;AAAA,gBACrB,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,kBACR,KAAK;AAAA,kBACL;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAKA,YAAM,UAAU;AAChB,YAAM,SAAS;AACf,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA;AAAA,UAER,EAAE,OAAO,OAAO,OAAO,SAAS,MAAM,iBAAiB,MAAM,aAAa;AAAA;AAAA;AAAA,UAI1E,EAAE,OAAO,iCAAiC;AAAA,UAC1C,EAAE,OAAO,iCAAiC;AAAA,UAC1C,EAAE,OAAO,iCAAiC;AAAA,UAC1C,EAAE,OAAO,6CAA6C;AAAA;AAAA,UAGtD,EAAE,OAAO,wBAAwB;AAAA,QACnC;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAEA,YAAM,iBAAiB;AAAA,QACrB,OAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,GAAG;AAAA,QACL;AAAA,QACA,UAAU;AAAA,MACZ;AAEA,YAAM,mBAAmB;AAAA,QACvB,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,QACA,UAAU;AAAA,MACZ;AAEA,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAEA,YAAM,oBAAoB;AAAA,QACxB,OAAO;AAAA,UACL;AAAA,UAAO;AAAA,UACP;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,QACA,UAAU;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,OAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,GAAG;AAAA,QACL;AAAA,MACF;AAGA,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAEA,YAAM,wBAAwB;AAAA,QAC5B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,UAEE,OAAO,KAAK,WAAW;AAAA,QAAK;AAAA,QAC9B;AAAA,UACE,WAAW;AAAA,UACX,OAAO,KAAK,sBAAsB;AAAA,UAClC,WAAW;AAAA,QACb;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,UAAU;AAAA,YACR;AAAA,YACA,EAAE,OAAO,eAAe;AAAA,UAC1B;AAAA,UACA,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAAA;AAAA,UAGE,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,WAAW;AAAA;AAAA,UACX,UAAU;AAAA,QACZ;AAAA,QACA;AAAA;AAAA,UACE,OAAO,MAAM,KAAK,iBAAiB;AAAA,UACnC,UAAU;AAAA,UACV,UAAU;AAAA,YACR;AAAA,cACE,WAAW;AAAA,cACX,UAAU;AAAA,gBACR,KAAK;AAAA,gBACL;AAAA,cACF;AAAA,cACA,SAAS;AAAA,cACT,UAAU;AAAA,gBACR;AAAA,kBACE,OAAO;AAAA,kBACP,KAAK;AAAA,gBACP;AAAA,gBACA;AAAA,kBACE,OAAO;AAAA,kBACP,KAAK;AAAA,gBACP;AAAA,gBACA;AAAA,kBACE,OAAO;AAAA,kBACP,KAAK;AAAA,gBACP;AAAA,gBACA;AAAA,kBACE,OAAO;AAAA,kBACP,KAAK;AAAA,gBACP;AAAA,gBACA;AAAA,kBACE,OAAO;AAAA,kBACP,KAAK;AAAA,gBACP;AAAA,cACF;AAAA,YACF;AAAA,UACF,EAAE,OAAO,YAAY,aAAa;AAAA,UAClC,WAAW;AAAA,QACb;AAAA,MACF,EAAE,OAAO,YAAY,aAAa;AAElC,YAAM,WAAW;AACjB,aAAO,WAAW;AAIlB,YAAM,gBAAgB;AAEtB,YAAM,iBAAiB;AACvB,YAAM,aAAa;AAEnB,YAAM,cAAc;AAAA,QAClB;AAAA,UACE,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,KAAK;AAAA,YACL,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,OAAO,gBAAgB,MAAM,iBAAiB,MAAM,aAAa;AAAA,UACxE,QAAQ;AAAA,YACN,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAEA,oBAAc,QAAQ,UAAU;AAEhC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU,CAAE,KAAK,QAAQ,EAAE,QAAQ,OAAO,CAAC,CAAE,EAC1C,OAAO,WAAW,EAClB,OAAO,aAAa,EACpB,OAAO,qBAAqB;AAAA,MACjC;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/bjB;AAAA;AASA,aAAS,GAAG,MAAM;AAChB,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,QAAQ;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,MAAM;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf,SAAS;AAAA,QACT,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,QAAS;AAAA,QACpB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR,KAAK;AAAA,cACL,KAAK;AAAA,cACL;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO,KAAK,cAAc;AAAA,gBAC1B,WAAW;AAAA,cACb;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,YAAE,OAAO;AAAA;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,YAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,SAAS;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5IjB;AAAA;AAQA,aAAS,QAAQ,MAAM;AACrB,YAAM,QAAQ,KAAK;AACnB,YAAM,WAAW;AACjB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,KAAM;AAAA,QACjB,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,UAAU;AAAA,UACR,SAAS;AAAA,YACP;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,UACA,SAAS;AAAA,YACP;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,OAAO;AAAA,YACP,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,OAAO;AAAA,YACP,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,OAAO;AAAA,YACP,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,OAAO,MAAM,OAAO,UAAU,MAAM,UAAU,MAAM,CAAC;AAAA,YACrD,WAAW;AAAA,UACb;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7EjB;AAAA;AAQA,aAAS,IAAI,MAAM;AACjB,YAAM,QAAQ,KAAK;AACnB,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,uBAAuB;AAAA,UAChC,EAAE,OAAO,KAAK,UAAU;AAAA,QAC1B;AAAA,MACF;AACA,YAAM,WAAW,KAAK,QAAQ;AAC9B,eAAS,WAAW;AAAA,QAClB;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,QACP;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,QACP;AAAA,MACF;AACA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,oBAAoB;AAAA,UAC7B,EAAE,OAAO,cAAc;AAAA,QACzB;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU,CAAE,KAAK,gBAAiB;AAAA,QAClC,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AACA,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,WAAW;AAAA,MACb;AAEA,YAAM,WAAW;AACjB,YAAM,0BAA0B;AAChC,YAAM,0BAA0B;AAChC,YAAM,UAAU,MAAM;AAAA,QACpB;AAAA,QAAU;AAAA,QAAyB;AAAA,MACrC;AACA,YAAM,aAAa,MAAM;AAAA,QACvB;AAAA,QAAS;AAAA,QAAgB;AAAA,QAAS;AAAA,QAClC,MAAM,UAAU,eAAe;AAAA,MACjC;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,MAAO;AAAA,QAClB,kBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,YACX,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,UAAU;AAAA,gBACR;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACxHjB;AAAA;AACA,QAAI,gBAAgB;AACpB,QAAI,OAAO,OAAO,aAAa;AAC/B,QAAI,YAAY;AAChB,QAAI,UAAU;AAAA,MACZ,WAAW;AAAA,MACX,UAAU;AAAA;AAAA;AAAA,QAGR,EAAE,OAAO,QAAQ,aAAa,MAAM,IAAI,YAAY,IAAI,eACzC,aAAa,cAAc;AAAA;AAAA,QAE1C,EAAE,OAAO,OAAO,aAAa,MAAM,IAAI,+BAA+B;AAAA,QACtE,EAAE,OAAO,IAAI,IAAI,cAAc;AAAA,QAC/B,EAAE,OAAO,OAAO,aAAa,aAAa;AAAA;AAAA,QAG1C,EAAE,OAAO,aAAa,SAAS,UAAU,SAAS,SAAS,SAAS,eACrD,aAAa,cAAc;AAAA;AAAA,QAG1C,EAAE,OAAO,iCAAiC;AAAA;AAAA,QAG1C,EAAE,OAAO,YAAY,SAAS,YAAY;AAAA;AAAA,QAG1C,EAAE,OAAO,yBAAyB;AAAA;AAAA,QAGlC,EAAE,OAAO,gCAAgC;AAAA,MAC3C;AAAA,MACA,WAAW;AAAA,IACb;AAqBA,aAAS,WAAW,IAAI,cAAc,OAAO;AAC3C,UAAI,UAAU,GAAI,QAAO;AAEzB,aAAO,GAAG,QAAQ,cAAc,OAAK;AACnC,eAAO,WAAW,IAAI,cAAc,QAAQ,CAAC;AAAA,MAC/C,CAAC;AAAA,IACH;AAGA,aAAS,KAAK,MAAM;AAClB,YAAM,QAAQ,KAAK;AACnB,YAAM,gBAAgB;AACtB,YAAM,mBAAmB,gBACrB,WAAW,SAAS,gBAAgB,oBAAoB,gBAAgB,YAAY,QAAQ,CAAC;AACjG,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,QAAQ;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf,SAAS;AAAA,QACT,SAAS;AAAA,QACT,MAAM;AAAA,QACN,UAAU;AAAA,MACZ;AAEA,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,OAAO,MAAM;AAAA,QACb,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAE,MAAO;AAAA;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU,CAAE,KAAK,oBAAqB;AAAA,QACtC,YAAY;AAAA,MACd;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,KAAM;AAAA,QACjB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,UAAU;AAAA,gBACR;AAAA;AAAA,kBAEE,OAAO;AAAA,kBACP,WAAW;AAAA,gBACb;AAAA,gBACA;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,UACb;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,YACX,UAAU,CAAE,KAAK,gBAAiB;AAAA,UACpC;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,YACA,WAAW;AAAA,cACT,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,YACP,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,MAAM,OAAO,YAAY,aAAa;AAAA,cACtC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,YACA,WAAW;AAAA,cACT,GAAG;AAAA,cACH,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,YACA,WAAW;AAAA,cACT,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,YACA,UAAU;AAAA,cACR;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA;AAAA;AAAA,YAGE,eAAe;AAAA,YACf,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,QAAQ,mBAAmB;AAAA,cAC3B,KAAK;AAAA,cACL;AAAA,YACF;AAAA,YACA,WAAW,EAAE,GAAG,iBAAiB;AAAA,YACjC,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,kBACA,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL;AAAA,kBACA,KAAK;AAAA,gBACP;AAAA,cACF;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChSjB;AAAA;AAAA,QAAM,WAAW;AACjB,QAAM,WAAW;AAAA,MACf;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,MAIA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,QAAQ;AAAA;AAAA,MAEZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA,IACF;AAEA,QAAM,cAAc;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA;AAAA,MACA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,qBAAqB;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,IACF;AAEA,QAAM,YAAY,CAAC,EAAE;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAWA,aAAS,WAAW,MAAM;AACxB,YAAM,QAAQ,KAAK;AAQnB,YAAM,gBAAgB,CAAC,OAAO,EAAE,MAAM,MAAM;AAC1C,cAAM,MAAM,OAAO,MAAM,CAAC,EAAE,MAAM,CAAC;AACnC,cAAM,MAAM,MAAM,MAAM,QAAQ,KAAK,KAAK;AAC1C,eAAO,QAAQ;AAAA,MACjB;AAEA,YAAM,aAAa;AACnB,YAAM,WAAW;AAAA,QACf,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AAEA,YAAM,mBAAmB;AACzB,YAAM,UAAU;AAAA,QACd,OAAO;AAAA,QACP,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,QAKL,mBAAmB,CAAC,OAAO,aAAa;AACtC,gBAAM,kBAAkB,MAAM,CAAC,EAAE,SAAS,MAAM;AAChD,gBAAM,WAAW,MAAM,MAAM,eAAe;AAC5C;AAAA;AAAA;AAAA;AAAA,YAIE,aAAa;AAAA;AAAA,YAGb,aAAa;AAAA,YACX;AACF,qBAAS,YAAY;AACrB;AAAA,UACF;AAIA,cAAI,aAAa,KAAK;AAGpB,gBAAI,CAAC,cAAc,OAAO,EAAE,OAAO,gBAAgB,CAAC,GAAG;AACrD,uBAAS,YAAY;AAAA,YACvB;AAAA,UACF;AAKA,cAAI;AACJ,gBAAM,aAAa,MAAM,MAAM,UAAU,eAAe;AAIxD,cAAK,IAAI,WAAW,MAAM,OAAO,GAAI;AACnC,qBAAS,YAAY;AACrB;AAAA,UACF;AAKA,cAAK,IAAI,WAAW,MAAM,gBAAgB,GAAI;AAC5C,gBAAI,EAAE,UAAU,GAAG;AACjB,uBAAS,YAAY;AAErB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,aAAa;AAAA,QACjB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU;AAAA,QACV,qBAAqB;AAAA,MACvB;AAGA,YAAM,gBAAgB;AACtB,YAAM,OAAO,OAAO,aAAa;AAGjC,YAAM,iBAAiB;AACvB,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA;AAAA,UAER,EAAE,OAAO,QAAQ,cAAc,MAAM,IAAI,YAAY,IAAI,eAC1C,aAAa,OAAO;AAAA,UACnC,EAAE,OAAO,OAAO,cAAc,SAAS,IAAI,eAAe,IAAI,OAAO;AAAA;AAAA,UAGrE,EAAE,OAAO,6BAA6B;AAAA;AAAA,UAGtC,EAAE,OAAO,2CAA2C;AAAA,UACpD,EAAE,OAAO,+BAA+B;AAAA,UACxC,EAAE,OAAO,+BAA+B;AAAA;AAAA;AAAA,UAIxC,EAAE,OAAO,kBAAkB;AAAA,QAC7B;AAAA,QACA,WAAW;AAAA,MACb;AAEA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU,CAAC;AAAA;AAAA,MACb;AACA,YAAM,gBAAgB;AAAA,QACpB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,QAAQ;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,UAAU;AAAA,YACR,KAAK;AAAA,YACL;AAAA,UACF;AAAA,UACA,aAAa;AAAA,QACf;AAAA,MACF;AACA,YAAM,eAAe;AAAA,QACnB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,QAAQ;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,UAAU;AAAA,YACR,KAAK;AAAA,YACL;AAAA,UACF;AAAA,UACA,aAAa;AAAA,QACf;AAAA,MACF;AACA,YAAM,mBAAmB;AAAA,QACvB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,QAAQ;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,UAAU;AAAA,YACR,KAAK;AAAA,YACL;AAAA,UACF;AAAA,UACA,aAAa;AAAA,QACf;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,YAAM,gBAAgB,KAAK;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,WAAW;AAAA,cACX,UAAU;AAAA,gBACR;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,gBACT;AAAA,gBACA;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,kBACP,KAAK;AAAA,kBACL,YAAY;AAAA,kBACZ,cAAc;AAAA,kBACd,WAAW;AAAA,gBACb;AAAA,gBACA;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO,aAAa;AAAA,kBACpB,YAAY;AAAA,kBACZ,WAAW;AAAA,gBACb;AAAA;AAAA;AAAA,gBAGA;AAAA,kBACE,OAAO;AAAA,kBACP,WAAW;AAAA,gBACb;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA,EAAE,OAAO,QAAQ;AAAA,QACjB;AAAA;AAAA;AAAA;AAAA,MAIF;AACA,YAAM,WAAW,gBACd,OAAO;AAAA;AAAA;AAAA,QAGN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,QACF,EAAE,OAAO,eAAe;AAAA,MAC1B,CAAC;AACH,YAAM,qBAAqB,CAAC,EAAE,OAAO,SAAS,MAAM,QAAQ;AAC5D,YAAM,kBAAkB,mBAAmB,OAAO;AAAA;AAAA,QAEhD;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU;AAAA,UACV,UAAU,CAAC,MAAM,EAAE,OAAO,kBAAkB;AAAA,QAC9C;AAAA,MACF,CAAC;AACD,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAGA,YAAM,mBAAmB;AAAA,QACvB,UAAU;AAAA;AAAA,UAER;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,MAAM,OAAO,YAAY,KAAK,MAAM,OAAO,MAAM,UAAU,GAAG,IAAI;AAAA,YACpE;AAAA,YACA,OAAO;AAAA,cACL,GAAG;AAAA,cACH,GAAG;AAAA,cACH,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,YACA,OAAO;AAAA,cACL,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA,QAEF;AAAA,MACF;AAEA,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,OACA,MAAM;AAAA;AAAA,UAEJ;AAAA;AAAA,UAEA;AAAA;AAAA,UAEA;AAAA;AAAA,UAEA;AAAA;AAAA;AAAA;AAAA;AAAA,QAKF;AAAA,QACA,WAAW;AAAA,QACX,UAAU;AAAA,UACR,GAAG;AAAA;AAAA,YAED,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAEA,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,sBAAsB;AAAA,QAC1B,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,QACA,OAAO;AAAA,QACP,UAAU,CAAE,MAAO;AAAA,QACnB,SAAS;AAAA,MACX;AAEA,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAEA,eAAS,OAAO,MAAM;AACpB,eAAO,MAAM,OAAO,OAAO,KAAK,KAAK,GAAG,GAAG,GAAG;AAAA,MAChD;AAEA,YAAM,gBAAgB;AAAA,QACpB,OAAO,MAAM;AAAA,UACX;AAAA,UACA,OAAO;AAAA,YACL,GAAG;AAAA,YACH;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD;AAAA,UAAY,MAAM,UAAU,IAAI;AAAA,QAAC;AAAA,QACnC,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAEA,YAAM,kBAAkB;AAAA,QACtB,OAAO,MAAM,OAAO,MAAM,MAAM;AAAA,UAC9B,MAAM,OAAO,YAAY,oBAAoB;AAAA,QAC/C,CAAC;AAAA,QACD,KAAK;AAAA,QACL,cAAc;AAAA,QACd,UAAU;AAAA,QACV,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAEA,YAAM,mBAAmB;AAAA,QACvB,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,QACA,UAAU;AAAA,UACR;AAAA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,kBAAkB,6DAMb,KAAK,sBAAsB;AAEtC,YAAM,oBAAoB;AAAA,QACxB,OAAO;AAAA,UACL;AAAA,UAAiB;AAAA,UACjB;AAAA,UAAY;AAAA,UACZ;AAAA,UACA;AAAA;AAAA,UACA,MAAM,UAAU,eAAe;AAAA,QACjC;AAAA,QACA,UAAU;AAAA,QACV,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,QACA,UAAU;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,MAAM,OAAO,OAAO,KAAK;AAAA,QACnC,UAAU;AAAA;AAAA,QAEV,SAAS,EAAE,iBAAiB,gBAAgB;AAAA,QAC5C,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK,QAAQ;AAAA,YACX,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,WAAW;AAAA,UACb,CAAC;AAAA,UACD;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA,UAEA,EAAE,OAAO,QAAQ;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,aAAa,MAAM,UAAU,GAAG;AAAA,YACvC,WAAW;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA;AAAA,YACE,OAAO,MAAM,KAAK,iBAAiB;AAAA,YACnC,UAAU;AAAA,YACV,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,cACA,KAAK;AAAA,cACL;AAAA,gBACE,WAAW;AAAA;AAAA;AAAA;AAAA,gBAIX,OAAO;AAAA,gBACP,aAAa;AAAA,gBACb,KAAK;AAAA,gBACL,UAAU;AAAA,kBACR;AAAA,oBACE,WAAW;AAAA,oBACX,UAAU;AAAA,sBACR;AAAA,wBACE,OAAO,KAAK;AAAA,wBACZ,WAAW;AAAA,sBACb;AAAA,sBACA;AAAA,wBACE,WAAW;AAAA,wBACX,OAAO;AAAA,wBACP,MAAM;AAAA,sBACR;AAAA,sBACA;AAAA,wBACE,OAAO;AAAA,wBACP,KAAK;AAAA,wBACL,cAAc;AAAA,wBACd,YAAY;AAAA,wBACZ,UAAU;AAAA,wBACV,UAAU;AAAA,sBACZ;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA;AAAA;AAAA,gBACE,UAAU;AAAA,kBACR,EAAE,OAAO,SAAS,OAAO,KAAK,SAAS,IAAI;AAAA,kBAC3C,EAAE,OAAO,iBAAiB;AAAA,kBAC1B;AAAA,oBACE,OAAO,QAAQ;AAAA;AAAA;AAAA,oBAGf,YAAY,QAAQ;AAAA,oBACpB,KAAK,QAAQ;AAAA,kBACf;AAAA,gBACF;AAAA,gBACA,aAAa;AAAA,gBACb,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO,QAAQ;AAAA,oBACf,KAAK,QAAQ;AAAA,oBACb,MAAM;AAAA,oBACN,UAAU,CAAC,MAAM;AAAA,kBACnB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA;AAAA;AAAA,YAGE,eAAe;AAAA,UACjB;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,YAIE,OAAO,oBAAoB,KAAK,sBAC9B;AAAA;AAAA,YAOF,aAAY;AAAA,YACZ,OAAO;AAAA,YACP,UAAU;AAAA,cACR;AAAA,cACA,KAAK,QAAQ,KAAK,YAAY,EAAE,OAAO,YAAY,WAAW,iBAAiB,CAAC;AAAA,YAClF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UAIA;AAAA,YACE,OAAO,QAAQ;AAAA,YACf,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO,CAAE,wBAAyB;AAAA,YAClC,WAAW,EAAE,GAAG,iBAAiB;AAAA,YACjC,UAAU,CAAE,MAAO;AAAA,UACrB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,OAAO;AAAA;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7vBjB;AAAA;AAQA,aAAS,KAAK,MAAM;AAClB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,YAAM,cAAc;AAAA,QAClB,OAAO;AAAA,QACP,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AACA,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAMA,YAAM,gBAAgB;AAAA,QACpB,OAAO;AAAA,QACP,eAAe,SAAS,KAAK,GAAG;AAAA,MAClC;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAS;AAAA,UACP,SAAS;AAAA,QACX;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpDjB;AAAA;AACA,QAAI,gBAAgB;AACpB,QAAI,OAAO,OAAO,aAAa;AAC/B,QAAI,YAAY;AAChB,QAAI,UAAU;AAAA,MACZ,WAAW;AAAA,MACX,UAAU;AAAA;AAAA;AAAA,QAGR,EAAE,OAAO,QAAQ,aAAa,MAAM,IAAI,YAAY,IAAI,eACzC,aAAa,cAAc;AAAA;AAAA,QAE1C,EAAE,OAAO,OAAO,aAAa,MAAM,IAAI,+BAA+B;AAAA,QACtE,EAAE,OAAO,IAAI,IAAI,cAAc;AAAA,QAC/B,EAAE,OAAO,OAAO,aAAa,aAAa;AAAA;AAAA,QAG1C,EAAE,OAAO,aAAa,SAAS,UAAU,SAAS,SAAS,SAAS,eACrD,aAAa,cAAc;AAAA;AAAA,QAG1C,EAAE,OAAO,iCAAiC;AAAA;AAAA,QAG1C,EAAE,OAAO,YAAY,SAAS,YAAY;AAAA;AAAA,QAG1C,EAAE,OAAO,yBAAyB;AAAA;AAAA,QAGlC,EAAE,OAAO,gCAAgC;AAAA,MAC3C;AAAA,MACA,WAAW;AAAA,IACb;AAWA,aAAS,OAAO,MAAM;AACpB,YAAM,WAAW;AAAA,QACf,SACE;AAAA,QAKF,UACE;AAAA,QACF,SACE;AAAA,MACJ;AACA,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,QAAQ,EAAE,UAAU;AAAA,UAClB;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,QACF,EAAE;AAAA,MACJ;AACA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO,KAAK,sBAAsB;AAAA,MACpC;AAGA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,KAAK,aAAc;AAAA,MACjC;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO,QAAQ,KAAK;AAAA,MACtB;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAIA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU,CAAE,KAAK,gBAAiB;AAAA,UACpC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS,KAAK,MAAM;AAE1B,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO,kFAAkF,KAAK,sBAAsB;AAAA,MACtH;AACA,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,OAAO,MAAM,KAAK;AAAA,QAClB,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK,QAAQ,QAAQ,EAAE,WAAW,SAAS,CAAC;AAAA,cAC5C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAKA,YAAM,qBAAqB;AAC3B,YAAM,wBAAwB,KAAK;AAAA,QACjC;AAAA,QAAQ;AAAA,QACR,EAAE,UAAU,CAAE,KAAK,oBAAqB,EAAE;AAAA,MAC5C;AACA,YAAM,oBAAoB,EAAE,UAAU;AAAA,QACpC;AAAA,UACE,WAAW;AAAA,UACX,OAAO,KAAK;AAAA,QACd;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU,CAAC;AAAA;AAAA,QACb;AAAA,MACF,EAAE;AACF,YAAM,qBAAqB;AAC3B,yBAAmB,SAAS,CAAC,EAAE,WAAW,CAAE,iBAAkB;AAC9D,wBAAkB,SAAS,CAAC,EAAE,WAAW,CAAE,kBAAmB;AAE9D,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA,UACR,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,UAAU;AAAA,gBACR;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO,KAAK,sBAAsB;AAAA,gBAClC,aAAa;AAAA,gBACb,WAAW;AAAA,gBACX,UAAU,CAAE,KAAK,qBAAsB;AAAA,cACzC;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,gBACV,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,YAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,oBACL,gBAAgB;AAAA,oBAChB,UAAU;AAAA,sBACR;AAAA,sBACA,KAAK;AAAA,sBACL;AAAA,oBACF;AAAA,oBACA,WAAW;AAAA,kBACb;AAAA,kBACA,KAAK;AAAA,kBACL;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,KAAK;AAAA,gBACP;AAAA,cACF;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA,KAAK;AAAA,YACP;AAAA,YACA,YAAY;AAAA,cACV,GAAG;AAAA,YACL;AAAA,YACA,UAAU;AAAA,YACV,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,UAAU;AAAA,cACR,EAAE,eAAe,gDAAgD;AAAA,cACjE,KAAK;AAAA,cACL;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,cAAc;AAAA,gBACd,WAAW;AAAA,cACb;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,UACX;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7RjB;AAAA;AAAA,QAAM,QAAQ,CAAC,SAAS;AACtB,aAAO;AAAA,QACL,WAAW;AAAA,UACT,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,QACA,eAAe,KAAK;AAAA,QACpB,UAAU;AAAA,UACR,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA,yBAAyB;AAAA,UACvB,OAAO;AAAA,UACP,OAAO;AAAA,UACP,KAAK;AAAA,UACL,SAAS;AAAA,UACT,UAAU;AAAA,YACR,KAAK;AAAA,YACL,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,iBAAiB;AAAA,UACf,OAAO;AAAA,UACP,OAAO,KAAK,YAAY;AAAA,UASxB,WAAW;AAAA,QACb;AAAA,QACA,cAAc;AAAA,UACZ,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAEA,QAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,iBAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,iBAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,IACF;AAGA,QAAM,kBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA,IAGF,EAAE,QAAQ;AAGV,QAAM,mBAAmB,eAAe,OAAO,eAAe;AAY9D,aAAS,KAAK,MAAM;AAClB,YAAM,QAAQ,MAAM,IAAI;AACxB,YAAM,qBAAqB;AAE3B,YAAM,eAAe;AACrB,YAAM,WAAW;AACjB,YAAM,kBAAkB,MAAM,WAAW,UAAU,WAAW;AAI9D,YAAM,QAAQ,CAAC;AAAG,YAAM,cAAc,CAAC;AAEvC,YAAM,cAAc,SAAS,GAAG;AAC9B,eAAO;AAAA;AAAA,UAEL,WAAW;AAAA,UACX,OAAO,OAAO,IAAI,QAAQ;AAAA,QAC5B;AAAA,MACF;AAEA,YAAM,aAAa,SAAS,MAAM,OAAO,WAAW;AAClD,eAAO;AAAA,UACL,WAAW;AAAA,UACX;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc;AAAA,QAClB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,WAAW,eAAe,KAAK,GAAG;AAAA,MACpC;AAEA,YAAM,cAAc;AAAA;AAAA,QAElB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AAGA,kBAAY;AAAA,QACV,KAAK;AAAA,QACL,KAAK;AAAA,QACL,YAAY,GAAG;AAAA,QACf,YAAY,GAAG;AAAA,QACf,MAAM;AAAA;AAAA,QACN;AAAA,UACE,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,WAAW;AAAA,YACX,KAAK;AAAA,YACL,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,MAAM;AAAA,QACN;AAAA,QACA,WAAW,YAAY,QAAQ,UAAU,EAAE;AAAA,QAC3C,WAAW,YAAY,SAAS,WAAW,KAAK;AAAA,QAChD,WAAW,YAAY,YAAY;AAAA;AAAA,QACnC;AAAA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,WAAW;AAAA,UAClB,KAAK;AAAA,UACL,aAAa;AAAA,UACb,YAAY;AAAA,QACd;AAAA,QACA,MAAM;AAAA,QACN,EAAE,eAAe,UAAU;AAAA,QAC3B,MAAM;AAAA,MACR;AAEA,YAAM,sBAAsB,YAAY,OAAO;AAAA,QAC7C,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ,CAAC;AAED,YAAM,mBAAmB;AAAA,QACvB,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,UAAU,CAAE,EAAE,eAAe,UAAU,CAAE,EAAE,OAAO,WAAW;AAAA;AAAA,MAC/D;AAIA,YAAM,YAAY;AAAA,QAChB,OAAO,kBAAkB;AAAA,QACzB,aAAa;AAAA,QACb,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,sBAAsB;AAAA,UAC/B,MAAM;AAAA,UACN;AAAA,YACE,WAAW;AAAA,YACX,OAAO,SAAS,WAAW,KAAK,GAAG,IAAI;AAAA,YACvC,KAAK;AAAA,YACL,QAAQ;AAAA,cACN,gBAAgB;AAAA,cAChB,SAAS;AAAA,cACT,WAAW;AAAA,cACX,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,KAAK;AAAA,UACL,UAAU;AAAA,UACV,WAAW;AAAA,UACX,UAAU;AAAA,UACV,WAAW;AAAA,QACb;AAAA,MACF;AAGA,YAAM,gBAAgB;AAAA,QACpB,WAAW;AAAA,QACX,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,UAKR;AAAA,YACE,OAAO,MAAM,WAAW;AAAA,YACxB,WAAW;AAAA,UACb;AAAA,UACA,EAAE,OAAO,MAAM,SAAS;AAAA,QAC1B;AAAA,QACA,QAAQ;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,UAAU;AAAA,QACZ;AAAA,MACF;AAEA,YAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA,QAIpB,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAS;AAAA,QACT,WAAW;AAAA,QACX,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA,WAAW,WAAW,QAAQ;AAAA,UAC9B,WAAW,YAAY,SAAS,WAAW,KAAK;AAAA;AAAA,UAEhD;AAAA,YACE,OAAO,SAAS,KAAK,KAAK,GAAG,IAAI;AAAA,YACjC,WAAW;AAAA,UACb;AAAA,UACA,MAAM;AAAA,UACN,WAAW,gBAAgB,iBAAiB,CAAC;AAAA,UAC7C,WAAW,eAAe,MAAM,eAAe;AAAA,UAC/C,WAAW,kBAAkB,QAAQ,iBAAiB,CAAC;AAAA,UACvD,WAAW,gBAAgB,KAAK,CAAC;AAAA,UACjC,MAAM;AAAA,UACN;AAAA,YACE,WAAW;AAAA,YACX,OAAO,OAAO,eAAe,KAAK,GAAG,IAAI;AAAA,UAC3C;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,WAAW,gBAAgB,KAAK,GAAG,IAAI;AAAA,UAChD;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,YACX,UAAU;AAAA,UACZ;AAAA;AAAA,UACA,EAAE,OAAO,aAAa;AAAA;AAAA,UACtB,MAAM;AAAA,QACR;AAAA,MACF;AAEA,YAAM,uBAAuB;AAAA,QAC3B,OAAO,WAAW,SAAc,mBAAmB,KAAK,GAAG,CAAC;AAAA,QAC5D,aAAa;AAAA,QACb,UAAU,CAAE,aAAc;AAAA,MAC5B;AAEA,YAAM;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACt0BjB;AAAA;AAQA,aAAS,IAAI,MAAM;AACjB,YAAM,uBAAuB;AAC7B,YAAM,uBAAuB;AAC7B,YAAM,gBAAgB;AAAA,QACpB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,MAAO;AAAA,MACrB;AACA,YAAM,WAAW;AAAA,QACf,KAAK,QAAQ,UAAU,uBAAuB,KAAK,GAAG;AAAA,QACtD,KAAK;AAAA,UACH,OAAO;AAAA,UACP;AAAA,UACA;AAAA,YACE,UAAU,CAAE,aAAc;AAAA,YAC1B,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR,UAAU,KAAK;AAAA,UACf,SAAS;AAAA,UACT,SAAS;AAAA,UACT;AAAA;AAAA,YAEE;AAAA;AAAA,QAcJ;AAAA,QACA,UAAU,SAAS,OAAO;AAAA,UACxB;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK,QAAQ,KAAK,YAAY,EAAE,OAAO,oDAAoD,CAAC;AAAA,cAC5F;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,gBAAgB;AAAA,gBAChB,UAAU;AAAA,cACZ;AAAA,YACF,EAAE,OAAO,QAAQ;AAAA,UACnB;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAE,aAAc;AAAA,YAC1B,WAAW;AAAA,UACb;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/EjB;AAAA;AAQA,aAAS,SAAS,MAAM;AAEtB,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO,WAAW,KAAK,sBAAsB;AAAA,YAC7C,UAAU,CAAE,KAAK,gBAAiB;AAAA,UACpC;AAAA,UACA,EAAE,OAAO,iBAAiB;AAAA,QAC5B;AAAA,MACF;AAEA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAEA,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,EAAE,UACR,iPAG+D;AAAA,QACnE,UAAU,CAAE,QAAS;AAAA,MACvB;AAEA,YAAM,aAAa,EAAE,OAAO,MAAM,KAAK,sBAAsB,kBAAkB;AAE/E,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SAAS;AAAA,QACX;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,QAAS;AAAA,MACvB;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SAAS;AAAA,QAEX;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrFjB;AAAA;AAQA,aAAS,KAAK,MAAM;AAClB,YAAM,QAAQ,KAAK;AACnB,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,kBAAkB;AACxB,YAAM,gBAAgB;AAAA,QACpB,UAAU;AAAA,QACV,SAAS,SAAS,KAAK,GAAG;AAAA,MAC5B;AACA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,KAAK;AAAA;AAAA,MAEP;AACA,YAAM,MAAM,EAAE,UAAU;AAAA,QACtB,EAAE,OAAO,OAAO;AAAA,QAChB,EAAE,OAAO,MAAM;AAAA,UACb;AAAA;AAAA;AAAA,UAGA;AAAA,QACF,EAAE;AAAA,QACF;AAAA,UACE,OAAO;AAAA,UACP,WAAW;AAAA,QACb;AAAA,MACF,EAAE;AACF,YAAM,kBAAkB;AAAA,QACtB,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AACA,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAMA,YAAM,mBAAmB,CAAC,QAAQ,MAAM,QAAQ,UAAU;AACxD,cAAM,SAAU,UAAU,QACtB,QACA,MAAM,OAAO,OAAO,IAAI;AAC5B,eAAO,MAAM;AAAA,UACX,MAAM,OAAO,OAAO,QAAQ,GAAG;AAAA,UAC/B;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAMA,YAAM,YAAY,CAAC,QAAQ,MAAM,UAAU;AACzC,eAAO,MAAM;AAAA,UACX,MAAM,OAAO,OAAO,QAAQ,GAAG;AAAA,UAC/B;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,wBAAwB;AAAA,QAC5B;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA,EAAE,gBAAgB,KAAK;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,UACV,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU,CAAE,KAAK,gBAAiB;AAAA,YACpC;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,YACP;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU,CAAE,KAAK,gBAAiB;AAAA,YACpC;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,WAAW;AAAA,QACb;AAAA,QACA;AAAA;AAAA,UACE,OAAO,aAAa,KAAK,iBAAiB;AAAA,UAC1C,UAAU;AAAA,UACV,WAAW;AAAA,UACX,UAAU;AAAA,YACR,KAAK;AAAA,YACL;AAAA,cACE,WAAW;AAAA,cACX,UAAU;AAAA;AAAA,gBAER,EAAE,OAAO,iBAAiB,UAAU,MAAM,OAAO,GAAG,cAAc,EAAE,SAAS,KAAK,CAAC,CAAC,EAAE;AAAA;AAAA,gBAEtF,EAAE,OAAO,iBAAiB,UAAU,OAAO,KAAK,EAAE;AAAA,gBAClD,EAAE,OAAO,iBAAiB,UAAU,OAAO,KAAK,EAAE;AAAA,gBAClD,EAAE,OAAO,iBAAiB,UAAU,OAAO,KAAK,EAAE;AAAA,cACpD;AAAA,cACA,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,UAAU;AAAA,gBACR;AAAA;AAAA;AAAA,kBAGE,OAAO;AAAA,kBACP,WAAW;AAAA,gBACb;AAAA;AAAA,gBAEA,EAAE,OAAO,UAAU,aAAa,MAAM,IAAI,EAAE;AAAA;AAAA,gBAE5C,EAAE,OAAO,UAAU,QAAQ,MAAM,OAAO,GAAG,cAAc,EAAE,SAAS,KAAK,CAAC,GAAG,IAAI,EAAE;AAAA;AAAA,gBAEnF,EAAE,OAAO,UAAU,QAAQ,MAAM,IAAI,EAAE;AAAA,gBACvC,EAAE,OAAO,UAAU,QAAQ,MAAM,IAAI,EAAE;AAAA,gBACvC,EAAE,OAAO,UAAU,QAAQ,MAAM,IAAI,EAAE;AAAA,cACzC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,eAAe;AAAA,UACf,KAAK;AAAA,UACL,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,UAAU,CAAE,KAAK,UAAW;AAAA,QAC9B;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,WAAW;AAAA,QACb;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW;AACjB,aAAO,WAAW;AAElB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtdjB;AAAA;AAQA,aAAS,WAAW,MAAM;AACxB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,gBAAgB;AACtB,YAAM,QAAQ;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,MAAM;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf,qBAAqB;AAAA,UACnB;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU;AAAA,QACV,MAAM;AAAA,MACR;AACA,YAAM,iBAAiB;AAAA,QACrB,UAAU;AAAA,QACV,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,SAAS;AAAA,gBACT,UAAU,CAAE,KAAK,gBAAiB;AAAA,cACpC;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,EAAE,SACR,iFACgC;AAAA,YACpC,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA,KAAK,QAAQ,KAAK,mBAAmB,EAAE,WAAW,SAAS,CAAC;AAAA,cAC5D;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,SAAS;AAAA,cACX;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,MAAM,eAAe,QAAQ,KAAK,GAAG,IAAI;AAAA,YAChD,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,UAAU,CAAE,KAAK,qBAAsB;AAAA,UACzC;AAAA,UACA;AAAA,YACE,OAAO,QAAQ,KAAK;AAAA,YACpB,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5PjB;AAAA;AAYA,aAAS,IAAI,MAAM;AACjB,YAAM,QAAQ,KAAK;AAGnB,YAAM,eAAe;AACrB,YAAM,WAAW,MAAM;AAAA,QACrB;AAAA,QACA;AAAA,MAAY;AAEd,YAAM,4BAA4B,MAAM;AAAA,QACtC;AAAA,QACA;AAAA,MAAY;AACd,YAAM,WAAW;AAAA,QACf,OAAO;AAAA,QACP,OAAO,SAAS;AAAA,MAClB;AACA,YAAM,eAAe;AAAA,QACnB,OAAO;AAAA,QACP,UAAU;AAAA,UACR,EAAE,OAAO,UAAU,WAAW,GAAG;AAAA;AAAA,UACjC,EAAE,OAAO,OAAO;AAAA;AAAA,UAEhB,EAAE,OAAO,OAAO,WAAW,IAAI;AAAA,UAC/B,EAAE,OAAO,MAAM;AAAA;AAAA,QACjB;AAAA,MACF;AACA,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,UACR,EAAE,OAAO,QAAQ;AAAA,UACjB;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AACA,YAAM,gBAAgB,KAAK,QAAQ,KAAK,kBAAkB,EAAE,SAAS,KAAM,CAAC;AAC5E,YAAM,gBAAgB,KAAK,QAAQ,KAAK,mBAAmB;AAAA,QACzD,SAAS;AAAA,QACT,UAAU,KAAK,kBAAkB,SAAS,OAAO,KAAK;AAAA,MACxD,CAAC;AAED,YAAM,UAAU;AAAA,QACd,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,KAAK,kBAAkB,SAAS,OAAO,KAAK;AAAA,QACtD,YAAY,CAAC,GAAG,SAAS;AAAE,eAAK,KAAK,cAAc,EAAE,CAAC,KAAK,EAAE,CAAC;AAAA,QAAG;AAAA,QACjE,UAAU,CAAC,GAAG,SAAS;AAAE,cAAI,KAAK,KAAK,gBAAgB,EAAE,CAAC,EAAG,MAAK,YAAY;AAAA,QAAG;AAAA,MACnF;AAEA,YAAM,SAAS,KAAK,kBAAkB;AAAA,QACpC,OAAO;AAAA,QACP,KAAK;AAAA,MACP,CAAC;AAED,YAAM,aAAa;AACnB,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,UAAU;AAAA,UACR,EAAE,OAAO,8BAA8B;AAAA;AAAA,UACvC,EAAE,OAAO,gCAAgC;AAAA;AAAA,UACzC,EAAE,OAAO,4CAA4C;AAAA;AAAA;AAAA,UAErD,EAAE,OAAO,6EAA6E;AAAA,QACxF;AAAA,QACA,WAAW;AAAA,MACb;AACA,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,MAAM;AAAA;AAAA;AAAA,QAGV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA;AAAA,QAGA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,YAAY;AAAA;AAAA;AAAA,QAGhB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA;AAAA,QAGA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA;AAAA,QAGA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAQA,YAAM,WAAW,CAAC,UAAU;AAE1B,cAAM,SAAS,CAAC;AAChB,cAAM,QAAQ,UAAQ;AACpB,iBAAO,KAAK,IAAI;AAChB,cAAI,KAAK,YAAY,MAAM,MAAM;AAC/B,mBAAO,KAAK,KAAK,YAAY,CAAC;AAAA,UAChC,OAAO;AACL,mBAAO,KAAK,KAAK,YAAY,CAAC;AAAA,UAChC;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT;AAEA,YAAM,WAAW;AAAA,QACf,SAAS;AAAA,QACT,SAAS,SAAS,QAAQ;AAAA,QAC1B,UAAU;AAAA,MACZ;AAIA,YAAM,oBAAoB,CAAC,UAAU;AACnC,eAAO,MAAM,IAAI,UAAQ;AACvB,iBAAO,KAAK,QAAQ,UAAU,EAAE;AAAA,QAClC,CAAC;AAAA,MACH;AAEA,YAAM,mBAAmB,EAAE,UAAU;AAAA,QACnC;AAAA,UACE,OAAO;AAAA,YACL;AAAA,YACA,MAAM,OAAO,YAAY,GAAG;AAAA;AAAA,YAE5B,MAAM,OAAO,OAAO,kBAAkB,SAAS,EAAE,KAAK,MAAM,GAAG,MAAM;AAAA,YACrE;AAAA,UACF;AAAA,UACA,OAAO;AAAA,YACL,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AAAA,QACF;AAAA,MACF,EAAE;AAEF,YAAM,qBAAqB,MAAM,OAAO,UAAU,YAAY;AAE9D,YAAM,sCAAsC,EAAE,UAAU;AAAA,QACtD;AAAA,UACE,OAAO;AAAA,YACL,MAAM;AAAA,cACJ;AAAA,cACA,MAAM,UAAU,aAAa;AAAA,YAC/B;AAAA,YACA;AAAA,UACF;AAAA,UACA,OAAO,EAAE,GAAG,oBAAqB;AAAA,QACnC;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL;AAAA,YACA;AAAA,UACF;AAAA,UACA,OAAO,EAAE,GAAG,oBAAqB;AAAA,QACnC;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL;AAAA,YACA,MAAM;AAAA,cACJ;AAAA,cACA,MAAM,UAAU,aAAa;AAAA,YAC/B;AAAA,YACA;AAAA,UACF;AAAA,UACA,OAAO;AAAA,YACL,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AAAA,QACF;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL;AAAA,YACA,MAAM;AAAA,cACJ;AAAA,cACA,MAAM,UAAU,aAAa;AAAA,YAC/B;AAAA,UACF;AAAA,UACA,OAAO,EAAE,GAAG,cAAe;AAAA,QAC7B;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,UACA,OAAO;AAAA,YACL,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AAAA,QACF;AAAA,MACF,EAAE;AAEF,YAAM,iBAAiB;AAAA,QACrB,OAAO;AAAA,QACP,OAAO,MAAM,OAAO,UAAU,MAAM,UAAU,GAAG,GAAG,MAAM,UAAU,QAAQ,CAAC;AAAA,MAC/E;AACA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,OAAO;AAAA,UACL;AAAA;AAAA,UAEA,MAAM,OAAO,yBAAyB,kBAAkB,GAAG,EAAE,KAAK,MAAM,GAAG,KAAK,kBAAkB,SAAS,EAAE,KAAK,MAAM,GAAG,MAAM;AAAA,UACjI;AAAA,UACA,MAAM,OAAO,YAAY,GAAG;AAAA,UAC5B,MAAM,UAAU,QAAQ;AAAA,QAC1B;AAAA,QACA,OAAO,EAAE,GAAG,wBAAyB;AAAA,QACrC,UAAU,CAAE,WAAY;AAAA,MAC1B;AACA,kBAAY,SAAS,KAAK,eAAe;AAEzC,YAAM,qBAAqB;AAAA,QACzB;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,aAAa;AAAA,QACjB,OAAO,MAAM,OAAO,UAAU,yBAAyB;AAAA,QACvD,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR,SAAS;AAAA,UACT,SAAS;AAAA,YACP;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,SAAS;AAAA,cACT,SAAS;AAAA,gBACP;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,YACA,UAAU;AAAA,cACR;AAAA,cACA,GAAG;AAAA,YACL;AAAA,UACF;AAAA,UACA,GAAG;AAAA,UACH;AAAA,YACE,OAAO;AAAA,YACP,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,UACA,KAAK;AAAA,UACL,KAAK,QAAQ,MAAM,GAAG;AAAA,UACtB,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA,EAAE,UAAU;AAAA,cACV;AAAA,gBACE,OAAO;AAAA,gBACP,OAAO;AAAA,cACT;AAAA,YACF,EAAE;AAAA,UACJ;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,UAAU;AAAA,YACV,QAAQ;AAAA,cACN,OAAO;AAAA,cACP,KAAK,KAAK;AAAA,cACV,UAAU;AAAA,gBACR;AAAA,kBACE,OAAO;AAAA,kBACP,OAAO;AAAA,kBACP,YAAY;AAAA,gBACd;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,OAAO;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,YACA,OAAO;AAAA,cACL,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,UAAU;AAAA,cACR,EAAE,eAAe,MAAO;AAAA,cACxB,KAAK;AAAA,cACL;AAAA,gBACE,OAAO;AAAA;AAAA,gBACP,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,UAAU;AAAA,kBACR;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,KAAK;AAAA,kBACL;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,UAAU;AAAA,cACR;AAAA,gBACE,eAAe;AAAA,gBACf,SAAS;AAAA,cACX;AAAA,cACA;AAAA,gBACE,eAAe;AAAA,gBACf,SAAS;AAAA,cACX;AAAA,YACF;AAAA,YACA,WAAW;AAAA,YACX,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,cACR,EAAE,eAAe,qBAAqB;AAAA,cACtC,KAAK;AAAA,YACP;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAIA;AAAA,YACE,eAAe;AAAA,YACf,WAAW;AAAA,YACX,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU,CAAE,KAAK,QAAQ,KAAK,uBAAuB,EAAE,OAAO,cAAc,CAAC,CAAE;AAAA,UACjF;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,WAAW;AAAA,YACX,KAAK;AAAA,YACL,UAAU;AAAA;AAAA,cAER;AAAA,gBACE,OAAO;AAAA,gBACP,OAAO;AAAA,cACT;AAAA;AAAA,cAEA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpmBjB;AAAA;AAQA,aAAS,YAAY,MAAM;AACzB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,aAAa;AAAA,YACb,UAAU;AAAA;AAAA;AAAA,cAGR;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,MAAM;AAAA,cACR;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,MAAM;AAAA,cACR;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,MAAM;AAAA,cACR;AAAA,cACA,KAAK,QAAQ,KAAK,kBAAkB;AAAA,gBAClC,SAAS;AAAA,gBACT,WAAW;AAAA,gBACX,UAAU;AAAA,gBACV,MAAM;AAAA,cACR,CAAC;AAAA,cACD,KAAK,QAAQ,KAAK,mBAAmB;AAAA,gBACnC,SAAS;AAAA,gBACT,WAAW;AAAA,gBACX,UAAU;AAAA,gBACV,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrDjB;AAAA;AAOA,aAAS,UAAU,MAAM;AACvB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,QACA,mBAAmB;AAAA,MACrB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClBjB;AAAA;AAOA,aAAS,OAAO,MAAM;AACpB,YAAM,QAAQ,KAAK;AACnB,YAAM,WAAW,WAAC,uCAAkC,GAAC;AACrD,YAAM,iBAAiB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAKA,YAAM,QAAQ;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,QACT,MAAM;AAAA,MACR;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AAEA,YAAM,kBAAkB;AAAA,QACtB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU,CAAE,KAAK,gBAAiB;AAAA,QAClC,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAGA,YAAM,YAAY;AAClB,YAAM,aAAa,QAAQ,SAAS,UAAU,SAAS,SAAS,SAAS;AAMzE,YAAM,YAAY,OAAO,eAAe,KAAK,GAAG,CAAC;AACjD,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAWR;AAAA,YACE,OAAO,QAAQ,SAAS,MAAM,UAAU,eAAe,SAAS,YAAY,SAAS;AAAA,UACvF;AAAA,UACA;AAAA,YACE,OAAO,IAAI,UAAU;AAAA,UACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQA;AAAA,YACE,OAAO,0CAA0C,SAAS;AAAA,UAC5D;AAAA,UACA;AAAA,YACE,OAAO,4BAA4B,SAAS;AAAA,UAC9C;AAAA,UACA;AAAA,YACE,OAAO,6BAA6B,SAAS;AAAA,UAC/C;AAAA,UACA;AAAA,YACE,OAAO,mCAAmC,SAAS;AAAA,UACrD;AAAA;AAAA;AAAA,UAIA;AAAA,YACE,OAAO,OAAO,SAAS,WAAW,SAAS;AAAA,UAC7C;AAAA,QACF;AAAA,MACF;AACA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO,MAAM,UAAU,SAAS;AAAA,QAChC,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA;AAAA,YACE,OAAO;AAAA,UACT;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,gBAAgB;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA;AAAA,UAER;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,cAAc;AAAA,QACd,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA;AAAA,YAGE,eAAe;AAAA,YACf,WAAW;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cAAS;AAAA,cACT;AAAA,YACF;AAAA,YACA,OAAO;AAAA,cACL,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,YACA,UAAU,CAAE,MAAO;AAAA,UACrB;AAAA,UACA;AAAA,YACE,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,kBACL;AAAA,kBAAW;AAAA,kBACX;AAAA,kBAAU;AAAA,kBACV;AAAA,kBAAS;AAAA,kBAAS;AAAA,gBACpB;AAAA,cACF;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,kBACL;AAAA,kBAAW;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,YACA,OAAO;AAAA,cACL,GAAG;AAAA,cACH,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjbjB;AAAA;AAOA,aAAS,WAAW,MAAM;AACxB,aAAO;AAAA,QACL,SAAS,CAAE,OAAQ;AAAA,QACnB,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,QAAQ;AAAA;AAAA;AAAA,cAGN,KAAK;AAAA,cACL,QAAQ;AAAA,gBACN,KAAK;AAAA,gBACL,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,UAAU;AAAA,cACR,EAAE,OAAO,gBAAgB;AAAA,cACzB,EAAE,OAAO,mBAAmB;AAAA,YAC9B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/BjB;AAAA;AAUA,aAAS,EAAE,MAAM;AACf,YAAM,QAAQ,KAAK;AAOnB,YAAM,WAAW;AACjB,YAAM,kBAAkB,MAAM;AAAA;AAAA,QAE5B;AAAA;AAAA,QAEA;AAAA;AAAA,QAEA;AAAA,MACF;AACA,YAAM,eAAe;AACrB,YAAM,iBAAiB,MAAM;AAAA,QAC3B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QAEN,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SACE;AAAA,UACF,SACE;AAAA,UAEF;AAAA;AAAA,YAEE;AAAA;AAAA,QAqBJ;AAAA,QAEA,UAAU;AAAA;AAAA,UAER,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA,EAAE,UAAU;AAAA,cACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAME,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,QAAQ;AAAA,kBACN,KAAK,MAAM,UAAU,MAAM;AAAA;AAAA,oBAEzB;AAAA;AAAA,oBAEA;AAAA,kBACF,CAAC;AAAA,kBACD,YAAY;AAAA,gBACd;AAAA,cACF;AAAA,cACA;AAAA;AAAA;AAAA,gBAGE,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO;AAAA,oBACP,UAAU;AAAA,sBACR,EAAE,OAAO,SAAS;AAAA,sBAClB,EAAE,OAAO,oBAAoB;AAAA,oBAC/B;AAAA,oBACA,YAAY;AAAA,kBACd;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,OAAO;AAAA,cACT;AAAA,YACF,EAAE;AAAA,UACJ;AAAA,UAEA,KAAK;AAAA,UAEL;AAAA,YACE,OAAO;AAAA,YACP,UAAU,CAAE,KAAK,gBAAiB;AAAA,YAClC,UAAU;AAAA,cACR,KAAK,kBAAkB;AAAA,gBACrB,OAAO;AAAA,gBACP,KAAK;AAAA,cACP,CAAC;AAAA,cACD,KAAK,kBAAkB;AAAA,gBACrB,OAAO;AAAA,gBACP,KAAK;AAAA,cACP,CAAC;AAAA,cACD,KAAK,kBAAkB;AAAA,gBACrB,OAAO;AAAA,gBACP,KAAK;AAAA,cACP,CAAC;AAAA,cACD,KAAK,kBAAkB;AAAA,gBACrB,OAAO;AAAA,gBACP,KAAK;AAAA,cACP,CAAC;AAAA,cACD,KAAK,kBAAkB;AAAA,gBACrB,OAAO;AAAA,gBACP,KAAK;AAAA,cACP,CAAC;AAAA,cACD,KAAK,kBAAkB;AAAA,gBACrB,OAAO;AAAA,gBACP,KAAK;AAAA,cACP,CAAC;AAAA,cACD;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAWA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,kBACL,GAAG;AAAA,kBACH,GAAG;AAAA,gBACL;AAAA,gBACA,OAAO;AAAA,kBACL;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,kBACL,GAAG;AAAA,kBACH,GAAG;AAAA,gBACL;AAAA,gBACA,OAAO;AAAA,kBACL;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,kBACL,GAAG;AAAA,kBACH,GAAG;AAAA,gBACL;AAAA,gBACA,OAAO;AAAA,kBACL;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,OAAO,EAAE,GAAG,SAAS;AAAA,gBACrB,OAAO;AAAA,kBACL;AAAA;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAGA;AAAA;AAAA,YAEE,OAAO,EAAE,GAAG,WAAW;AAAA,YACvB,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,YACX,UAAU;AAAA,cACR,EAAE,OAAO,aAAa;AAAA,cACtB,EAAE,OAAO,UAAU;AAAA,YACrB;AAAA,UACF;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UAEA;AAAA;AAAA,YAEE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAE,EAAE,OAAO,MAAM,CAAE;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChQjB;AAAA;AASA,aAAS,KAAK,MAAM;AAClB,YAAM,QAAQ,KAAK;AACnB,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO,MAAM;AAAA,UACX;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,MAAM,UAAU,OAAO;AAAA,QAAC;AAAA,MAC5B;AACA,YAAM,gBAAgB;AACtB,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW;AAAA;AAAA,QAEf;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,QAAQ;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,IAAK;AAAA,QAChB,UAAU;AAAA,UACR,UAAU,KAAK,WAAW;AAAA,UAC1B,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK,QAAQ,QAAQ,QAAQ,EAAE,UAAU,CAAE,MAAO,EAAE,CAAC;AAAA,UACrD,KAAK,QAAQ,KAAK,mBAAmB;AAAA,YACnC,OAAO;AAAA,YACP,SAAS;AAAA,UACX,CAAC;AAAA,UACD;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR,EAAE,OAAO,2BAA2B;AAAA,cACpC,EAAE,OAAO,kCAAkC;AAAA,YAC7C;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR,EAAE,OAAO,kBAAkB,cAAc;AAAA,cACzC,EAAE,OAAO,mBAAmB,cAAc;AAAA,cAC1C,EAAE,OAAO,yBAAyB,cAAc;AAAA,cAChD,EAAE,OAAO,oDACE,cAAc;AAAA,YAC3B;AAAA,YACA,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA,KAAK;AAAA,YACP;AAAA,YACA,WAAW;AAAA,cACT,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,cACA,KAAK;AAAA,YACP;AAAA,YACA,WAAW;AAAA,cACT,GAAG;AAAA,cACH,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA,KAAK;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,YACA,WAAW;AAAA,cACT,GAAG;AAAA,cACH,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA,KAAK;AAAA,YACP;AAAA,YACA,WAAW;AAAA,cACT,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA,KAAK;AAAA,YACP;AAAA,YACA,WAAW;AAAA,cACT,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO,KAAK,WAAW;AAAA,YACvB,UAAU;AAAA,cACR,SAAS;AAAA,cACT,UAAU;AAAA,cACV,MAAM;AAAA,YACR;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChTjB;AAAA;AAAA,QAAM,QAAQ,CAAC,SAAS;AACtB,aAAO;AAAA,QACL,WAAW;AAAA,UACT,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,QACA,eAAe,KAAK;AAAA,QACpB,UAAU;AAAA,UACR,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA,yBAAyB;AAAA,UACvB,OAAO;AAAA,UACP,OAAO;AAAA,UACP,KAAK;AAAA,UACL,SAAS;AAAA,UACT,UAAU;AAAA,YACR,KAAK;AAAA,YACL,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,iBAAiB;AAAA,UACf,OAAO;AAAA,UACP,OAAO,KAAK,YAAY;AAAA,UASxB,WAAW;AAAA,QACb;AAAA,QACA,cAAc;AAAA,UACZ,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAEA,QAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,iBAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,iBAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,IACF;AAGA,QAAM,kBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA,IAGF,EAAE,QAAQ;AAYV,aAAS,KAAK,MAAM;AAClB,YAAM,QAAQ,MAAM,IAAI;AACxB,YAAM,oBAAoB;AAC1B,YAAM,mBAAmB;AAEzB,YAAM,gBAAgB;AACtB,YAAM,eAAe;AACrB,YAAM,WAAW;AACjB,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO,SAAS,WAAW;AAAA,QAC3B,WAAW;AAAA,MACb;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA;AAAA;AAAA,UAGL,MAAM;AAAA,UACN;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA,MAAM;AAAA,UACN;AAAA,YACE,WAAW;AAAA,YACX,OAAO,SAAS,KAAK,KAAK,GAAG,IAAI;AAAA;AAAA,YAEjC,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,OAAO,iBAAiB,KAAK,GAAG,IAAI;AAAA,UAC7C;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,WAAW,kBAAkB,KAAK,GAAG,IAAI;AAAA,UAClD;AAAA,UACA;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAE,MAAM,eAAgB;AAAA,UACpC;AAAA,UACA,MAAM;AAAA,UACN;AAAA,YACE,WAAW;AAAA,YACX,OAAO,SAAS,WAAW,KAAK,GAAG,IAAI;AAAA,UACzC;AAAA,UACA,EAAE,OAAO,6oCAA6oC;AAAA,UACtpC;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,YACX,UAAU;AAAA,cACR,MAAM;AAAA,cACN;AAAA,cACA,MAAM;AAAA,cACN,MAAM;AAAA,cACN,KAAK;AAAA,cACL,KAAK;AAAA,cACL,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAIA;AAAA,YACE,OAAO;AAAA,YACP,UAAU;AAAA,cACR,UAAU;AAAA,cACV,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,aAAa;AAAA,YACb,UAAU;AAAA,cACR,UAAU;AAAA,cACV,SAAS;AAAA,cACT,WAAW,eAAe,KAAK,GAAG;AAAA,YACpC;AAAA,YACA,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,cACL,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,UACF;AAAA,UACA,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvtBjB;AAAA;AASA,aAAS,MAAM,MAAM;AACnB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA;AAAA;AAAA;AAAA,YAIX,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChCjB;AAAA;AAsBA,aAAS,IAAI,MAAM;AACjB,YAAM,QAAQ,KAAK;AACnB,YAAM,eAAe,KAAK,QAAQ,MAAM,GAAG;AAC3C,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAE,EAAE,OAAO,KAAK,CAAE;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AACA,YAAM,oBAAoB;AAAA,QACxB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,EAAE,OAAO,KAAK,CAAE;AAAA,MAC9B;AAEA,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA;AAAA;AAAA,QAGA;AAAA,MACF;AAEA,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,QAAQ;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,qBAAqB;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,iBAAiB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAKA,YAAM,qBAAqB;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,0BAA0B;AAAA,QAC9B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAIA,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,YAAY;AAElB,YAAM,WAAW;AAAA,QACf,GAAG;AAAA,QACH,GAAG;AAAA,MACL,EAAE,OAAO,CAAC,YAAY;AACpB,eAAO,CAAC,mBAAmB,SAAS,OAAO;AAAA,MAC7C,CAAC;AAED,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAEA,YAAM,gBAAgB;AAAA,QACpB,OAAO,MAAM,OAAO,MAAM,MAAM,OAAO,GAAG,SAAS,GAAG,OAAO;AAAA,QAC7D,WAAW;AAAA,QACX,UAAU,EAAE,UAAU,UAAU;AAAA,MAClC;AAGA,eAAS,gBAAgB,MAAM;AAAA,QAC7B;AAAA,QAAY;AAAA,MACd,IAAI,CAAC,GAAG;AACN,cAAM,YAAY;AAClB,qBAAa,cAAc,CAAC;AAC5B,eAAO,KAAK,IAAI,CAAC,SAAS;AACxB,cAAI,KAAK,MAAM,QAAQ,KAAK,WAAW,SAAS,IAAI,GAAG;AACrD,mBAAO;AAAA,UACT,WAAW,UAAU,IAAI,GAAG;AAC1B,mBAAO,GAAG,IAAI;AAAA,UAChB,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA;AAAA,QAElB,SAAS;AAAA,QACT,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SACE,gBAAgB,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;AAAA,UACzD,SAAS;AAAA,UACT,MAAM;AAAA,UACN,UAAU;AAAA,QACZ;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,OAAO,MAAM,OAAO,GAAG,MAAM;AAAA,YAC7B,WAAW;AAAA,YACX,UAAU;AAAA,cACR,UAAU;AAAA,cACV,SAAS,SAAS,OAAO,MAAM;AAAA,cAC/B,SAAS;AAAA,cACT,MAAM;AAAA,YACR;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,MAAM,OAAO,GAAG,gBAAgB;AAAA,UACzC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzqBjB;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,IAAI;AACrB,aAAO,OAAO,OAAO,IAAI,GAAG;AAAA,IAC9B;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AAMA,aAAS,qBAAqB,MAAM;AAClC,YAAM,OAAO,KAAK,KAAK,SAAS,CAAC;AAEjC,UAAI,OAAO,SAAS,YAAY,KAAK,gBAAgB,QAAQ;AAC3D,aAAK,OAAO,KAAK,SAAS,GAAG,CAAC;AAC9B,eAAO;AAAA,MACT,OAAO;AACL,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AAWA,aAAS,UAAU,MAAM;AAEvB,YAAM,OAAO,qBAAqB,IAAI;AACtC,YAAM,SAAS,OACV,KAAK,UAAU,KAAK,QACrB,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAC3C,aAAO;AAAA,IACT;AAEA,QAAM,iBAAiB,aAAW;AAAA,MAChC;AAAA,MACA;AAAA,MACA,MAAM,KAAK,OAAO,IAAI,OAAO;AAAA,IAC/B;AAGA,QAAM,cAAc;AAAA,MAClB;AAAA;AAAA,MACA;AAAA;AAAA,IACF,EAAE,IAAI,cAAc;AAGpB,QAAM,sBAAsB;AAAA,MAC1B;AAAA,MACA;AAAA,IACF,EAAE,IAAI,cAAc;AAGpB,QAAM,eAAe;AAAA,MACnB;AAAA,MACA;AAAA,IACF;AAGA,QAAM,WAAW;AAAA;AAAA;AAAA;AAAA,MAIf;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,IACF;AAMA,QAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,0BAA0B;AAAA,MAC9B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAIA,QAAM,qBAAqB;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,eAAe;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,oBAAoB;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA,IAGF;AAGA,QAAM,WAAW,OAAO,cAAc,mBAAmB,GAAG;AAG5D,QAAM,iBAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMF;AAGA,QAAM,sBAAsB;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,aAAa,OAAO,gBAAgB,qBAAqB,GAAG;AAGlE,QAAM,iBAAiB,OAAO,SAAS,qBAAqB,GAAG;AAK/D,QAAM,oBAAoB;AAAA,MACxB;AAAA,MACA;AAAA,MACA,OAAO,gBAAgB,OAAO,SAAS,SAAS,GAAG,GAAG,IAAI;AAAA,MAC1D;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,UAAU,YAAY,IAAI;AAAA,MACjC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,uBAAuB;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAaA,aAAS,MAAM,MAAM;AACnB,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAEA,YAAM,gBAAgB,KAAK;AAAA,QACzB;AAAA,QACA;AAAA,QACA,EAAE,UAAU,CAAE,MAAO,EAAE;AAAA,MACzB;AACA,YAAM,WAAW;AAAA,QACf,KAAK;AAAA,QACL;AAAA,MACF;AAIA,YAAM,cAAc;AAAA,QAClB,OAAO;AAAA,UACL;AAAA,UACA,OAAO,GAAG,aAAa,GAAG,mBAAmB;AAAA,QAC/C;AAAA,QACA,WAAW,EAAE,GAAG,UAAU;AAAA,MAC5B;AACA,YAAM,gBAAgB;AAAA;AAAA,QAEpB,OAAO,OAAO,MAAM,OAAO,GAAG,QAAQ,CAAC;AAAA,QACvC,WAAW;AAAA,MACb;AACA,YAAM,iBAAiB,SACpB,OAAO,QAAM,OAAO,OAAO,QAAQ,EACnC,OAAO,CAAE,KAAM,CAAC;AACnB,YAAM,iBAAiB,SACpB,OAAO,QAAM,OAAO,OAAO,QAAQ,EACnC,OAAO,YAAY,EACnB,IAAI,cAAc;AACrB,YAAM,UAAU,EAAE,UAAU;AAAA,QAC1B;AAAA,UACE,WAAW;AAAA,UACX,OAAO,OAAO,GAAG,gBAAgB,GAAG,mBAAmB;AAAA,QACzD;AAAA,MACF,EAAE;AAEF,YAAM,WAAW;AAAA,QACf,UAAU;AAAA,UACR;AAAA;AAAA,UACA;AAAA;AAAA,QACF;AAAA,QACA,SAAS,eACN,OAAO,kBAAkB;AAAA,QAC5B,SAAS;AAAA,MACX;AACA,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,iBAAiB;AAAA;AAAA,QAErB,OAAO,OAAO,MAAM,OAAO,GAAG,QAAQ,CAAC;AAAA,QACvC,WAAW;AAAA,MACb;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO,OAAO,MAAM,OAAO,GAAG,QAAQ,GAAG,QAAQ;AAAA,MACnD;AACA,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,MACF;AAGA,YAAM,iBAAiB;AAAA;AAAA,QAErB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,SAAS;AAAA,UAClB;AAAA;AAAA;AAAA;AAAA,YAIE,OAAO,WAAW,iBAAiB;AAAA,UAAK;AAAA,QAC5C;AAAA,MACF;AACA,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,MACF;AAIA,YAAM,gBAAgB;AACtB,YAAM,YAAY;AAClB,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA;AAAA,UAER,EAAE,OAAO,OAAO,aAAa,SAAS,aAAa,iBAAsB,aAAa,SAAS;AAAA;AAAA,UAE/F,EAAE,OAAO,SAAS,SAAS,SAAS,SAAS,iBAAsB,aAAa,SAAS;AAAA;AAAA,UAEzF,EAAE,OAAO,mBAAmB;AAAA;AAAA,UAE5B,EAAE,OAAO,kBAAkB;AAAA,QAC7B;AAAA,MACF;AAGA,YAAM,oBAAoB,CAAC,eAAe,QAAQ;AAAA,QAChD,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,OAAO,MAAM,cAAc,YAAY,EAAE;AAAA,UAClD,EAAE,OAAO,OAAO,MAAM,cAAc,uBAAuB,EAAE;AAAA,QAC/D;AAAA,MACF;AACA,YAAM,kBAAkB,CAAC,eAAe,QAAQ;AAAA,QAC9C,WAAW;AAAA,QACX,OAAO,OAAO,MAAM,cAAc,uBAAuB;AAAA,MAC3D;AACA,YAAM,gBAAgB,CAAC,eAAe,QAAQ;AAAA,QAC5C,WAAW;AAAA,QACX,OAAO;AAAA,QACP,OAAO,OAAO,MAAM,cAAc,IAAI;AAAA,QACtC,KAAK;AAAA,MACP;AACA,YAAM,mBAAmB,CAAC,eAAe,QAAQ;AAAA,QAC/C,OAAO,OAAO,cAAc,KAAK;AAAA,QACjC,KAAK,OAAO,OAAO,YAAY;AAAA,QAC/B,UAAU;AAAA,UACR,kBAAkB,YAAY;AAAA,UAC9B,gBAAgB,YAAY;AAAA,UAC5B,cAAc,YAAY;AAAA,QAC5B;AAAA,MACF;AACA,YAAM,qBAAqB,CAAC,eAAe,QAAQ;AAAA,QACjD,OAAO,OAAO,cAAc,GAAG;AAAA,QAC/B,KAAK,OAAO,KAAK,YAAY;AAAA,QAC7B,UAAU;AAAA,UACR,kBAAkB,YAAY;AAAA,UAC9B,cAAc,YAAY;AAAA,QAC5B;AAAA,MACF;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR,iBAAiB;AAAA,UACjB,iBAAiB,GAAG;AAAA,UACpB,iBAAiB,IAAI;AAAA,UACrB,iBAAiB,KAAK;AAAA,UACtB,mBAAmB;AAAA,UACnB,mBAAmB,GAAG;AAAA,UACtB,mBAAmB,IAAI;AAAA,UACvB,mBAAmB,KAAK;AAAA,QAC1B;AAAA,MACF;AAEA,YAAM,kBAAkB;AAAA,QACtB,KAAK;AAAA,QACL;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,WAAW;AAAA,UACX,UAAU,CAAE,KAAK,gBAAiB;AAAA,QACpC;AAAA,MACF;AAEA,YAAM,sBAAsB;AAAA,QAC1B,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AAEA,YAAM,0BAA0B,CAAC,iBAAiB;AAChD,cAAM,QAAQ,OAAO,cAAc,IAAI;AACvC,cAAM,MAAM,OAAO,MAAM,YAAY;AACrC,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,UAAU;AAAA,YACR,GAAG;AAAA,YACH;AAAA,cACE,OAAO;AAAA,cACP,OAAO,SAAS,GAAG;AAAA,cACnB,KAAK;AAAA,YACP;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,UAAU;AAAA,UACR,wBAAwB,KAAK;AAAA,UAC7B,wBAAwB,IAAI;AAAA,UAC5B,wBAAwB,GAAG;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAGA,YAAM,oBAAoB,EAAE,OAAO,OAAO,KAAK,YAAY,GAAG,EAAE;AAChE,YAAM,qBAAqB;AAAA,QACzB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,8BAA8B;AAAA,QAClC,WAAW;AAAA,QACX,OAAO,MAAM,mBAAmB;AAAA,MAClC;AACA,YAAM,cAAc;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,sBAAsB;AAAA,QAC1B,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ,EAAE,UAAU;AAAA,UAClB;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,cACR,GAAG;AAAA,cACH;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE;AAAA,MACJ;AACA,YAAM,oBAAoB;AAAA,QACxB,OAAO;AAAA,QACP,OAAO,OAAO,KAAK,OAAO,GAAG,iBAAiB,CAAC;AAAA,MACjD;AACA,YAAM,yBAAyB;AAAA,QAC7B,OAAO;AAAA,QACP,OAAO,OAAO,KAAK,UAAU;AAAA,MAC/B;AACA,YAAM,aAAa;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,OAAO;AAAA,QACX,OAAO,UAAU,SAAS;AAAA,QAC1B,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,OAAO,iEAAiE,qBAAqB,GAAG;AAAA,UACzG;AAAA,UACA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,YACE,OAAO,OAAO,WAAW,UAAU,cAAc,CAAC;AAAA,YAClD,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AACA,YAAM,oBAAoB;AAAA,QACxB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,WAAK,SAAS,KAAK,iBAAiB;AAIpC,YAAM,qBAAqB;AAAA,QACzB,OAAO,OAAO,YAAY,MAAM;AAAA,QAChC,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AAEA,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA,GAAG;AAAA,UACH;AAAA,UACA,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAEA,YAAM,qBAAqB;AAAA,QACzB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF;AACA,YAAM,0BAA0B;AAAA,QAC9B,OAAO;AAAA,UACL,UAAU,OAAO,YAAY,MAAM,CAAC;AAAA,UACpC,UAAU,OAAO,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,QACzD;AAAA,QACA,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,sBAAsB;AAAA,QAC1B,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,UACA,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA,GAAG;AAAA,UACH;AAAA,UACA;AAAA,QACF;AAAA,QACA,YAAY;AAAA,QACZ,SAAS;AAAA,MACX;AAGA,YAAM,oBAAoB;AAAA,QACxB,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,OAAO,kBAAkB,OAAO,YAAY,QAAQ;AAAA,QACtD;AAAA,QACA,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAIA,YAAM,iBAAiB;AAAA,QACrB,OAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,QACA,WAAW,EAAE,GAAG,UAAU;AAAA,QAC1B,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX;AAEA,YAAM,uBAAuB;AAAA,QAC3B,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,MACF;AAGA,YAAM,kBAAkB;AAAA,QACtB,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,QACA,UAAU,CAAE,IAAK;AAAA,QACjB,UAAU;AAAA,UACR,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,QACA,KAAK;AAAA,MACP;AAGA,iBAAW,WAAW,OAAO,UAAU;AACrC,cAAM,gBAAgB,QAAQ,SAAS,KAAK,UAAQ,KAAK,UAAU,UAAU;AAE7E,sBAAc,WAAW;AACzB,cAAM,WAAW;AAAA,UACf,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA,GAAG;AAAA,QACL;AACA,sBAAc,WAAW;AAAA,UACvB,GAAG;AAAA,UACH;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,cACA,GAAG;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,UACR,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,UAAU;AAAA,cACR,KAAK,QAAQ,KAAK,YAAY;AAAA,gBAC5B,WAAW;AAAA,gBACX,OAAO;AAAA,cACT,CAAC;AAAA,cACD,GAAG;AAAA,YACL;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU,CAAE,GAAG,QAAS;AAAA,YACxB,WAAW;AAAA,UACb;AAAA,UACA;AAAA,UACA,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACv5BjB;AAAA;AASA,aAAS,KAAK,MAAM;AAClB,YAAM,WAAW;AAGjB,YAAM,iBAAiB;AAMvB,YAAM,MAAM;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,8BAA+B;AAAA,UACxC;AAAA;AAAA,YACE,OAAO;AAAA,UAAiC;AAAA,UAC1C;AAAA;AAAA,YACE,OAAO;AAAA,UAAmC;AAAA,QAC9C;AAAA,MACF;AAEA,YAAM,qBAAqB;AAAA,QACzB,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA,EAAE,OAAO,MAAM;AAAA,QACjB;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAIA,YAAM,mBAAmB,KAAK,QAAQ,QAAQ,EAAE,UAAU;AAAA,QACxD;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,QACP;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,QACP;AAAA,QACA,EAAE,OAAO,eAAe;AAAA,MAC1B,EAAE,CAAC;AAEH,YAAM,UAAU;AAChB,YAAM,UAAU;AAChB,YAAM,cAAc;AACpB,YAAM,UAAU;AAChB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO,QAAQ,UAAU,UAAU,cAAc,UAAU;AAAA,MAC7D;AAEA,YAAM,kBAAkB;AAAA,QACtB,KAAK;AAAA,QACL,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AACA,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,eAAgB;AAAA,QAC5B,SAAS;AAAA,QACT,WAAW;AAAA,MACb;AACA,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,eAAgB;AAAA,QAC5B,SAAS;AAAA,QACT,WAAW;AAAA,MACb;AAEA,YAAM,QAAQ;AAAA,QACZ;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,WAAW;AAAA,QACb;AAAA,QACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAKE,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA;AAAA;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,aAAa;AAAA,UACb,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,WAAW;AAAA,QACb;AAAA,QACA;AAAA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,WAAW;AAAA,QACpB;AAAA;AAAA,QAEA;AAAA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,OAAO,iBAAiB;AAAA,QACjC;AAAA,QACA;AAAA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,MAAM;AAAA,QACf;AAAA,QACA;AAAA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,OAAO;AAAA,QAChB;AAAA,QACA;AAAA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,MAAM,KAAK,sBAAsB;AAAA,QAC1C;AAAA,QACA;AAAA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,QAAQ,KAAK,sBAAsB;AAAA,QAC5C;AAAA,QACA;AAAA;AAAA,UACE,WAAW;AAAA;AAAA,UAEX,OAAO;AAAA,UACP,WAAW;AAAA,QACb;AAAA,QACA,KAAK;AAAA,QACL;AAAA,UACE,eAAe;AAAA,UACf,UAAU,EAAE,SAAS,SAAS;AAAA,QAChC;AAAA,QACA;AAAA;AAAA;AAAA,QAGA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,KAAK,cAAc;AAAA,UAC1B,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,cAAc,CAAE,GAAG,KAAM;AAC/B,kBAAY,IAAI;AAChB,kBAAY,KAAK,gBAAgB;AACjC,sBAAgB,WAAW;AAE3B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,SAAS,CAAE,KAAM;AAAA,QACjB,UAAU;AAAA,MACZ;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjMjB;AAAA;AAAA,QAAM,WAAW;AACjB,QAAM,WAAW;AAAA,MACf;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,MAIA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAM,QAAQ;AAAA;AAAA,MAEZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA,IACF;AAEA,QAAM,cAAc;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA;AAAA,MACA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,qBAAqB;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,IACF;AAEA,QAAM,YAAY,CAAC,EAAE;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAWA,aAAS,WAAW,MAAM;AACxB,YAAM,QAAQ,KAAK;AAQnB,YAAM,gBAAgB,CAAC,OAAO,EAAE,MAAM,MAAM;AAC1C,cAAM,MAAM,OAAO,MAAM,CAAC,EAAE,MAAM,CAAC;AACnC,cAAM,MAAM,MAAM,MAAM,QAAQ,KAAK,KAAK;AAC1C,eAAO,QAAQ;AAAA,MACjB;AAEA,YAAM,aAAa;AACnB,YAAM,WAAW;AAAA,QACf,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AAEA,YAAM,mBAAmB;AACzB,YAAM,UAAU;AAAA,QACd,OAAO;AAAA,QACP,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,QAKL,mBAAmB,CAAC,OAAO,aAAa;AACtC,gBAAM,kBAAkB,MAAM,CAAC,EAAE,SAAS,MAAM;AAChD,gBAAM,WAAW,MAAM,MAAM,eAAe;AAC5C;AAAA;AAAA;AAAA;AAAA,YAIE,aAAa;AAAA;AAAA,YAGb,aAAa;AAAA,YACX;AACF,qBAAS,YAAY;AACrB;AAAA,UACF;AAIA,cAAI,aAAa,KAAK;AAGpB,gBAAI,CAAC,cAAc,OAAO,EAAE,OAAO,gBAAgB,CAAC,GAAG;AACrD,uBAAS,YAAY;AAAA,YACvB;AAAA,UACF;AAKA,cAAI;AACJ,gBAAM,aAAa,MAAM,MAAM,UAAU,eAAe;AAIxD,cAAK,IAAI,WAAW,MAAM,OAAO,GAAI;AACnC,qBAAS,YAAY;AACrB;AAAA,UACF;AAKA,cAAK,IAAI,WAAW,MAAM,gBAAgB,GAAI;AAC5C,gBAAI,EAAE,UAAU,GAAG;AACjB,uBAAS,YAAY;AAErB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,aAAa;AAAA,QACjB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU;AAAA,QACV,qBAAqB;AAAA,MACvB;AAGA,YAAM,gBAAgB;AACtB,YAAM,OAAO,OAAO,aAAa;AAGjC,YAAM,iBAAiB;AACvB,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA;AAAA,UAER,EAAE,OAAO,QAAQ,cAAc,MAAM,IAAI,YAAY,IAAI,eAC1C,aAAa,OAAO;AAAA,UACnC,EAAE,OAAO,OAAO,cAAc,SAAS,IAAI,eAAe,IAAI,OAAO;AAAA;AAAA,UAGrE,EAAE,OAAO,6BAA6B;AAAA;AAAA,UAGtC,EAAE,OAAO,2CAA2C;AAAA,UACpD,EAAE,OAAO,+BAA+B;AAAA,UACxC,EAAE,OAAO,+BAA+B;AAAA;AAAA;AAAA,UAIxC,EAAE,OAAO,kBAAkB;AAAA,QAC7B;AAAA,QACA,WAAW;AAAA,MACb;AAEA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU,CAAC;AAAA;AAAA,MACb;AACA,YAAM,gBAAgB;AAAA,QACpB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,QAAQ;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,UAAU;AAAA,YACR,KAAK;AAAA,YACL;AAAA,UACF;AAAA,UACA,aAAa;AAAA,QACf;AAAA,MACF;AACA,YAAM,eAAe;AAAA,QACnB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,QAAQ;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,UAAU;AAAA,YACR,KAAK;AAAA,YACL;AAAA,UACF;AAAA,UACA,aAAa;AAAA,QACf;AAAA,MACF;AACA,YAAM,mBAAmB;AAAA,QACvB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,QAAQ;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,UAAU;AAAA,YACR,KAAK;AAAA,YACL;AAAA,UACF;AAAA,UACA,aAAa;AAAA,QACf;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,YAAM,gBAAgB,KAAK;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,WAAW;AAAA,cACX,UAAU;AAAA,gBACR;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,gBACT;AAAA,gBACA;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,kBACP,KAAK;AAAA,kBACL,YAAY;AAAA,kBACZ,cAAc;AAAA,kBACd,WAAW;AAAA,gBACb;AAAA,gBACA;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO,aAAa;AAAA,kBACpB,YAAY;AAAA,kBACZ,WAAW;AAAA,gBACb;AAAA;AAAA;AAAA,gBAGA;AAAA,kBACE,OAAO;AAAA,kBACP,WAAW;AAAA,gBACb;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA,EAAE,OAAO,QAAQ;AAAA,QACjB;AAAA;AAAA;AAAA;AAAA,MAIF;AACA,YAAM,WAAW,gBACd,OAAO;AAAA;AAAA;AAAA,QAGN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,QACF,EAAE,OAAO,eAAe;AAAA,MAC1B,CAAC;AACH,YAAM,qBAAqB,CAAC,EAAE,OAAO,SAAS,MAAM,QAAQ;AAC5D,YAAM,kBAAkB,mBAAmB,OAAO;AAAA;AAAA,QAEhD;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU;AAAA,UACV,UAAU,CAAC,MAAM,EAAE,OAAO,kBAAkB;AAAA,QAC9C;AAAA,MACF,CAAC;AACD,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAGA,YAAM,mBAAmB;AAAA,QACvB,UAAU;AAAA;AAAA,UAER;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,MAAM,OAAO,YAAY,KAAK,MAAM,OAAO,MAAM,UAAU,GAAG,IAAI;AAAA,YACpE;AAAA,YACA,OAAO;AAAA,cACL,GAAG;AAAA,cACH,GAAG;AAAA,cACH,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,YACA,OAAO;AAAA,cACL,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA,QAEF;AAAA,MACF;AAEA,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,OACA,MAAM;AAAA;AAAA,UAEJ;AAAA;AAAA,UAEA;AAAA;AAAA,UAEA;AAAA;AAAA,UAEA;AAAA;AAAA;AAAA;AAAA;AAAA,QAKF;AAAA,QACA,WAAW;AAAA,QACX,UAAU;AAAA,UACR,GAAG;AAAA;AAAA,YAED,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAEA,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,sBAAsB;AAAA,QAC1B,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,QACA,OAAO;AAAA,QACP,UAAU,CAAE,MAAO;AAAA,QACnB,SAAS;AAAA,MACX;AAEA,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAEA,eAAS,OAAO,MAAM;AACpB,eAAO,MAAM,OAAO,OAAO,KAAK,KAAK,GAAG,GAAG,GAAG;AAAA,MAChD;AAEA,YAAM,gBAAgB;AAAA,QACpB,OAAO,MAAM;AAAA,UACX;AAAA,UACA,OAAO;AAAA,YACL,GAAG;AAAA,YACH;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD;AAAA,UAAY,MAAM,UAAU,IAAI;AAAA,QAAC;AAAA,QACnC,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAEA,YAAM,kBAAkB;AAAA,QACtB,OAAO,MAAM,OAAO,MAAM,MAAM;AAAA,UAC9B,MAAM,OAAO,YAAY,oBAAoB;AAAA,QAC/C,CAAC;AAAA,QACD,KAAK;AAAA,QACL,cAAc;AAAA,QACd,UAAU;AAAA,QACV,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAEA,YAAM,mBAAmB;AAAA,QACvB,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,QACA,UAAU;AAAA,UACR;AAAA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,kBAAkB,6DAMb,KAAK,sBAAsB;AAEtC,YAAM,oBAAoB;AAAA,QACxB,OAAO;AAAA,UACL;AAAA,UAAiB;AAAA,UACjB;AAAA,UAAY;AAAA,UACZ;AAAA,UACA;AAAA;AAAA,UACA,MAAM,UAAU,eAAe;AAAA,QACjC;AAAA,QACA,UAAU;AAAA,QACV,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,QACA,UAAU;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,MAAM,OAAO,OAAO,KAAK;AAAA,QACnC,UAAU;AAAA;AAAA,QAEV,SAAS,EAAE,iBAAiB,gBAAgB;AAAA,QAC5C,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK,QAAQ;AAAA,YACX,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,WAAW;AAAA,UACb,CAAC;AAAA,UACD;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA,UAEA,EAAE,OAAO,QAAQ;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,aAAa,MAAM,UAAU,GAAG;AAAA,YACvC,WAAW;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA;AAAA,YACE,OAAO,MAAM,KAAK,iBAAiB;AAAA,YACnC,UAAU;AAAA,YACV,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,cACA,KAAK;AAAA,cACL;AAAA,gBACE,WAAW;AAAA;AAAA;AAAA;AAAA,gBAIX,OAAO;AAAA,gBACP,aAAa;AAAA,gBACb,KAAK;AAAA,gBACL,UAAU;AAAA,kBACR;AAAA,oBACE,WAAW;AAAA,oBACX,UAAU;AAAA,sBACR;AAAA,wBACE,OAAO,KAAK;AAAA,wBACZ,WAAW;AAAA,sBACb;AAAA,sBACA;AAAA,wBACE,WAAW;AAAA,wBACX,OAAO;AAAA,wBACP,MAAM;AAAA,sBACR;AAAA,sBACA;AAAA,wBACE,OAAO;AAAA,wBACP,KAAK;AAAA,wBACL,cAAc;AAAA,wBACd,YAAY;AAAA,wBACZ,UAAU;AAAA,wBACV,UAAU;AAAA,sBACZ;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA;AAAA;AAAA,gBACE,UAAU;AAAA,kBACR,EAAE,OAAO,SAAS,OAAO,KAAK,SAAS,IAAI;AAAA,kBAC3C,EAAE,OAAO,iBAAiB;AAAA,kBAC1B;AAAA,oBACE,OAAO,QAAQ;AAAA;AAAA;AAAA,oBAGf,YAAY,QAAQ;AAAA,oBACpB,KAAK,QAAQ;AAAA,kBACf;AAAA,gBACF;AAAA,gBACA,aAAa;AAAA,gBACb,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO,QAAQ;AAAA,oBACf,KAAK,QAAQ;AAAA,oBACb,MAAM;AAAA,oBACN,UAAU,CAAC,MAAM;AAAA,kBACnB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA;AAAA;AAAA,YAGE,eAAe;AAAA,UACjB;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,YAIE,OAAO,oBAAoB,KAAK,sBAC9B;AAAA;AAAA,YAOF,aAAY;AAAA,YACZ,OAAO;AAAA,YACP,UAAU;AAAA,cACR;AAAA,cACA,KAAK,QAAQ,KAAK,YAAY,EAAE,OAAO,YAAY,WAAW,iBAAiB,CAAC;AAAA,YAClF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UAIA;AAAA,YACE,OAAO,QAAQ;AAAA,YACf,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO,CAAE,wBAAyB;AAAA,YAClC,WAAW,EAAE,GAAG,iBAAiB;AAAA,YACjC,UAAU,CAAE,MAAO;AAAA,UACrB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,OAAO;AAAA;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAaA,aAAS,WAAW,MAAM;AACxB,YAAM,aAAa,WAAW,IAAI;AAElC,YAAM,aAAa;AACnB,YAAMA,SAAQ;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY;AAAA,QAChB,eAAe;AAAA,QACf,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,UAAU,CAAE,WAAW,QAAQ,eAAgB;AAAA,MACjD;AACA,YAAM,YAAY;AAAA,QAChB,eAAe;AAAA,QACf,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,UAAU;AAAA,UACR,SAAS;AAAA,UACT,UAAUA;AAAA,QACZ;AAAA,QACA,UAAU,CAAE,WAAW,QAAQ,eAAgB;AAAA,MACjD;AACA,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,uBAAuB;AAAA,QAC3B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,aAAa;AAAA,QACjB,UAAU;AAAA,QACV,SAAS,SAAS,OAAO,oBAAoB;AAAA,QAC7C,SAAS;AAAA,QACT,UAAU,UAAU,OAAOA,MAAK;AAAA,QAChC,qBAAqB;AAAA,MACvB;AACA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO,MAAM;AAAA,MACf;AAEA,YAAM,WAAW,CAAC,MAAM,OAAO,gBAAgB;AAC7C,cAAM,OAAO,KAAK,SAAS,UAAU,OAAK,EAAE,UAAU,KAAK;AAC3D,YAAI,SAAS,IAAI;AAAE,gBAAM,IAAI,MAAM,8BAA8B;AAAA,QAAG;AAEpE,aAAK,SAAS,OAAO,MAAM,GAAG,WAAW;AAAA,MAC3C;AAKA,aAAO,OAAO,WAAW,UAAU,UAAU;AAE7C,iBAAW,QAAQ,gBAAgB,KAAK,SAAS;AACjD,iBAAW,WAAW,WAAW,SAAS,OAAO;AAAA,QAC/C;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAGD,eAAS,YAAY,WAAW,KAAK,QAAQ,CAAC;AAE9C,eAAS,YAAY,cAAc,UAAU;AAE7C,YAAM,sBAAsB,WAAW,SAAS,KAAK,OAAK,EAAE,UAAU,UAAU;AAChF,0BAAoB,YAAY;AAEhC,aAAO,OAAO,YAAY;AAAA,QACxB,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/2BjB;AAAA;AASA,aAAS,MAAM,MAAM;AACnB,YAAM,QAAQ,KAAK;AAKnB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA;AAAA,YAEE,OAAO;AAAA,UAAK;AAAA,QAChB;AAAA,MACF;AAGA,YAAM,aAAa;AACnB,YAAM,aAAa;AACnB,YAAM,WAAW;AACjB,YAAM,WAAW;AACjB,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA;AAAA,YAEE,OAAO,MAAM,OAAO,OAAO,MAAM,OAAO,YAAY,UAAU,GAAG,KAAK;AAAA,UAAE;AAAA,UAC1E;AAAA;AAAA,YAEE,OAAO,MAAM,OAAO,OAAO,UAAU,KAAK;AAAA,UAAE;AAAA,UAC9C;AAAA;AAAA,YAEE,OAAO,MAAM,OAAO,OAAO,UAAU,KAAK;AAAA,UAAE;AAAA,UAC9C;AAAA;AAAA,YAEE,OAAO,MAAM;AAAA,cACX;AAAA,cACA,MAAM,OAAO,YAAY,UAAU;AAAA,cACnC;AAAA,cACA,MAAM,OAAO,UAAU,QAAQ;AAAA,cAC/B;AAAA,YACF;AAAA,UAAE;AAAA,QACN;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA;AAAA,YAEE,OAAO;AAAA,UAAgE;AAAA,UACzE;AAAA;AAAA,YAEE,OAAO;AAAA,UAA8B;AAAA,UACvC;AAAA;AAAA,YAEE,OAAO;AAAA,UAA+B;AAAA,UACxC;AAAA;AAAA,YAEE,OAAO;AAAA,UAA6B;AAAA,UACtC;AAAA;AAAA,YAEE,OAAO;AAAA,UAA4B;AAAA,QACvC;AAAA,MACF;AAEA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,cAAc,KAAK,QAAQ,OAAO,KAAK,EAAE,UAAU;AAAA,QACvD;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,QACP;AAAA,MACF,EAAE,CAAC;AAEH,YAAM,UAAU,KAAK,QAAQ,MAAM,KAAK,EAAE,UAAU;AAAA,QAClD,EAAE,OAAO,IAAI;AAAA,QACb;AAAA;AAAA,UAEE,OAAO;AAAA,QAAqB;AAAA,MAChC,EAAE,CAAC;AAEH,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA;AAAA,QAEX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,EAAE,SACR,qEAAqE;AAAA,QACzE,UAAU,CAAE,OAAQ;AAAA,MACtB;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,IAAK;AAAA,QAChB,kBAAkB;AAAA,QAClB,kBAAkB,EAAE,OAAO,SAAS;AAAA,QACpC,UAAU;AAAA,UACR,SACE;AAAA,UAWF;AAAA;AAAA,YAEE;AAAA;AAAA,UAGF;AAAA;AAAA,YAEE;AAAA;AAAA,UACF,SAAS;AAAA,QACX;AAAA,QACA,SACE;AAAA,QACF,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5JjB;AAAA;AASA,aAAS,KAAK,MAAM;AAClB,WAAK;AACL,YAAM,gBAAgB,KAAK,QAAQ,OAAO,KAAK;AAC/C,oBAAc,SAAS,KAAK,MAAM;AAClC,YAAM,eAAe,KAAK,QAAQ,MAAM,GAAG;AAE3C,YAAM,MAAM;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,qBAAqB;AAAA,QACzB,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA;AAAA,QAEX,OAAO;AAAA,MACT;AAEA,YAAM,OAAO;AAAA;AAAA,QAEX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAEA,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA;AAAA,QAEX,OAAO;AAAA,MACT;AAEA,YAAM,eAAe;AAAA,QACnB,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SAAS;AAAA,QACX;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC1IjB;AAAA;AAAA,QAAI,OAAO;AAEX,SAAK,iBAAiB,OAAO,aAA0B;AACvD,SAAK,iBAAiB,QAAQ,cAA2B;AACzD,SAAK,iBAAiB,KAAK,WAAwB;AACnD,SAAK,iBAAiB,OAAO,aAA0B;AACvD,SAAK,iBAAiB,UAAU,gBAA6B;AAC7D,SAAK,iBAAiB,OAAO,aAA0B;AACvD,SAAK,iBAAiB,YAAY,kBAA+B;AACjE,SAAK,iBAAiB,QAAQ,cAA2B;AACzD,SAAK,iBAAiB,QAAQ,cAA2B;AACzD,SAAK,iBAAiB,MAAM,YAAyB;AACrD,SAAK,iBAAiB,WAAW,iBAA8B;AAC/D,SAAK,iBAAiB,OAAO,aAA0B;AACvD,SAAK,iBAAiB,QAAQ,cAA2B;AACzD,SAAK,iBAAiB,cAAc,oBAAiC;AACrE,SAAK,iBAAiB,QAAQ,cAA2B;AACzD,SAAK,iBAAiB,UAAU,gBAA6B;AAC7D,SAAK,iBAAiB,QAAQ,cAA2B;AACzD,SAAK,iBAAiB,OAAO,aAA0B;AACvD,SAAK,iBAAiB,YAAY,kBAA+B;AACjE,SAAK,iBAAiB,QAAQ,cAA2B;AACzD,SAAK,iBAAiB,cAAc,oBAAiC;AACrE,SAAK,iBAAiB,OAAO,aAA0B;AACvD,SAAK,iBAAiB,gBAAgB,sBAAmC;AACzE,SAAK,iBAAiB,aAAa,mBAAgC;AACnE,SAAK,iBAAiB,UAAU,gBAA6B;AAC7D,SAAK,iBAAiB,eAAe,qBAAkC;AACvE,SAAK,iBAAiB,KAAK,WAAwB;AACnD,SAAK,iBAAiB,QAAQ,cAA2B;AACzD,SAAK,iBAAiB,QAAQ,cAA2B;AACzD,SAAK,iBAAiB,SAAS,eAA4B;AAC3D,SAAK,iBAAiB,OAAO,aAA0B;AACvD,SAAK,iBAAiB,SAAS,eAA4B;AAC3D,SAAK,iBAAiB,QAAQ,cAA2B;AACzD,SAAK,iBAAiB,cAAc,oBAAiC;AACrE,SAAK,iBAAiB,SAAS,eAA4B;AAC3D,SAAK,iBAAiB,QAAQ,cAA2B;AAEzD,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,WAAO,UAAU;AAAA;AAAA;;;ACxCjB,oBAAwB;AAExB,IAAO,iBAAQ,cAAAC;", "names": ["TYPES", "HighlightJS"]}