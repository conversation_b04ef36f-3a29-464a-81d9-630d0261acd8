import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { 
  DashboardStatistics, 
  TrendData, 
  UpcomingEvent, 
  PopularEvent, 
  EventTypeDistribution 
} from './types';

/**
 * 获取仪表板统计数据
 */
export const getDashboardStatistics = (timeRange = '30d'): AxiosPromise<DashboardStatistics> => {
  return request({
    url: '/dashboard/statistics',
    method: 'get',
    params: { timeRange }
  });
};

/**
 * 获取趋势数据
 */
export const getTrendData = (timeRange = '30d', type = 'both'): AxiosPromise<TrendData> => {
  return request({
    url: '/dashboard/trends',
    method: 'get',
    params: { timeRange, type }
  });
};

/**
 * 获取即将开始的赛事
 */
export const getUpcomingEvents = (limit = 5): AxiosPromise<UpcomingEvent[]> => {
  return request({
    url: '/dashboard/upcoming-events',
    method: 'get',
    params: { limit }
  });
};

/**
 * 获取热门赛事排行
 */
export const getPopularEvents = (limit = 5): AxiosPromise<PopularEvent[]> => {
  return request({
    url: '/dashboard/popular-events',
    method: 'get',
    params: { limit }
  });
};

/**
 * 获取赛事类型分布
 */
export const getEventTypeDistribution = (): AxiosPromise<EventTypeDistribution[]> => {
  return request({
    url: '/dashboard/event-type-distribution',
    method: 'get'
  });
};