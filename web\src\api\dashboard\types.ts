// 统计数据接口
export interface DashboardStatistics {
  totalEvents: StatisticItem;
  totalRegistrations: StatisticItem;
  ongoingEvents: StatisticItem;
  completedEvents: StatisticItem;
}

export interface StatisticItem {
  value: number;
  trend: number; // 增长率百分比
}

// 趋势数据接口
export interface TrendData {
  dates: string[];
  registrations: number[];
  events: number[];
}

// 即将开始的赛事接口
export interface UpcomingEvent {
  id: number;
  name: string;
  location: string;
  startDate: string;
  participants: number;
  maxParticipants: number;
  status: 'open' | 'full' | 'closed' | 'ongoing' | 'completed';
}

// 热门赛事接口
export interface PopularEvent {
  id: number;
  name: string;
  registrations: number;
  maxParticipants: number;
  rank: number;
}

// 赛事类型分布接口
export interface EventTypeDistribution {
  name: string;
  value: number;
  percentage: number;
}