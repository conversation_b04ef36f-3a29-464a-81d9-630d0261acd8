import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import type {
  CertificateTemplateVO,
  CertificateTemplateForm,
  CertificateTemplateQuery,
  CertificateGenerationVO,
  CertificateGenerationForm,
  CertificateGenerationQuery,
  CertificateTemplateParamsVO,
  CertificateTemplateParamsForm,
  CertificateBatchTaskVO,
  CertificateBatchTaskForm,
  CertificateBatchTaskQuery,
  BatchGenerateCertificatesRequest,
  CertificatePreviewRequest,
  CertificateDownloadResponse,
  BatchDownloadResponse,
  CertificateTemplateStatsVO,
  CertificateGenerationStatsVO,
  CertificateTemplateParamsQuery
} from './types';

// ========== 证书模板管理 API ==========

/**
 * 查询证书模板列表
 */
export function listCertificateTemplates(query : CertificateTemplateQuery) : AxiosPromise<CertificateTemplateVO[]> {
  return request({
    url: '/event/certificate/template/list',
    method: 'get',
    params: query
  });
}

/**
 * 查询证书模板详情
 */
export function getCertificateTemplate(id : number) : AxiosPromise<CertificateTemplateVO> {
  return request({
    url: `/event/certificate/template/${id}`,
    method: 'get'
  });
}

/**
 * 新增证书模板
 */
export function addCertificateTemplate(data : CertificateTemplateForm) : AxiosPromise<any> {
  return request({
    url: '/event/certificate/template',
    method: 'post',
    data
  });
}

/**
 * 修改证书模板
 */
export function updateCertificateTemplate(data : CertificateTemplateForm) : AxiosPromise<any> {
  return request({
    url: '/event/certificate/template',
    method: 'put',
    data
  });
}

/**
 * 删除证书模板
 */
export function delCertificateTemplate(ids : number | number[]) : AxiosPromise<any> {
  return request({
    url: `/event/certificate/template/${ids}`,
    method: 'delete'
  });
}

/**
 * 设置默认模板
 */
export function setDefaultCertificateTemplate(templateId : number) {
  return request({
    url: `/event/certificate/template/${templateId}/setDefault`,
    method: 'put'
  });
}

/**
 * 更新模板状态
 */
export function updateCertificateTemplateStatus(templateId : number, isEnabled : number) {
  return request({
    url: `/event/certificate/template/${templateId}/status`,
    method: 'put',
    data: { isEnabled }
  });
}

/**
 * 复制证书模板
 */
export function copyCertificateTemplate(templateId : number, newTemplateName ?: string) {
  return request({
    url: `/event/certificate/template/${templateId}/copy`,
    method: 'post',
    data: { newTemplateName }
  });
}

/**
 * 获取模板统计信息
 */
export function getCertificateTemplateStats(templateId : number) {
  return request<CertificateTemplateStatsVO>({
    url: `/event/certificate/template/${templateId}/stats`,
    method: 'get'
  });
}

// ========== 模板参数配置 API ==========

/**
 * 查询模板详情
 */
export function getParamList(query : CertificateTemplateParamsQuery) : AxiosPromise<CertificateTemplateParamsVO[]> {
  return request({
    url: `/event/certificate/template/paramList`,
    method: 'get',
    params: query
  });
}
/**
 * 查询模板详情
 */
export function getCertificateTemplateParams( id : number) : AxiosPromise<CertificateTemplateParamsVO[]> {
  return request({
    url: `/event/certificate/template/params/${id}`,
    method: 'get'
  });
}

/**
 * 保存模板参数配置
 */
export function saveCertificateTemplateParams(templateId : number, params : CertificateTemplateParamsForm[]) {
  return request({
    url: `/event/certificate/template/${templateId}/params`,
    method: 'post',
    data: params
  });
}
export function updateCertificateTemplateParams(params : CertificateTemplateParamsForm[]) {
  return request({
    url: `/event/certificate/template/params`,
    method: 'put',
    data: params
  });
}
/**
 * 删除模板参数
 */
export function delCertificateTemplateParam(paramId : number) {
  return request({
    url: `/event/certificate/template/param/${paramId}`,
    method: 'delete'
  });
}

// ========== 证书生成记录 API ==========

/**
 * 查询证书生成记录列表
 */
export function listCertificateGenerations(query : CertificateGenerationQuery) : AxiosPromise<CertificateGenerationVO[]> {
  return request({
    url: '/event/certificate/generation/list',
    method: 'get',
    params: query
  });
}

/**
 * 查询证书生成记录详情
 */
export function getCertificateGeneration(id : number) : AxiosPromise<CertificateGenerationVO> {
  return request({
    url: `/event/certificate/generation/${id}`,
    method: 'get'
  });
}

/**
 * 删除证书生成记录
 */
export function delCertificateGeneration(ids : number | number[]) {
  return request({
    url: `/event/certificate/generation/delete/${ids}`,
    method: 'delete'
  });
}

/**
 * 重新生成证书
 */
export function regenerateCertificate(generationId : number) {
  return request({
    url: `/event/certificate/generation/${generationId}/regenerate`,
    method: 'post'
  });
}

/**
 * 批量重新生成失败的证书
 */
export function batchRegenerateFailedCertificates(eventId : number) {
  return request({
    url: `/event/certificate/generation/batch/regenerate`,
    method: 'post',
    data: { eventId }
  });
}

// ========== 证书生成 API ==========

/**
 * 批量生成证书
 */
export function batchGenerateCertificates(data : BatchGenerateCertificatesRequest) {
  return request({
    url: '/event/certificate/generation/batch/generate',
    method: 'post',
    data
  });
}

/**
 * 单个生成证书
 */
export function generateSingleCertificate(data : {
  templateId : number;
  registrationId : number;
  generationParams : Record<string, any>;
}) {
  return request({
    url: '/event/certificate/generation/single',
    method: 'post',
    data
  });
}

/**
 * 预览证书模板
 */
export function previewCertificateTemplate(data : CertificatePreviewRequest) {
  return request<{ previewUrl : string }>({
    url: '/event/certificate/template/preview',
    method: 'post',
    data
  });
}

// ========== 证书下载 API ==========

/**
 * 下载单个证书
 */
export function downloadCertificate(generationId : number) {
  return request<CertificateDownloadResponse>({
    url: `/event/certificate/generation/download/${generationId}`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 批量下载事项所有证书
 */
export function batchDownloadCertificates(eventId : number, templateId ?: number) {
  return request<BatchDownloadResponse>({
    url: `/event/certificate/generation/batch/download`,
    method: 'post',
    data: { eventId, templateId },
    responseType: 'blob'
  });
}

/**
 * 获取证书下载链接
 */
export function getCertificateDownloadUrl(generationId : number) {
  return request<{ downloadUrl : string; expires : number }>({
    url: `/event/certificate/generation/${generationId}/downloadUrl`,
    method: 'get'
  });
}

// ========== 批量任务管理 API ==========

/**
 * 查询批量任务列表
 */
export function listCertificateBatchTasks(query : CertificateBatchTaskQuery) : AxiosPromise<CertificateBatchTaskVO[]> {
  return request({
    url: '/event/certificate/batch/task/list',
    method: 'get',
    params: query
  });
}

/**
 * 查询批量任务详情
 */
export function getCertificateBatchTask(taskId : number) {
  return request<CertificateBatchTaskVO>({
    url: `/event/certificate/batch/task/${taskId}`,
    method: 'get'
  });
}

/**
 * 取消批量任务
 */
export function cancelCertificateBatchTask(taskId : number) {
  return request({
    url: `/event/certificate/batch/task/${taskId}/cancel`,
    method: 'put'
  });
}

/**
 * 删除批量任务
 */
export function delCertificateBatchTask(ids : number | number[]) {
  return request({
    url: `/event/certificate/batch/task/${ids}`,
    method: 'delete'
  });
}

/**
 * 获取任务进度
 */
export function getCertificateBatchTaskProgress(taskId : number) {
  return request<{
    taskId : number;
    progress : number;
    status : number;
    totalCount : number;
    successCount : number;
    failedCount : number;
    currentStep : string;
    estimatedTime : number;
  }>({
    url: `/event/certificate/batch/task/${taskId}/progress`,
    method: 'get'
  });
}

// ========== 统计分析 API ==========

/**
 * 获取事项证书生成统计
 */
export function getCertificateGenerationStats(eventId : number) {
  return request<CertificateGenerationStatsVO>({
    url: `/event/certificate/stats/${eventId}`,
    method: 'get'
  });
}

/**
 * 获取模板使用统计
 */
export function getCertificateTemplateUsageStats(eventId : number) {
  return request<CertificateTemplateStatsVO[]>({
    url: `/event/certificate/template/stats/${eventId}`,
    method: 'get'
  });
}

// ========== 导入导出 API ==========

/**
 * 导出证书模板
 */
export function exportCertificateTemplates(eventId : number) {
  return request({
    url: `/event/certificate/template/export`,
    method: 'post',
    data: { eventId },
    responseType: 'blob'
  });
}

/**
 * 导入证书模板
 */
export function importCertificateTemplates(eventId : number, file : File) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('eventId', eventId.toString());

  return request({
    url: `/event/certificate/template/import`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 导出证书生成记录
 */
export function exportCertificateGenerations(query : CertificateGenerationQuery) {
  return request({
    url: `/event/certificate/generation/export`,
    method: 'post',
    data: query,
    responseType: 'blob'
  });
}

// ========== 其他工具 API ==========

/**
 * 验证模板参数
 */
export function validateTemplateParams(templateId : number, params : Record<string, any>) {
  return request<{
    valid : boolean;
    errors : Array<{
      paramKey : string;
      message : string;
    }>;
  }>({
    url: `/event/certificate/template/${templateId}/validate`,
    method: 'post',
    data: params
  });
}

/**
 * 获取可用变量列表
 */
export function getCertificateVariables(eventId : number) {
  return request<Array<{
    key : string;
    label : string;
    description : string;
    example : string;
  }>>({
    url: `/event/certificate/variables/${eventId}`,
    method: 'get'
  });
}

/**
 * 清理过期的证书文件
 */
export function cleanExpiredCertificates(eventId : number, days : number = 30) {
  return request({
    url: `/event/certificate/clean`,
    method: 'post',
    data: { eventId, days }
  });
}
