export interface CertificateTemplateVO {
  id?: number;
  tenantId?: string;
  templateName: string;
  eventId: number;
  templateImage: string;
  templateConfig?: string;
  isEnabled: number;
  isDefault: number;
  templateType: number;
  sortOrder?: number;
  createDept?: number;
  createBy?: number;
  createTime?: string;
  updateBy?: number;
  updateTime?: string;
  remark?: string;
  delFlag?: number;
}

export interface CertificateTemplateForm {
  id?: number;
  templateName: string;
  eventId?: number;
  templateImage: string;
  templateConfig?: string;
  isEnabled: number;
  isDefault: number;
  templateType: number;
  sortOrder?: number;
  remark?: string;
}

export interface CertificateTemplateQuery extends PageQuery {
  eventId?: number;
  templateName?: string;
  isEnabled?: number;
  templateType?: number;
}

export interface CertificateGenerationVO {
  id?: number;
  tenantId?: string;
  eventId: number;
  templateId: number;
  registrationId: number;
  participantName: string;
  participantPhone?: string;
  certificateUrl?: string;
  generationStatus: number;
  generationParams?: string;
  errorMessage?: string;
  fileSize?: number;
  downloadCount?: number;
  lastDownloadTime?: string;
  createDept?: number;
  createBy?: number;
  createTime?: string;
  updateBy?: number;
  updateTime?: string;
  remark?: string;
  delFlag?: number;
}

export interface CertificateGenerationForm {
  id?: number;
  eventId: number;
  templateId: number;
  registrationId: number;
  participantName: string;
  participantPhone?: string;
  generationParams?: string;
  remark?: string;
}

export interface CertificateGenerationQuery extends PageQuery {
  eventId?: number;
  templateId?: number;
  registrationId?: number;
  generationStatus?: number;
  participantName?: string;
}
export interface CertificateTemplateParamsQuery extends PageQuery {
  templateId?: number;
}
export interface CertificateTemplateParamsVO {
  id?: number;
  tenantId?: string;
  templateId: number;
  paramKey: string;
  paramLabel: string;
  paramType: string;
  paramOptions?: string;
  defaultValue?: string;
  isRequired: number;
  positionX: number;
  positionY: number;
  fontSize: number;
  fontColor?: string;
  fontFamily?: string;
  sortOrder?: number;
  createDept?: number;
  createBy?: number;
  createTime?: string;
  updateBy?: number;
  updateTime?: string;
  remark?: string;
  delFlag?: number;
}

export interface CertificateTemplateParamsForm {
  id?: number;
  templateId: number;
  paramKey: string;
  paramLabel: string;
  paramType: string;
  paramOptions?: string;
  defaultValue?: string;
  isRequired: number;
  positionX: number;
  positionY: number;
  fontSize: number;
  fontColor?: string;
  fontFamily?: string;
  sortOrder?: number;
  remark?: string;
}

export interface CertificateBatchTaskVO {
  id?: number;
  tenantId?: string;
  taskName: string;
  eventId: number;
  templateId: number;
  totalCount?: number;
  successCount?: number;
  failedCount?: number;
  taskStatus: number;
  startTime?: string;
  endTime?: string;
  errorMessage?: string;
  generationParams?: string;
  selectedRegistrations?: string;
  excludedRegistrations?: string;
  createDept?: number;
  createBy?: number;
  createTime?: string;
  updateBy?: number;
  updateTime?: string;
  remark?: string;
  delFlag?: number;
}

export interface CertificateBatchTaskForm {
  taskName: string;
  eventId: number;
  templateId: number;
  generationParams?: string;
  selectedRegistrations?: number[];
  excludedRegistrations?: number[];
  remark?: string;
}

export interface CertificateBatchTaskQuery extends PageQuery {
  eventId?: number;
  templateId?: number;
  taskStatus?: number;
  taskName?: string;
}

// 批量生成请求
export interface BatchGenerateCertificatesRequest {
  eventId: number;
  templateId: number;
  selectedRegistrations: number[];
  excludedRegistrations?: number[];
  generationParams: Record<string, any>;
  taskName?: string;
}

// 模板预览请求
export interface CertificatePreviewRequest {
  templateId: number;
  generationParams: Record<string, any>;
}

// 证书下载响应
export interface CertificateDownloadResponse {
  fileName: string;
  fileUrl: string;
  fileSize: number;
}

// 批量下载响应
export interface BatchDownloadResponse {
  downloadUrl: string;
  fileName: string;
  totalFiles: number;
  totalSize: number;
}

// 模板统计信息
export interface CertificateTemplateStatsVO {
  templateId: number;
  templateName: string;
  totalGenerations: number;
  successGenerations: number;
  failedGenerations: number;
  totalDownloads: number;
  lastGenerationTime?: string;
}

// 证书生成统计
export interface CertificateGenerationStatsVO {
  eventId: number;
  totalTemplates: number;
  totalGenerations: number;
  successGenerations: number;
  failedGenerations: number;
  pendingGenerations: number;
  totalDownloads: number;
  totalFileSize: number;
}
