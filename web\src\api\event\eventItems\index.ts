import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { EventItemsVO, EventItemsForm, EventItemsQuery } from '@/api/event/eventItems/types';

/**
 * 查询赛事项目列表
 * @param query
 * @returns {*}
 */

export const listEventItems = (query?: EventItemsQuery): AxiosPromise<EventItemsVO[]> => {
  return request({
    url: '/event/eventItems/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询赛事项目详细
 * @param id
 */
export const getEventItems = (id: string | number): AxiosPromise<EventItemsVO> => {
  return request({
    url: '/event/eventItems/' + id,
    method: 'get'
  });
};

/**
 * 新增赛事项目
 * @param data
 */
export const addEventItems = (data: EventItemsForm) => {
  return request({
    url: '/event/eventItems',
    method: 'post',
    data: data
  });
};

/**
 * 修改赛事项目
 * @param data
 */
export const updateEventItems = (data: EventItemsForm) => {
  return request({
    url: '/event/eventItems',
    method: 'put',
    data: data
  });
};

/**
 * 删除赛事项目
 * @param id
 */
export const delEventItems = (id: string | number | Array<string | number>) => {
  return request({
    url: '/event/eventItems/' + id,
    method: 'delete'
  });
};
