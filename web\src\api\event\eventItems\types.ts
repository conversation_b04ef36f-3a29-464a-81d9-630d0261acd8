export interface EventItemsVO {
  /**
   * 项目ID
   */
  id: string | number;

  /**
   * 所属赛事ID
   */
  eventId: string | number;

  /**
   * 项目名称
   */
  name: string;

  /**
   * 项目描述
   */
  description: string;

  /**
   * 最大参与人数
   */
  maxParticipants: number;

  /**
   * 当前报名人数
   */
  currentParticipants: number;

  /**
   * 额外费用
   */
  additionalFee: number;

  /**
   * 最小年龄限制
   */
  ageLimitMin: number;

  /**
   * 最大年龄限制
   */
  ageLimitMax: number;

  /**
   * 性别限制
   */
  genderLimit: number;

  /**
   * 特殊要求
   */
  requirements: string;

  /**
   * 排序
   */
  sortOrder: number;

  /**
   * 备注
   */
  remark: string;

}

export interface EventItemsForm extends BaseEntity {
  /**
   * 项目ID
   */
  id?: string | number;

  /**
   * 所属赛事ID
   */
  eventId?: string | number;

  /**
   * 项目名称
   */
  name?: string;

  /**
   * 项目描述
   */
  description?: string;

  /**
   * 最大参与人数
   */
  maxParticipants?: number;

  /**
   * 当前报名人数
   */
  currentParticipants?: number;

  /**
   * 额外费用
   */
  additionalFee?: number;

  /**
   * 最小年龄限制
   */
  ageLimitMin?: number;

  /**
   * 最大年龄限制
   */
  ageLimitMax?: number;

  /**
   * 性别限制
   */
  genderLimit?: number;

  /**
   * 特殊要求
   */
  requirements?: string;

  /**
   * 排序
   */
  sortOrder?: number;

  /**
   * 备注
   */
  remark?: string;

}

export interface EventItemsQuery extends PageQuery {

  /**
   * 所属赛事ID
   */
  eventId?: string | number;

  /**
   * 项目名称
   */
  name?: string;

  /**
   * 项目描述
   */
  description?: string;

  /**
   * 最大参与人数
   */
  maxParticipants?: number;

  /**
   * 当前报名人数
   */
  currentParticipants?: number;

  /**
   * 额外费用
   */
  additionalFee?: number;

  /**
   * 最小年龄限制
   */
  ageLimitMin?: number;

  /**
   * 最大年龄限制
   */
  ageLimitMax?: number;

  /**
   * 性别限制
   */
  genderLimit?: number;

  /**
   * 特殊要求
   */
  requirements?: string;

  /**
   * 排序
   */
  sortOrder?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



