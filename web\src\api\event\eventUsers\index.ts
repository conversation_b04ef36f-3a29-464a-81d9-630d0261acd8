import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { EventUsersVO, EventUsersForm, EventUsersQuery } from '@/api/event/eventUsers/types';

/**
 * 查询赛事用户列表列表
 * @param query
 * @returns {*}
 */

export const listEventUsers = (query?: EventUsersQuery): AxiosPromise<EventUsersVO[]> => {
  return request({
    url: '/event/eventUsers/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询赛事用户列表详细
 * @param id
 */
export const getEventUsers = (id: string | number): AxiosPromise<EventUsersVO> => {
  return request({
    url: '/event/eventUsers/' + id,
    method: 'get'
  });
};

/**
 * 新增赛事用户列表
 * @param data
 */
export const addEventUsers = (data: EventUsersForm) => {
  return request({
    url: '/event/eventUsers',
    method: 'post',
    data: data
  });
};

/**
 * 修改赛事用户列表
 * @param data
 */
export const updateEventUsers = (data: EventUsersForm) => {
  return request({
    url: '/event/eventUsers',
    method: 'put',
    data: data
  });
};

/**
 * 删除赛事用户列表
 * @param id
 */
export const delEventUsers = (id: string | number | Array<string | number>) => {
  return request({
    url: '/event/eventUsers/' + id,
    method: 'delete'
  });
};
