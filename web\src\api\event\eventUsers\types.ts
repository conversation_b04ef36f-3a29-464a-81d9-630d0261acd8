export interface EventUsersVO {
  /**
   * 用户ID
   */
  id: string | number;

  /**
   * 微信OpenID
   */
  openid: string | number;

  /**
   * 微信UnionID
   */
  unionid: string | number;

  /**
   * 昵称
   */
  nickname: string;

  /**
   * 头像URL
   */
  avatar: string;

  /**
   * 手机号
   */
  phone: string;

  /**
   * 邮箱
   */
  email: string;

  /**
   * 真实姓名
   */
  realName: string;

  /**
   * 身份证号存储)
   */
  idCard: string | number;

  /**
   * 性别
   */
  gender: number;

  /**
   * 生日
   */
  birthday: string;

  /**
   * 地址
   */
  address: string;

  /**
   * 紧急联系人
   */
  emergencyContact: string;

  /**
   * 用户状态
   */
  status: number;

  /**
   * 备注
   */
  remark: string;

}

export interface EventUsersForm extends BaseEntity {
  /**
   * 用户ID
   */
  id?: string | number;

  /**
   * 微信OpenID
   */
  openid?: string | number;

  /**
   * 微信UnionID
   */
  unionid?: string | number;

  /**
   * 昵称
   */
  nickname?: string;

  /**
   * 头像URL
   */
  avatar?: string;

  /**
   * 手机号
   */
  phone?: string;

  /**
   * 邮箱
   */
  email?: string;

  /**
   * 真实姓名
   */
  realName?: string;

  /**
   * 身份证号存储)
   */
  idCard?: string | number;

  /**
   * 性别
   */
  gender?: number;

  /**
   * 生日
   */
  birthday?: string;

  /**
   * 地址
   */
  address?: string;

  /**
   * 紧急联系人
   */
  emergencyContact?: string;

  /**
   * 用户状态
   */
  status?: number;

  /**
   * 备注
   */
  remark?: string;

}

export interface EventUsersQuery extends PageQuery {

  /**
   * 微信OpenID
   */
  openid?: string | number;

  /**
   * 微信UnionID
   */
  unionid?: string | number;

  /**
   * 昵称
   */
  nickname?: string;

  /**
   * 头像URL
   */
  avatar?: string;

  /**
   * 手机号
   */
  phone?: string;

  /**
   * 邮箱
   */
  email?: string;

  /**
   * 真实姓名
   */
  realName?: string;

  /**
   * 身份证号存储)
   */
  idCard?: string | number;

  /**
   * 性别
   */
  gender?: number;

  /**
   * 生日
   */
  birthday?: string;

  /**
   * 地址
   */
  address?: string;

  /**
   * 紧急联系人
   */
  emergencyContact?: string;

  /**
   * 用户状态
   */
  status?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



