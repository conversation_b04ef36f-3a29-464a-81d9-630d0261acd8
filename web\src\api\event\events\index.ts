import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { EventsVO, EventsForm, EventsQuery } from '@/api/event/events/types';

/**
 * 查询赛事列表列表
 * @param query
 * @returns {*}
 */

export const listEvents = (query?: EventsQuery): AxiosPromise<EventsVO[]> => {
  return request({
    url: '/event/events/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询赛事列表详细
 * @param id
 */
export const getEvents = (id: string | number): AxiosPromise<EventsVO> => {
  return request({
    url: '/event/events/' + id,
    method: 'get'
  });
};

/**
 * 新增赛事列表
 * @param data
 */
export const addEvents = (data: EventsForm) => {
  return request({
    url: '/event/events',
    method: 'post',
    data: data
  });
};

/**
 * 修改赛事列表
 * @param data
 */
export const updateEvents = (data: EventsForm) => {
  return request({
    url: '/event/events',
    method: 'put',
    data: data
  });
};

/**
 * 删除赛事列表
 * @param id
 */
export const delEvents = (id: string | number | Array<string | number>) => {
  return request({
    url: '/event/events/' + id,
    method: 'delete'
  });
};

/**
 * 修改赛事状态
 * @param id 赛事ID
 * @param status 状态：0-未开始，1-报名中，2-进行中，3-已结束，4-已取消
 */
export const updateEventStatus = (id: string | number, status: number) => {
  return request({
    url: `/event/events/${id}/status/${status}`,
    method: 'put'
  });
};

/**
 * 发布/取消发布赛事
 * @param id 赛事ID
 * @param publish 是否发布：true-发布，false-取消发布
 */
export const publishEvent = (id: string | number, publish: boolean) => {
  return request({
    url: `/event/events/${id}/publish/${publish}`,
    method: 'put'
  });
};

/**
 * 获取赛事统计信息
 * @param id 赛事ID
 */
export const getEventStatistics = (id: string | number) => {
  return request({
    url: `/event/events/${id}/statistics`,
    method: 'get'
  });
};

/**
 * 导出赛事列表
 * @param query 查询参数
 */
export const exportEvents = (query?: EventsQuery) => {
  return request({
    url: '/event/events/export',
    method: 'post',
    params: query,
    responseType: 'blob'
  });
};

/**
 * 查询赛事二维码地址
 * @param id
 */
export const getEventsQRCodeUrl = (id: string | number) => {
  return request({
    url: `event/events/${id}/qr-code`,
    method: 'get'
  });
};
