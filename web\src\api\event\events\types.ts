export interface EventsVO {
  /**
   * 赛事ID
   */
  id: string | number;

  /**
   * 赛事标题
   */
  title: string;

  /**
   * 赛事描述
   */
  description: string;

  /**
   * 赛事开始时间
   */
  startTime: string;

  /**
   * 赛事结束时间
   */
  endTime: string;

  /**
   * 举办地点
   */
  location: string;

  /**
   * 海报图片URL
   */
  poster: string;
  posterUrl:string;

  /**
   * 比赛规则(JSON数组)
   */
  rules: string;

  /**
   * 奖励设置(JSON数组)
   */
  rewards: string;

  /**
   * 报名截止时间
   */
  registrationDeadline: string;

  /**
   * 报名费用
   */
  fee: number;

  /**
   * 赛事状态
   */
  status: number;

  /**
   * 主办方
   */
  organizer: string;

  /**
   * 联系方式
   */
  contactInfo: string;

  /**
   * 最大参与人数限制
   */
  maxParticipants: number;

  /**
   * 当前报名人数
   */
  currentParticipants: number;

  /**
   * 备注
   */
  remark: string;

}

export interface EventsForm extends BaseEntity {
  /**
   * 赛事ID
   */
  id?: string | number;

  /**
   * 赛事标题
   */
  title?: string;

  /**
   * 赛事描述
   */
  description?: string;

  /**
   * 赛事开始时间
   */
  startTime?: string;

  /**
   * 赛事结束时间
   */
  endTime?: string;

  /**
   * 举办地点
   */
  location?: string;

  /**
   * 海报图片URL
   */
  poster?: string;

  /**
   * 海报图片URL
   */
  posterUrl?: string;

  /**
   * 比赛规则(JSON数组)
   */
  rules?: string;

  /**
   * 奖励设置(JSON数组)
   */
  rewards?: string;

  /**
   * 报名截止时间
   */
  registrationDeadline?: string;

  /**
   * 报名费用
   */
  fee?: number;

  /**
   * 赛事状态
   */
  status?: number;

  /**
   * 主办方
   */
  organizer?: string;

  /**
   * 联系方式
   */
  contactInfo?: string;

  /**
   * 最大参与人数限制
   */
  maxParticipants?: number;

  /**
   * 当前报名人数
   */
  currentParticipants?: number;

  /**
   * 备注
   */
  remark?: string;

}

export interface EventsQuery extends PageQuery {

  /**
   * 赛事标题
   */
  title?: string;

  /**
   * 赛事描述
   */
  description?: string;

  /**
   * 赛事开始时间
   */
  startTime?: string;

  /**
   * 赛事结束时间
   */
  endTime?: string;

  /**
   * 举办地点
   */
  location?: string;

  /**
   * 海报图片URL
   */
  poster?: string;

  /**
   * 比赛规则(JSON数组)
   */
  rules?: string;

  /**
   * 奖励设置(JSON数组)
   */
  rewards?: string;

  /**
   * 报名截止时间
   */
  registrationDeadline?: string;

  /**
   * 报名费用
   */
  fee?: number;

  /**
   * 赛事状态
   */
  status?: number;

  /**
   * 主办方
   */
  organizer?: string;

  /**
   * 联系方式
   */
  contactInfo?: string;

  /**
   * 最大参与人数限制
   */
  maxParticipants?: number;

  /**
   * 当前报名人数
   */
  currentParticipants?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



