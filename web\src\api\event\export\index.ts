import request from '@/utils/request';

/**
 * 数据导出API
 * 提供各种条件的数据导出功能
 */

/**
 * 导出所有报名数据
 */
export const exportAllRegistrations = () => {
  return request({
    url: '/event/export/registrations/all',
    method: 'post',
    responseType: 'blob'
  });
};

/**
 * 导出已支付用户数据
 */
export const exportPaidRegistrations = () => {
  return request({
    url: '/event/export/registrations/paid',
    method: 'post',
    responseType: 'blob'
  });
};

/**
 * 导出未支付用户数据
 */
export const exportUnpaidRegistrations = () => {
  return request({
    url: '/event/export/registrations/unpaid',
    method: 'post',
    responseType: 'blob'
  });
};

/**
 * 根据赛事ID导出报名数据
 * @param eventId 赛事ID
 */
export const exportRegistrationsByEvent = (eventId: string | number) => {
  return request({
    url: `/event/export/registrations/event/${eventId}`,
    method: 'post',
    responseType: 'blob'
  });
};

/**
 * 根据关键词搜索并导出报名数据
 * @param keyword 关键词
 */
export const exportRegistrationsByKeyword = (keyword: string) => {
  return request({
    url: '/event/export/registrations/search',
    method: 'post',
    params: { keyword },
    responseType: 'blob'
  });
};

/**
 * 导出赛事列表数据
 */
export const exportAllEvents = () => {
  return request({
    url: '/event/export/events/all',
    method: 'post',
    responseType: 'blob'
  });
};

/**
 * 根据状态导出赛事数据
 * @param status 赛事状态
 */
export const exportEventsByStatus = (status: number) => {
  return request({
    url: `/event/export/events/status/${status}`,
    method: 'post',
    responseType: 'blob'
  });
};

/**
 * 导出综合统计报表
 */
export const exportComprehensiveStatistics = () => {
  return request({
    url: '/event/export/statistics/comprehensive',
    method: 'post',
    responseType: 'blob'
  });
};

/**
 * 获取导出数据预览（返回数据行数）
 * @param type 导出类型：all、paid、unpaid、event
 * @param param 额外参数（如赛事ID、关键词等）
 */
export const getExportPreview = (type: string, param?: string) => {
  return request({
    url: '/event/export/preview',
    method: 'get',
    params: { type, param }
  });
};