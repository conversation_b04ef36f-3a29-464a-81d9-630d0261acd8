// 表单配置API服务
import request from '@/utils/request'
import type { FormConfig, FormField, FormTemplate, FormBuilderData } from '@/components/FormBuilder/types/form'

// 表单配置相关接口
export interface FormConfigQuery {
  pageNum?: number
  pageSize?: number
  eventId?: number | string
  formName?: string
  status?: number
}

export interface FormConfigVO {
  id: number
  eventId: number
  formName: string
  formDescription: string
  formConfig: FormConfig
  fields: FormField[]
  isDefault: number
  status: number
  sortOrder: number
  createTime: string
  updateTime: string
  createBy: string
  updateBy: string
  remark: string
}

// 获取表单配置列表
export function listFormConfigs(query: FormConfigQuery) {
  return request({
    url: '/event/form/config/list',
    method: 'get',
    params: query
  })
}

// 获取表单配置详情
export function getFormConfig(id: number | string) {
  return request({
    url: `/event/form/config/${id}`,
    method: 'get'
  })
}

// 根据赛事ID获取表单配置
export function getFormConfigByEventId(eventId: number | string) {
  return request({
    url: `/event/form/config/event/${eventId}`,
    method: 'get'
  })
}

// 创建表单配置
export function createFormConfig(data: {
  eventId: number | string
  formName: string
  formDescription?: string
  formConfig: FormConfig
  fields: FormField[]
}) {
  return request({
    url: '/event/form/config',
    method: 'post',
    data
  })
}

// 更新表单配置
export function updateFormConfig(id: number | string, data: {
  formName?: string
  formDescription?: string
  formConfig?: FormConfig
  fields?: FormField[]
}) {
  return request({
    url: `/event/form/config/${id}`,
    method: 'put',
    data
  })
}

// 删除表单配置
export function deleteFormConfig(ids: (number | string)[]) {
  return request({
    url: `/event/form/config/${ids.join(',')}`,
    method: 'delete'
  })
}

// 复制表单配置
export function copyFormConfig(id: number | string, data: {
  eventId: number | string
  formName: string
}) {
  return request({
    url: `/event/form/config/${id}/copy`,
    method: 'post',
    data
  })
}

// 表单字段管理接口

// 获取字段列表
export function listFormFields(formConfigId: number | string) {
  return request({
    url: '/event/form/field/list',
    method: 'get',
    params: { formConfigId }
  })
}

// 创建字段
export function createFormField(data: Omit<FormField, 'id'>) {
  return request({
    url: '/event/form/field',
    method: 'post',
    data
  })
}

// 更新字段
export function updateFormField(id: number | string, data: Partial<FormField>) {
  return request({
    url: `/event/form/field/${id}`,
    method: 'put',
    data
  })
}

// 删除字段
export function deleteFormField(ids: (number | string)[]) {
  return request({
    url: `/event/form/field/${ids.join(',')}`,
    method: 'delete'
  })
}

// 批量更新字段排序
export function updateFieldsSort(fields: { id: number | string; sortOrder: number }[]) {
  return request({
    url: '/event/form/field/sort',
    method: 'put',
    data: { fields }
  })
}

// 表单模板管理接口

// 获取模板列表
export function listFormTemplates(query: {
  pageNum?: number
  pageSize?: number
  templateName?: string
  templateType?: 'system' | 'custom'
  isPublic?: number
  status?: number
}) {
  return request({
    url: '/event/form/template/list',
    method: 'get',
    params: query
  })
}

// 获取模板详情
export function getFormTemplate(id: number | string) {
  return request({
    url: `/event/form/template/${id}`,
    method: 'get'
  })
}

// 获取默认模板
export function getDefaultFormTemplate() {
  return request({
    url: '/event/form/template/default',
    method: 'get'
  })
}

// 创建模板
export function createFormTemplate(data: {
  templateName: string
  templateDescription?: string
  templateConfig: FormBuilderData
  templateType?: 'system' | 'custom'
  isPublic?: boolean
}) {
  return request({
    url: '/event/form/template',
    method: 'post',
    data
  })
}

// 应用模板到表单
export function applyTemplate(templateId: number | string, data: {
  eventId: number | string
  formName: string
}) {
  return request({
    url: `/event/form/template/${templateId}/apply`,
    method: 'post',
    data
  })
}

// 表单预览和验证接口

// 预览表单
export function previewForm(data: FormBuilderData) {
  return request({
    url: '/event/form/preview',
    method: 'post',
    data
  })
}

// 验证表单配置
export function validateFormConfig(data: FormBuilderData) {
  return request({
    url: '/event/form/validate',
    method: 'post',
    data
  })
}

// 获取字段类型配置
export function getFieldTypes() {
  return request({
    url: '/event/form/field-types',
    method: 'get'
  })
}

// 表单数据处理接口

// 根据表单配置处理提交数据
export function processFormData(data: {
  formConfigId: number | string
  formData: Record<string, any>
}) {
  return request({
    url: '/event/form/process-data',
    method: 'post',
    data
  })
}
