import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PaymentsVO, PaymentsForm, PaymentsQuery } from '@/api/event/payments/types';

/**
 * 查询支付记录列表
 * @param query
 * @returns {*}
 */

export const listPayments = (query?: PaymentsQuery): AxiosPromise<PaymentsVO[]> => {
  return request({
    url: '/event/payments/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询支付记录详细
 * @param id
 */
export const getPayments = (id: string | number): AxiosPromise<PaymentsVO> => {
  return request({
    url: '/event/payments/' + id,
    method: 'get'
  });
};

/**
 * 新增支付记录
 * @param data
 */
export const addPayments = (data: PaymentsForm) => {
  return request({
    url: '/event/payments',
    method: 'post',
    data: data
  });
};

/**
 * 修改支付记录
 * @param data
 */
export const updatePayments = (data: PaymentsForm) => {
  return request({
    url: '/event/payments',
    method: 'put',
    data: data
  });
};

/**
 * 删除支付记录
 * @param id
 */
export const delPayments = (id: string | number | Array<string | number>) => {
  return request({
    url: '/event/payments/' + id,
    method: 'delete'
  });
};

/**
 * 导出支付记录列表
 * @param query 查询参数
 */
export const exportPayments = (query?: PaymentsQuery) => {
  return request({
    url: '/event/payments/export',
    method: 'post',
    params: query,
    responseType: 'blob'
  });
};
