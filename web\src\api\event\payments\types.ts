export interface PaymentsVO {
  /**
   * 支付记录ID
   */
  id: string | number;

  /**
   * 报名ID
   */
  registrationId: string | number;

  /**
   * 用户ID
   */
  userId: string | number;

  /**
   * 支付金额
   */
  amount: number;

  /**
   * 支付方式
   */
  paymentMethod: string;

  /**
   * 支付渠道
   */
  paymentChannel: string;

  /**
   * 第三方交易号
   */
  transactionId: string | number;

  /**
   * 商户订单号
   */
  outTradeNo: string;

  /**
   * 支付状态
   */
  status: number;

  /**
   * 支付成功时间
   */
  paidAt: string | number;

  /**
   * 支付通知数据
   */
  notifyData: string;

  /**
   * 退款金额
   */
  refundAmount: number;

  /**
   * 退款原因
   */
  refundReason: string;

  /**
   * 退款时间
   */
  refundedAt: string;

  /**
   * 备注
   */
  remark: string;

}

export interface PaymentsForm extends BaseEntity {
  /**
   * 支付记录ID
   */
  id?: string | number;

  /**
   * 报名ID
   */
  registrationId?: string | number;

  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 支付金额
   */
  amount?: number;

  /**
   * 支付方式
   */
  paymentMethod?: string;

  /**
   * 支付渠道
   */
  paymentChannel?: string;

  /**
   * 第三方交易号
   */
  transactionId?: string | number;

  /**
   * 商户订单号
   */
  outTradeNo?: string;

  /**
   * 支付状态
   */
  status?: number;

  /**
   * 支付成功时间
   */
  paidAt?: string | number;

  /**
   * 支付通知数据
   */
  notifyData?: string;

  /**
   * 退款金额
   */
  refundAmount?: number;

  /**
   * 退款原因
   */
  refundReason?: string;

  /**
   * 退款时间
   */
  refundedAt?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface PaymentsQuery extends PageQuery {

  /**
   * 报名ID
   */
  registrationId?: string | number;

  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 支付金额
   */
  amount?: number;

  /**
   * 支付方式
   */
  paymentMethod?: string;

  /**
   * 支付渠道
   */
  paymentChannel?: string;

  /**
   * 第三方交易号
   */
  transactionId?: string | number;

  /**
   * 商户订单号
   */
  outTradeNo?: string;

  /**
   * 支付状态
   */
  status?: number;

  /**
   * 支付成功时间
   */
  paidAt?: string | number;

  /**
   * 支付通知数据
   */
  notifyData?: string;

  /**
   * 退款金额
   */
  refundAmount?: number;

  /**
   * 退款原因
   */
  refundReason?: string;

  /**
   * 退款时间
   */
  refundedAt?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



