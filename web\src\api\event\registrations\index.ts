import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RegistrationsVO, RegistrationsForm, RegistrationsQuery } from '@/api/event/registrations/types';

/**
 * 查询报名记录列表
 * @param query
 * @returns {*}
 */

export const listRegistrations = (query ?: RegistrationsQuery) : AxiosPromise<RegistrationsVO[]> => {
  return request({
    url: '/event/registrations/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询报名记录详细
 * @param id
 */
export const getRegistrations = (id : string | number) : AxiosPromise<RegistrationsVO> => {
  return request({
    url: '/event/registrations/' + id,
    method: 'get'
  });
};

/**
 * 新增报名记录
 * @param data
 */
export const addRegistrations = (data : RegistrationsForm) => {
  return request({
    url: '/event/registrations',
    method: 'post',
    data: data
  });
};

/**
 * 修改报名记录
 * @param data
 */
export const updateRegistrations = (data : RegistrationsForm) => {
  return request({
    url: '/event/registrations',
    method: 'put',
    data: data
  });
};

/**
 * 删除报名记录
 * @param id
 */
export const delRegistrations = (id : string | number | Array<string | number>) => {
  return request({
    url: '/event/registrations/' + id,
    method: 'delete'
  });
};

/**
 * 修改支付状态（根据支付备注核实后修改）
 * @param id 报名ID
 * @param paymentStatus 支付状态：0-未支付，1-已支付
 * @param paymentProof 支付凭证
 * @param remark 操作备注
 */
export const updatePaymentStatus = (id : string | number, paymentStatus : number, paymentProof ?: string | number, remark ?: string) => {
  return request({
    url: `/event/registrations/${id}/payment-status/${paymentStatus}/payment-proof/${paymentProof}`,
    method: 'put',
    params: { remark }
  });
};

/**
 * 根据支付人姓名搜索并修改支付状态
 * @param payerName 支付人姓名
 * @param paymentStatus 支付状态
 * @param remark 操作备注
 */
export const updatePaymentStatusByPayerName = (payerName : string, paymentStatus : number, remark ?: string) => {
  return request({
    url: '/event/registrations/update-by-payer',
    method: 'put',
    params: { payerName, paymentStatus, remark }
  });
};

/**
 * 批量修改支付状态
 * @param ids 报名ID数组
 * @param paymentStatus 支付状态
 * @param remark 操作备注
 */
export const batchUpdatePaymentStatus = (ids : string[], paymentStatus : number, remark ?: string) => {
  return request({
    url: `/event/registrations/batch/payment-status/${paymentStatus}`,
    method: 'put',
    data: ids,
    params: { remark }
  });
};

/**
 * 根据用户信息搜索报名记录
 * @param keyword 关键词（姓名、手机号等）
 * @param pageQuery 分页参数
 */
export const searchRegistrations = (keyword : string, pageQuery : any) => {
  return request({
    url: '/event/registrations/search',
    method: 'get',
    params: { keyword, ...pageQuery }
  });
};

/**
 * 根据支付状态查询报名记录
 * @param paymentStatus 支付状态：0-未支付，1-已支付
 * @param pageQuery 分页参数
 */
export const listByPaymentStatus = (paymentStatus : number, pageQuery : any) => {
  return request({
    url: `/event/registrations/payment-status/${paymentStatus}`,
    method: 'get',
    params: pageQuery
  });
};

/**
 * 根据赛事ID查询报名记录
 * @param eventId 赛事ID
 * @param pageQuery 分页参数
 */
export const listByEvent = (eventId : string | number, pageQuery : any) => {
  return request({
    url: `/event/registrations/event/${eventId}`,
    method: 'get',
    params: pageQuery
  });
};

/**
 * 获取报名统计信息
 */
export const getRegistrationStatistics = () => {
  return request({
    url: '/event/registrations/statistics',
    method: 'get'
  });
};

/**
 * 生成支付二维码
 * @param id 报名ID
 */
export const generatePaymentQrCode = (id : string | number) => {
  return request({
    url: `/event/registrations/${id}/qrcode`,
    method: 'get'
  });
};

/**
 * 导出报名记录列表
 * @param query 查询参数
 */
export const exportRegistrations = (query ?: RegistrationsQuery) => {
  return request({
    url: '/event/registrations/export',
    method: 'post',
    params: query,
    responseType: 'blob'
  });
};

/**
 * 导出指定赛事的报名数据
 * @param eventId 赛事ID
 */
export const exportByEvent = (eventId : string | number) => {
  return request({
    url: `/event/registrations/export/event/${eventId}`,
    method: 'post',
    responseType: 'blob'
  });
};


/**
 * 保存赛事结果信息
*/
export const savaCompetitionResult = (data : RegistrationsForm) => {
  return request({
    url: '/event/registrations/savaCompetitionResult',
    method: 'post',
    data: data
  });
};

/**
 * 查询事件项目列表 (临时添加，应该从eventItems API导入)
 */
export const listEventItems = (query: any) => {
  return request({
    url: '/event/eventItems/list',
    method: 'get',
    params: query
  });
};
