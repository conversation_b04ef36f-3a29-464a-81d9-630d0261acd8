export interface RegistrationsVO {
  /**
   * 报名ID
   */
  id : string | number;

  /**
   * 用户ID
   */
  userId : string | number;

  /**
   * 赛事ID
   */
  eventId : string | number;

  /**
   * 报名项目ID
   */
  eventItemId : string | number;

  /**
   * 报名信息(JSON格式)
   */
  registrationData : string;

  /**
   * 支付状态
   */
  paymentStatus : number;

  /**
   * 支付金额
   */
  paymentAmount : number;

  /**
   * 支付方式
   */
  paymentMethod : string;

  /**
   * 支付时间
   */
  paymentTime : string;

  /**
   * 支付交易号
   */
  paymentTransactionId : string | number;

  /**
   * 支付二维码URL
   */
  qrCodeUrl : string;

  /**
   * 签到状态
   */
  checkInStatus : number;

  /**
   * 签到时间
   */
  checkInTime : string;

  /**
   * 参赛状态
   */
  completionStatus : number;

  /**
   * 完赛时间
   */
  completionTime : string;

  /**
   * 比赛结果数据
   */
  resultData : string;

  /**
   * 备注
   */
  remark : string;
  certificateAwardUrl : string;
  certificateAward : string;
  chainHash : string;
  fileChainHash : string;

}

export interface RegistrationsForm extends BaseEntity {
  /**
   * 报名ID
   */
  id ?: string | number;

  /**
   * 用户ID
   */
  userId ?: string | number;

  /**
   * 赛事ID
   */
  eventId ?: string | number;

  /**
   * 报名项目ID
   */
  eventItemId ?: string | number;

  /**
   * 报名信息(JSON格式)
   */
  registrationData ?: string;

  /**
   * 支付状态
   */
  paymentStatus ?: number;

  /**
   * 支付金额
   */
  paymentAmount ?: number;

  /**
   * 支付方式
   */
  paymentMethod ?: string;

  /**
   * 支付时间
   */
  paymentTime ?: string;

  /**
   * 支付交易号
   */
  paymentTransactionId ?: string | number;

  /**
   * 支付二维码URL
   */
  qrCodeUrl ?: string;

  /**
   * 签到状态
   */
  checkInStatus ?: number;

  /**
   * 签到时间
   */
  checkInTime ?: string;

  /**
   * 参赛状态
   */
  completionStatus ?: number;

  /**
   * 完赛时间
   */
  completionTime ?: string;

  /**
   * 比赛结果数据
   */
  resultData ?: string;

  /**
   * 备注
   */
  remark ?: string;

}

export interface RegistrationsQuery extends PageQuery {

  /**
   * 用户ID
   */
  userId ?: string | number;

  /**
   * 赛事ID
   */
  eventId ?: string | number;

  /**
   * 报名项目ID
   */
  eventItemId ?: string | number;

  /**
   * 报名信息(JSON格式)
   */
  registrationData ?: string;

  /**
   * 支付状态
   */
  paymentStatus ?: number;

  /**
   * 支付金额
   */
  paymentAmount ?: number;

  /**
   * 支付方式
   */
  paymentMethod ?: string;

  /**
   * 支付时间
   */
  paymentTime ?: string;

  /**
   * 支付交易号
   */
  paymentTransactionId ?: string | number;

  /**
   * 支付二维码URL
   */
  qrCodeUrl ?: string;

  /**
   * 签到状态
   */
  checkInStatus ?: number;

  /**
   * 签到时间
   */
  checkInTime ?: string;

  /**
   * 参赛状态
   */
  completionStatus ?: number;

  /**
   * 完赛时间
   */
  completionTime ?: string;

  /**
   * 比赛结果数据
   */
  resultData ?: string;

  /**
   * 日期范围参数
   */
  params ?: any;
}
