import request from '@/utils/request';
import { CustomerServiceVO, CustomerServiceQuery, CustomerServiceForm, EventCustomerServiceVO, EventCustomerServiceQuery, EventCustomerServiceForm } from './types';

/**
 * 查询客服管理列表
 */
export const listCustomerService = (query: CustomerServiceQuery): Promise<PageResult<CustomerServiceVO[]>> => {
  return request({
    url: '/event/customerService/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询客服管理详细
 */
export const getCustomerService = (id: string | number): Promise<ApiResult<CustomerServiceVO>> => {
  return request({
    url: '/event/customerService/' + id,
    method: 'get'
  });
};

/**
 * 新增客服管理
 */
export const addCustomerService = (data: CustomerServiceForm): Promise<ApiResult<void>> => {
  return request({
    url: '/event/customerService',
    method: 'post',
    data: data
  });
};

/**
 * 修改客服管理
 */
export const updateCustomerService = (data: CustomerServiceForm): Promise<ApiResult<void>> => {
  return request({
    url: '/event/customerService',
    method: 'put',
    data: data
  });
};

/**
 * 删除客服管理
 */
export const delCustomerService = (ids: string | number | Array<string | number>): Promise<ApiResult<void>> => {
  return request({
    url: '/event/customerService/' + ids,
    method: 'delete'
  });
};

/**
 * 导出客服管理
 */
export const exportCustomerService = (query: CustomerServiceQuery): Promise<any> => {
  return request({
    url: '/event/customerService/export',
    method: 'post',
    data: query
  });
};

/**
 * 获取所有可用客服列表（用于下拉选择）
 */
export const getAllCustomerService = (): Promise<ApiResult<CustomerServiceVO[]>> => {
  return request({
    url: '/event/customerService/all',
    method: 'get'
  });
};

// ========== 事项客服关系管理接口 ==========

/**
 * 查询事项客服关系列表
 */
export const listEventCustomerService = (query: EventCustomerServiceQuery): Promise<PageResult<EventCustomerServiceVO[]>> => {
  return request({
    url: '/event/customerService/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询指定事项的客服列表
 */
export const getEventCustomerServices = (eventId: string | number): Promise<ApiResult<EventCustomerServiceVO[]>> => {
  return request({
    url: '/event/customerService/event/' + eventId,
    method: 'get'
  });
};

/**
 * 新增事项客服关系
 */
export const addEventCustomerService = (data: EventCustomerServiceForm): Promise<ApiResult<void>> => {
  return request({
    url: '/event/customerService/addEventCustomerService',
    method: 'post',
    data: data
  });
};

/**
 * 批量新增事项客服关系
 */
export const batchAddEventCustomerService = (eventId: string | number, customerServiceIds: Array<string | number>): Promise<ApiResult<void>> => {
  return request({
    url: '/event/customerService/batch',
    method: 'post',
    data: {
      eventId,
      customerServiceIds
    }
  });
};

/**
 * 修改事项客服关系
 */
export const updateEventCustomerService = (data: EventCustomerServiceForm): Promise<ApiResult<void>> => {
  return request({
    url: '/event/customerService/editEventCustomerService',
    method: 'put',
    data: data
  });
};

/**
 * 删除事项客服关系
 */
export const delEventCustomerService = (ids: string | number | Array<string | number>): Promise<ApiResult<void>> => {
  return request({
    url: '/event/customerService/removeEventCustomerService/' + ids,
    method: 'delete'
  });
};

/**
 * 设置主要负责人
 */
export const setPrimaryCustomerService = (eventId: string | number, customerServiceId: string | number): Promise<ApiResult<void>> => {
  return request({
    url: '/event/customerService/setPrimary',
    method: 'post',
    data: {
      eventId,
      customerServiceId
    }
  });
};
