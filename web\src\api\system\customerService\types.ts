/**
 * 客服管理查询对象类型
 */
export interface CustomerServiceQuery extends PageQuery {
  name?: string;
  position?: string;
  phone?: string;
  status?: number;
}

/**
 * 客服管理返回对象
 */
export interface CustomerServiceVO extends BaseEntity {
  id: string | number;
  name: string;
  position: string;
  phone: string;
  qq?: string;
  email?: string;
  wechat?: string;
  otherContact?: string;
  status: number;
  sortOrder?: number;
  tenantId?: string;
}

/**
 * 客服管理表单类型
 */
export interface CustomerServiceForm {
  id?: string | number;
  name: string;
  position: string;
  phone: string;
  qq?: string;
  email?: string;
  wechat?: string;
  otherContact?: string;
  status: number;
  sortOrder?: number;
  remark?: string;
}

/**
 * 事项客服关系查询对象类型
 */
export interface EventCustomerServiceQuery extends PageQuery {
  eventId?: string | number;
  customerServiceId?: string | number;
  isPrimary?: number;
}

/**
 * 事项客服关系返回对象
 */
export interface EventCustomerServiceVO extends BaseEntity {
  id: string | number;
  eventId: string | number;
  customerServiceId: string | number;
  isPrimary: number;
  eventTitle?: string;
  customerServiceName?: string;
  position?: string;
  phone?: string;
  qq?: string;
  email?: string;
  wechat?: string;
  otherContact?: string;
  customerServiceStatus?: number;
}

/**
 * 事项客服关系表单类型
 */
export interface EventCustomerServiceForm {
  id?: string | number;
  eventId: string | number;
  customerServiceId: string | number;
  isPrimary: number;
  remark?: string;
}