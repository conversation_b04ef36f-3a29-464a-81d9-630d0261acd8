<!-- 动态表单字段组件 -->
<template>
  <div class="dynamic-field">
    <!-- 文本输入框 -->
    <template v-if="field.fieldType === 'text'">
      <el-input
        :model-value="modelValue"
        v-bind="getProps(field)"
        :placeholder="field.fieldPlaceholder"
        :disabled="field.isDisabled"
        :readonly="field.isReadonly"
        @update:model-value="handleChange"
        @change="handleChange"
      />
    </template>
    
    <!-- 多行文本输入框 -->
    <template v-else-if="field.fieldType === 'textarea'">
      <el-input
        :model-value="modelValue"
        type="textarea"
        v-bind="getProps(field)"
        :placeholder="field.fieldPlaceholder"
        :disabled="field.isDisabled"
        :readonly="field.isReadonly"
        @update:model-value="handleChange"
        @change="handleChange"
      />
    </template>
    
    <!-- 数字输入框 -->
    <template v-else-if="field.fieldType === 'number'">
      <el-input-number
        :model-value="modelValue"
        v-bind="getProps(field)"
        :placeholder="field.fieldPlaceholder"
        :disabled="field.isDisabled"
        :readonly="field.isReadonly"
        @update:model-value="handleChange"
        @change="handleChange"
        style="width: 100%"
      />
    </template>
    
    <!-- 邮箱输入框 -->
    <template v-else-if="field.fieldType === 'email'">
      <el-input
        :model-value="modelValue"
        type="email"
        v-bind="getProps(field)"
        :placeholder="field.fieldPlaceholder"
        :disabled="field.isDisabled"
        :readonly="field.isReadonly"
        @update:model-value="handleChange"
        @change="handleChange"
      />
    </template>
    
    <!-- 手机号输入框 -->
    <template v-else-if="field.fieldType === 'phone'">
      <el-input
        :model-value="modelValue"
        type="tel"
        v-bind="getProps(field)"
        :placeholder="field.fieldPlaceholder"
        :disabled="field.isDisabled"
        :readonly="field.isReadonly"
        @update:model-value="handleChange"
        @change="handleChange"
      />
    </template>
    
    <!-- 日期选择器 -->
    <template v-else-if="field.fieldType === 'date'">
      <el-date-picker
        :model-value="modelValue"
        type="date"
        v-bind="getProps(field)"
        :placeholder="field.fieldPlaceholder"
        :disabled="field.isDisabled"
        :readonly="field.isReadonly"
        @update:model-value="handleChange"
        @change="handleChange"
        style="width: 100%"
      />
    </template>
    
    <!-- 日期时间选择器 -->
    <template v-else-if="field.fieldType === 'datetime'">
      <el-date-picker
        :model-value="modelValue"
        type="datetime"
        v-bind="getProps(field)"
        :placeholder="field.fieldPlaceholder"
        :disabled="field.isDisabled"
        :readonly="field.isReadonly"
        @update:model-value="handleChange"
        @change="handleChange"
        style="width: 100%"
      />
    </template>
    
    <!-- 下拉选择框 -->
    <template v-else-if="field.fieldType === 'select'">
      <el-select
        :model-value="modelValue"
        v-bind="getProps(field)"
        :placeholder="field.fieldPlaceholder"
        :disabled="field.isDisabled"
        :readonly="field.isReadonly"
        @update:model-value="handleChange"
        @change="handleChange"
        style="width: 100%"
      >
        <el-option
          v-for="option in field.fieldOptions?.options || []"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :disabled="option.disabled"
        />
      </el-select>
    </template>
    
    <!-- 单选按钮组 -->
    <template v-else-if="field.fieldType === 'radio'">
      <el-radio-group
        :model-value="modelValue"
        v-bind="getProps(field)"
        :disabled="field.isDisabled"
        @update:model-value="handleChange"
        @change="handleChange"
      >
        <el-radio
          v-for="option in field.fieldOptions?.options || []"
          :key="option.value"
          :label="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </el-radio>
      </el-radio-group>
    </template>
    
    <!-- 复选框组 -->
    <template v-else-if="field.fieldType === 'checkbox'">
      <el-checkbox-group
        :model-value="modelValue"
        v-bind="getProps(field)"
        :disabled="field.isDisabled"
        @update:model-value="handleChange"
        @change="handleChange"
      >
        <el-checkbox
          v-for="option in field.fieldOptions?.options || []"
          :key="option.value"
          :label="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </el-checkbox>
      </el-checkbox-group>
    </template>
    
    <!-- 文件上传 -->
    <template v-else-if="field.fieldType === 'file'">
      <el-upload
        :model-value="modelValue"
        v-bind="getProps(field)"
        :disabled="field.isDisabled"
        @update:model-value="handleChange"
        @change="handleChange"
        class="upload-demo"
        drag
        :show-file-list="true"
        :limit="field.fieldOptions?.maxCount || 1"
        :accept="field.fieldOptions?.accept"
      >
        <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持格式：{{ field.fieldOptions?.accept || '*' }}，
            大小限制：{{ field.fieldOptions?.maxSize || 5 }}MB
          </div>
        </template>
      </el-upload>
    </template>
    
    <!-- 图片上传 -->
    <template v-else-if="field.fieldType === 'image'">
      <el-upload
        :model-value="modelValue"
        v-bind="getProps(field)"
        :disabled="field.isDisabled"
        @update:model-value="handleChange"
        @change="handleChange"
        class="image-uploader"
        list-type="picture-card"
        :show-file-list="true"
        :limit="field.fieldOptions?.maxCount || 1"
        :accept="field.fieldOptions?.accept || 'image/*'"
      >
        <el-icon><Plus /></el-icon>
        <template #tip>
          <div class="el-upload__tip">
            支持格式：jpg/png/gif，大小限制：{{ field.fieldOptions?.maxSize || 2 }}MB
          </div>
        </template>
      </el-upload>
    </template>
    
    <!-- 默认文本输入框 -->
    <template v-else>
      <el-input
        :model-value="modelValue"
        v-bind="getProps(field)"
        :placeholder="field.fieldPlaceholder"
        :disabled="field.isDisabled"
        :readonly="field.isReadonly"
        @update:model-value="handleChange"
        @change="handleChange"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { FormField } from '../types/form'

interface Props {
  field: FormField
  modelValue: any
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleChange = (value: any) => {
  emit('update:modelValue', value)
  emit('change', value)
}

const getProps = (field: FormField) => {
  const baseProps: any = {}

  switch (field.fieldType) {
    case 'textarea':
      return {
        ...baseProps,
        rows: field.fieldOptions?.rows || 3,
        maxlength: field.validationRules?.maxLength?.value,
        showWordLimit: !!field.validationRules?.maxLength
      }

    case 'select':
      return {
        ...baseProps,
        clearable: field.fieldOptions?.clearable !== false,
        multiple: field.fieldOptions?.multiple || false,
        filterable: field.fieldOptions?.filterable || false
      }

    case 'date':
      return {
        ...baseProps,
        format: field.fieldOptions?.format || 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD'
      }

    case 'datetime':
      return {
        ...baseProps,
        format: field.fieldOptions?.format || 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss'
      }

    case 'number':
      return {
        ...baseProps,
        min: field.validationRules?.min?.value,
        max: field.validationRules?.max?.value,
        step: field.fieldOptions?.step || 1,
        precision: field.fieldOptions?.precision,
        controlsPosition: 'right'
      }

    case 'email':
      return {
        ...baseProps,
        maxlength: field.validationRules?.maxLength?.value || 255,
        showWordLimit: !!field.validationRules?.maxLength
      }

    case 'phone':
      return {
        ...baseProps,
        maxlength: 11,
        showWordLimit: false
      }

    case 'file':
    case 'image':
      return {
        ...baseProps,
        action: '#',
        autoUpload: false,
        listType: field.fieldType === 'image' ? 'picture-card' : 'text'
      }

    default:
      return {
        ...baseProps,
        maxlength: field.validationRules?.maxLength?.value,
        showWordLimit: !!field.validationRules?.maxLength
      }
  }
}
</script>

<style lang="scss" scoped>
.dynamic-field {
  width: 100%;

  .upload-demo {
    :deep(.el-upload) {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);

      &:hover {
        border-color: var(--el-color-primary);
      }
    }

    :deep(.el-upload-dragger) {
      width: 100%;
      height: 120px;
      background-color: var(--el-fill-color-lighter);
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      box-sizing: border-box;
      text-align: center;
      cursor: pointer;
      position: relative;
      overflow: hidden;

      &:hover {
        border-color: var(--el-color-primary);
      }
    }

    :deep(.el-upload__text) {
      color: var(--el-text-color-regular);
      font-size: 14px;
      text-align: center;

      em {
        color: var(--el-color-primary);
        font-style: normal;
      }
    }
  }

  .image-uploader {
    :deep(.el-upload) {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);

      &:hover {
        border-color: var(--el-color-primary);
      }
    }

    :deep(.el-upload-list__item) {
      transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
    }
  }

  // 确保所有输入组件都占满宽度
  :deep(.el-input),
  :deep(.el-select),
  :deep(.el-date-picker),
  :deep(.el-input-number) {
    width: 100%;
  }

  // 单选和复选框样式优化
  :deep(.el-radio-group),
  :deep(.el-checkbox-group) {
    .el-radio,
    .el-checkbox {
      margin-right: 16px;
      margin-bottom: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  // 文本域样式优化
  :deep(.el-textarea) {
    .el-textarea__inner {
      resize: vertical;
    }
  }
}
</style>
