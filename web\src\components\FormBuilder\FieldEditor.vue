<!-- 字段编辑器组件 -->
<template>
  <div class="field-editor">
    <el-form :model="localField" label-width="100px" ref="formRef">
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="基本配置" name="basic">
          <el-form-item label="字段键名" prop="fieldKey" required>
            <el-input 
              v-model="localField.fieldKey"
              placeholder="英文键名，用于数据存储"
              :rules="[
                { required: true, message: '请输入字段键名', trigger: 'blur' },
                { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '只能包含字母、数字和下划线，必须以字母开头', trigger: 'blur' }
              ]"
            />
          </el-form-item>
          
          <el-form-item label="字段标签" prop="fieldLabel" required>
            <el-input 
              v-model="localField.fieldLabel"
              placeholder="显示给用户的标签"
              :rules="[{ required: true, message: '请输入字段标签', trigger: 'blur' }]"
            />
          </el-form-item>
          
          <el-form-item label="字段类型">
            <el-select v-model="localField.fieldType" @change="handleFieldTypeChange">
              <el-option-group label="输入组件">
                <el-option label="单行文本" value="text" />
                <el-option label="多行文本" value="textarea" />
                <el-option label="数字输入" value="number" />
                <el-option label="邮箱" value="email" />
                <el-option label="手机号" value="phone" />
              </el-option-group>
              <el-option-group label="选择组件">
                <el-option label="下拉选择" value="select" />
                <el-option label="单选" value="radio" />
                <el-option label="多选" value="checkbox" />
              </el-option-group>
              <el-option-group label="日期组件">
                <el-option label="日期" value="date" />
                <el-option label="日期时间" value="datetime" />
              </el-option-group>
              <el-option-group label="上传组件">
                <el-option label="文件上传" value="file" />
                <el-option label="图片上传" value="image" />
              </el-option-group>
            </el-select>
          </el-form-item>
          
          <el-form-item label="占位符">
            <el-input 
              v-model="localField.fieldPlaceholder"
              placeholder="字段占位符文字"
            />
          </el-form-item>
          
          <el-form-item label="默认值">
            <component 
              :is="getDefaultValueComponent()"
              v-model="localField.fieldDefaultValue"
              v-bind="getDefaultValueProps()"
            />
          </el-form-item>
        </el-tab-pane>
        
        <el-tab-pane label="选项配置" name="options" :disabled="!needsOptions">
          <template v-if="['select', 'radio', 'checkbox'].includes(localField.fieldType)">
            <div class="options-section">
              <div class="section-header">
                <h4>选项列表</h4>
                <el-button 
                  type="primary"
                  size="small"
                  icon="Plus"
                  @click="addOption"
                >
                  添加选项
                </el-button>
              </div>
              
              <draggable 
                v-model="localField.fieldOptions.options"
                item-key="value"
                handle=".drag-handle"
                @end="handleOptionsSort"
              >
                <template #item="{ element: option, index }">
                  <div class="option-item">
                    <el-icon class="drag-handle"><Rank /></el-icon>
                    <el-input 
                      v-model="option.label"
                      placeholder="选项标签"
                      style="flex: 1"
                    />
                    <el-input 
                      v-model="option.value"
                      placeholder="选项值"
                      style="flex: 1"
                    />
                    <el-switch 
                      v-model="option.disabled"
                      inactive-text="禁用"
                      size="small"
                    />
                    <el-button 
                      type="danger"
                      icon="Delete"
                      size="small"
                      @click="removeOption(index)"
                    />
                  </div>
                </template>
              </draggable>
              
              <div class="options-settings" v-if="localField.fieldType === 'select'">
                <el-form-item label="允许多选">
                  <el-switch v-model="localField.fieldOptions.multiple" />
                </el-form-item>
                <el-form-item label="可清空">
                  <el-switch v-model="localField.fieldOptions.clearable" />
                </el-form-item>
                <el-form-item label="可搜索">
                  <el-switch v-model="localField.fieldOptions.filterable" />
                </el-form-item>
              </div>
            </div>
          </template>
          
          <!-- 其他字段类型的特殊配置 -->
          <template v-if="localField.fieldType === 'textarea'">
            <el-form-item label="行数">
              <el-input-number
                v-model="localField.fieldOptions.rows"
                :min="2"
                :max="10"
              />
            </el-form-item>
            <el-form-item label="显示字数">
              <el-switch v-model="localField.fieldOptions.showWordLimit" />
            </el-form-item>
          </template>
          
          <template v-if="localField.fieldType === 'number'">
            <el-form-item label="步长">
              <el-input-number
                v-model="localField.fieldOptions.step"
                :min="0.01"
                :step="0.01"
              />
            </el-form-item>
            <el-form-item label="小数位数">
              <el-input-number
                v-model="localField.fieldOptions.precision"
                :min="0"
                :max="6"
              />
            </el-form-item>
          </template>
          
          <template v-if="['date', 'datetime'].includes(localField.fieldType)">
            <el-form-item label="日期格式">
              <el-select v-model="localField.fieldOptions.format">
                <el-option label="YYYY-MM-DD" value="YYYY-MM-DD" />
                <el-option label="YYYY/MM/DD" value="YYYY/MM/DD" />
                <el-option label="DD/MM/YYYY" value="DD/MM/YYYY" />
                <el-option v-if="localField.fieldType === 'datetime'" label="YYYY-MM-DD HH:mm:ss" value="YYYY-MM-DD HH:mm:ss" />
                <el-option v-if="localField.fieldType === 'datetime'" label="YYYY-MM-DD HH:mm" value="YYYY-MM-DD HH:mm" />
              </el-select>
            </el-form-item>
          </template>
          
          <template v-if="['file', 'image'].includes(localField.fieldType)">
            <el-form-item label="允许类型">
              <el-input 
                v-model="localField.fieldOptions.accept"
                placeholder=".jpg,.png,.pdf"
              />
            </el-form-item>
            <el-form-item label="大小限制(MB)">
              <el-input-number
                v-model="localField.fieldOptions.maxSize"
                :min="1"
                :max="100"
              />
            </el-form-item>
            <el-form-item label="数量限制">
              <el-input-number
                v-model="localField.fieldOptions.maxCount"
                :min="1"
                :max="10"
              />
            </el-form-item>
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="验证规则" name="validation">
          <el-form-item label="必填">
            <el-switch v-model="localField.isRequired" />
          </el-form-item>
          
          <el-form-item label="必填提示" v-if="localField.isRequired">
            <el-input 
              v-model="requiredMessage"
              placeholder="请输入必填提示信息"
            />
          </el-form-item>
          
          <!-- 字符串长度验证 -->
          <template v-if="['text', 'textarea', 'email', 'phone'].includes(localField.fieldType)">
            <el-form-item label="最小长度">
              <el-input-number
                v-model="minLengthValue"
                :min="0"
                placeholder="不限制"
              />
            </el-form-item>
            <el-form-item label="最大长度">
              <el-input-number
                v-model="maxLengthValue"
                :min="0"
                placeholder="不限制"
              />
            </el-form-item>
          </template>
          
          <!-- 数值范围验证 -->
          <template v-if="localField.fieldType === 'number'">
            <el-form-item label="最小值">
              <el-input-number
                v-model="minValue"
                placeholder="不限制"
              />
            </el-form-item>
            <el-form-item label="最大值">
              <el-input-number
                v-model="maxValue"
                placeholder="不限制"
              />
            </el-form-item>
          </template>
          
          <!-- 正则表达式验证 -->
          <el-form-item label="自定义验证">
            <el-input 
              v-model="patternValue"
              placeholder="输入正则表达式"
            />
            <div class="form-tip">例如：^1[3-9]\\d{9}$ 验证手机号</div>
          </el-form-item>
          
          <el-form-item label="验证提示" v-if="patternValue">
            <el-input 
              v-model="patternMessage"
              placeholder="验证失败提示信息"
            />
          </el-form-item>
        </el-tab-pane>
        
        <el-tab-pane label="布局样式" name="layout">
          <el-form-item label="列宽度">
            <el-slider
              v-model="localField.gridSpan"
              :min="6"
              :max="24"
              :step="6"
              :marks="{ 6: '1/4', 12: '1/2', 18: '3/4', 24: '全宽' }"
            />
          </el-form-item>
          
          <el-form-item label="字段状态">
            <el-radio-group v-model="fieldState">
              <el-radio label="normal">正常</el-radio>
              <el-radio label="disabled">禁用</el-radio>
              <el-radio label="readonly">只读</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="CSS类名">
            <el-input 
              v-model="localField.cssClass"
              placeholder="自定义CSS类名"
            />
          </el-form-item>
          
          <el-form-item label="备注">
            <el-input 
              v-model="localField.remark"
              type="textarea"
              :rows="3"
              placeholder="字段说明或备注信息"
            />
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    
    <div class="editor-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import draggable from 'vuedraggable'
import { getDefaultFieldConfig, validateField } from '@/components/FormBuilder/utils/formBuilder'
import type { FormField, FieldOption } from '@/components/FormBuilder/types/form'

interface Props {
  modelValue: FormField
}

interface Emits {
  (e: 'save', field: FormField): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref()
const activeTab = ref('basic')

const localField = reactive<FormField>({
  ...props.modelValue,
  fieldOptions: { ...props.modelValue.fieldOptions },
  validationRules: { ...props.modelValue.validationRules }
})

// 计算属性
const needsOptions = computed(() => {
  return ['select', 'radio', 'checkbox'].includes(localField.fieldType) ||
         ['textarea', 'number', 'date', 'datetime', 'file', 'image'].includes(localField.fieldType)
})

const fieldState = computed({
  get() {
    if (localField.isDisabled) return 'disabled'
    if (localField.isReadonly) return 'readonly'
    return 'normal'
  },
  set(value) {
    localField.isDisabled = value === 'disabled'
    localField.isReadonly = value === 'readonly'
  }
})

// 验证规则相关计算属性
const requiredMessage = computed({
  get: () => localField.validationRules?.required?.message || `请输入${localField.fieldLabel}`,
  set: (value) => {
    if (!localField.validationRules) localField.validationRules = {}
    if (!localField.validationRules.required) localField.validationRules.required = { value: true, message: '' }
    localField.validationRules.required.message = value
  }
})

const minLengthValue = computed({
  get: () => localField.validationRules?.minLength?.value,
  set: (value) => {
    if (!localField.validationRules) localField.validationRules = {}
    if (value !== undefined && value > 0) {
      localField.validationRules.minLength = { value, message: `最少输入${value}个字符` }
    } else {
      delete localField.validationRules.minLength
    }
  }
})

const maxLengthValue = computed({
  get: () => localField.validationRules?.maxLength?.value,
  set: (value) => {
    if (!localField.validationRules) localField.validationRules = {}
    if (value !== undefined && value > 0) {
      localField.validationRules.maxLength = { value, message: `最多输入${value}个字符` }
    } else {
      delete localField.validationRules.maxLength
    }
  }
})

const minValue = computed({
  get: () => localField.validationRules?.min?.value,
  set: (value) => {
    if (!localField.validationRules) localField.validationRules = {}
    if (value !== undefined) {
      localField.validationRules.min = { value, message: `值不能小于${value}` }
    } else {
      delete localField.validationRules.min
    }
  }
})

const maxValue = computed({
  get: () => localField.validationRules?.max?.value,
  set: (value) => {
    if (!localField.validationRules) localField.validationRules = {}
    if (value !== undefined) {
      localField.validationRules.max = { value, message: `值不能大于${value}` }
    } else {
      delete localField.validationRules.max
    }
  }
})

const patternValue = computed({
  get: () => localField.validationRules?.pattern?.value || '',
  set: (value) => {
    if (!localField.validationRules) localField.validationRules = {}
    if (value) {
      localField.validationRules.pattern = { 
        value, 
        message: localField.validationRules.pattern?.message || '格式不正确' 
      }
    } else {
      delete localField.validationRules.pattern
    }
  }
})

const patternMessage = computed({
  get: () => localField.validationRules?.pattern?.message || '格式不正确',
  set: (value) => {
    if (localField.validationRules?.pattern) {
      localField.validationRules.pattern.message = value
    }
  }
})

// 初始化字段选项
const initFieldOptions = () => {
  if (!localField.fieldOptions) {
    localField.fieldOptions = {}
  }
  if (!localField.validationRules) {
    localField.validationRules = {}
  }
  
  // 为选择类字段初始化选项
  if (['select', 'radio', 'checkbox'].includes(localField.fieldType) && !localField.fieldOptions.options) {
    localField.fieldOptions.options = [
      { label: '选项1', value: 'option1', disabled: false },
      { label: '选项2', value: 'option2', disabled: false }
    ]
  }
}

// 监听字段类型变化
const handleFieldTypeChange = () => {
  const defaultConfig = getDefaultFieldConfig(localField.fieldType)
  localField.fieldOptions = { ...defaultConfig.fieldOptions }
  localField.validationRules = { ...defaultConfig.validationRules }
  initFieldOptions()
}

// 选项管理
const addOption = () => {
  if (!localField.fieldOptions.options) {
    localField.fieldOptions.options = []
  }
  
  localField.fieldOptions.options.push({
    label: `选项${localField.fieldOptions.options.length + 1}`,
    value: `option${localField.fieldOptions.options.length + 1}`,
    disabled: false
  })
}

const removeOption = (index: number) => {
  if (localField.fieldOptions?.options) {
    localField.fieldOptions.options.splice(index, 1)
  }
}

const handleOptionsSort = () => {
  // 选项排序后的处理
}

// 默认值组件
const getDefaultValueComponent = () => {
  switch (localField.fieldType) {
    case 'select':
      return 'el-select'
    case 'radio':
      return 'el-radio-group'
    case 'checkbox':
      return 'el-checkbox-group'
    case 'date':
    case 'datetime':
      return 'el-date-picker'
    case 'number':
      return 'el-input-number'
    case 'textarea':
      return 'el-input'
    default:
      return 'el-input'
  }
}

const getDefaultValueProps = () => {
  switch (localField.fieldType) {
    case 'textarea':
      return { type: 'textarea', rows: 2 }
    case 'date':
      return { type: 'date', valueFormat: 'YYYY-MM-DD' }
    case 'datetime':
      return { type: 'datetime', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    default:
      return {}
  }
}

// 事件处理
const handleSave = async () => {
  try {
    await formRef.value?.validate()
    
    // 验证字段配置
    const errors = validateField(localField)
    if (errors.length > 0) {
      ElMessage.error(`字段配置有误：${errors.join(', ')}`)
      return
    }
    
    emit('save', { ...localField })
  } catch (error) {
    ElMessage.error('请完善必填信息')
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 初始化
initFieldOptions()
</script>

<style lang="scss" scoped>
.field-editor {
  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }

  .options-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 14px;
        color: #303133;
      }
    }

    .option-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      padding: 8px;
      border: 1px solid #ebeef5;
      border-radius: 4px;

      .drag-handle {
        cursor: move;
        color: #c0c4cc;
        
        &:hover {
          color: #409eff;
        }
      }

      .el-switch {
        margin: 0 8px;
      }
    }

    .options-settings {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #ebeef5;
    }
  }

  .editor-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    margin-top: 20px;
  }

  :deep(.el-tabs__content) {
    padding: 20px;
  }

  :deep(.el-form-item) {
    margin-bottom: 18px;
  }

  :deep(.el-slider) {
    margin: 12px 0;
  }
}
</style>