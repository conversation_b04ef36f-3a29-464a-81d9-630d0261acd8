<!-- 字段属性配置组件 -->
<template>
  <div class="field-properties">
    <el-form :model="field" label-width="100px" size="small">
      <el-divider content-position="left">基本属性</el-divider>

      <el-form-item label="字段键名" required>
        <el-input
          v-model="field.fieldKey"
          placeholder="英文键名，用于数据存储"
          @input="handleChange"
        />
        <div class="form-item-tip">只能包含字母、数字和下划线，必须以字母开头</div>
      </el-form-item>

      <el-form-item label="字段标签" required>
        <el-input
          v-model="field.fieldLabel"
          placeholder="显示给用户的标签"
          @input="handleChange"
        />
      </el-form-item>

      <el-form-item label="字段类型">
        <el-select v-model="field.fieldType" @change="handleFieldTypeChange">
          <el-option-group label="输入组件">
            <el-option label="单行文本" value="text" />
            <el-option label="多行文本" value="textarea" />
            <el-option label="数字输入" value="number" />
            <el-option label="邮箱" value="email" />
            <el-option label="手机号" value="phone" />
          </el-option-group>
          <el-option-group label="选择组件">
            <el-option label="下拉选择" value="select" />
            <el-option label="单选" value="radio" />
            <el-option label="多选" value="checkbox" />
          </el-option-group>
          <el-option-group label="日期组件">
            <el-option label="日期" value="date" />
            <el-option label="日期时间" value="datetime" />
          </el-option-group>
          <el-option-group label="上传组件">
            <el-option label="文件上传" value="file" />
            <el-option label="图片上传" value="image" />
          </el-option-group>
        </el-select>
      </el-form-item>

      <el-form-item label="占位符">
        <el-input
          v-model="field.fieldPlaceholder"
          placeholder="字段占位符文字"
          @input="handleChange"
        />
      </el-form-item>

      <el-form-item label="默认值">
        <component
          :is="getDefaultValueComponent()"
          v-model="field.fieldDefaultValue"
          v-bind="getDefaultValueProps()"
          @change="handleChange"
        />
      </el-form-item>

      <el-divider content-position="left">选项配置</el-divider>

      <!-- 选择类字段的选项配置 -->
      <template v-if="['select', 'radio', 'checkbox'].includes(field.fieldType)">
        <el-form-item label="选项列表">
          <div class="options-config">
            <div
              v-for="(option, index) in field.fieldOptions?.options || []"
              :key="index"
              class="option-item"
            >
              <el-input
                v-model="option.label"
                placeholder="选项标签"
                @input="handleChange"
              />
              <el-input
                v-model="option.value"
                placeholder="选项值"
                @input="handleChange"
              />
              <el-switch
                v-model="option.disabled"
                inactive-text="禁用"
                @change="handleChange"
              />
              <el-button
                type="danger"
                icon="Delete"
                size="small"
                @click="removeOption(index)"
              />
            </div>
            <el-button
              type="primary"
              icon="Plus"
              size="small"
              @click="addOption"
            >
              添加选项
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="多选" v-if="field.fieldType === 'select'">
          <el-switch
            v-model="field.fieldOptions.multiple"
            @change="handleChange"
          />
        </el-form-item>

        <el-form-item label="可清空" v-if="field.fieldType === 'select'">
          <el-switch
            v-model="field.fieldOptions.clearable"
            @change="handleChange"
          />
        </el-form-item>
      </template>

      <!-- 文本字段配置 -->
      <template v-if="field.fieldType === 'textarea'">
        <el-form-item label="行数">
          <el-input-number
            v-model="field.fieldOptions.rows"
            :min="2"
            :max="10"
            @change="handleChange"
          />
        </el-form-item>
      </template>

      <!-- 数字字段配置 -->
      <template v-if="field.fieldType === 'number'">
        <el-form-item label="步长">
          <el-input-number
            v-model="field.fieldOptions.step"
            :min="0.01"
            :step="0.01"
            @change="handleChange"
          />
        </el-form-item>

        <el-form-item label="小数位">
          <el-input-number
            v-model="field.fieldOptions.precision"
            :min="0"
            :max="6"
            @change="handleChange"
          />
        </el-form-item>
      </template>

      <!-- 日期字段配置 -->
      <template v-if="['date', 'datetime'].includes(field.fieldType)">
        <el-form-item label="日期格式">
          <el-select v-model="field.fieldOptions.format" @change="handleChange">
            <el-option label="YYYY-MM-DD" value="YYYY-MM-DD" />
            <el-option label="YYYY/MM/DD" value="YYYY/MM/DD" />
            <el-option label="DD/MM/YYYY" value="DD/MM/YYYY" />
            <el-option v-if="field.fieldType === 'datetime'" label="YYYY-MM-DD HH:mm:ss" value="YYYY-MM-DD HH:mm:ss" />
            <el-option v-if="field.fieldType === 'datetime'" label="YYYY-MM-DD HH:mm" value="YYYY-MM-DD HH:mm" />
          </el-select>
        </el-form-item>
      </template>

      <!-- 文件上传配置 -->
      <template v-if="['file', 'image'].includes(field.fieldType)">
        <el-form-item label="允许类型">
          <el-input
            v-model="field.fieldOptions.accept"
            placeholder=".jpg,.png,.pdf"
            @input="handleChange"
          />
        </el-form-item>

        <el-form-item label="大小限制">
          <el-input-number
            v-model="field.fieldOptions.maxSize"
            :min="1"
            :max="100"
            @change="handleChange"
          />
          <span class="unit">MB</span>
        </el-form-item>

        <el-form-item label="数量限制">
          <el-input-number
            v-model="field.fieldOptions.maxCount"
            :min="1"
            :max="10"
            @change="handleChange"
          />
        </el-form-item>
      </template>

      <el-divider content-position="left">验证规则</el-divider>

      <!-- 使用专门的验证配置组件 -->
      <ValidationConfig
        v-model="field.validationRules"
        :field-type="field.fieldType"
        :field-label="field.fieldLabel"
        @change="handleValidationChange"
      />

      <el-divider content-position="left">布局属性</el-divider>

      <el-form-item label="列宽度">
        <el-slider
          v-model="field.gridSpan"
          :min="6"
          :max="24"
          :step="6"
          :marks="{ 6: '1/4', 12: '1/2', 18: '3/4', 24: '全宽' }"
          @change="handleChange"
        />
      </el-form-item>

      <el-form-item label="禁用">
        <el-switch
          v-model="field.isDisabled"
          @change="handleChange"
        />
      </el-form-item>

      <el-form-item label="只读">
        <el-switch
          v-model="field.isReadonly"
          @change="handleChange"
        />
      </el-form-item>

      <el-form-item label="CSS类名">
        <el-input
          v-model="field.cssClass"
          placeholder="自定义CSS类名"
          @input="handleChange"
        />
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="field.remark"
          type="textarea"
          :rows="2"
          placeholder="字段备注信息"
          @input="handleChange"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { getDefaultFieldConfig } from '@/components/FormBuilder/utils/formBuilder'
import ValidationConfig from './ValidationConfig.vue'
import type { FormField, FieldOption } from '@/components/FormBuilder/types/form'

interface Props {
  modelValue: FormField
}

interface Emits {
  (e: 'update:modelValue', value: FormField): void
  (e: 'change', value: FormField): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const field = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
})

// 初始化字段选项
watch(
  () => field.value,
  (newField) => {
    if (!newField.fieldOptions) {
      newField.fieldOptions = {}
    }
    if (!newField.validationRules) {
      newField.validationRules = {}
    }
  },
  { immediate: true, deep: true }
)

// 验证规则的计算属性
const requiredMessage = computed({
  get: () => field.value.validationRules?.required?.message || `请输入${field.value.fieldLabel}`,
  set: (value) => {
    if (!field.value.validationRules) field.value.validationRules = {}
    if (!field.value.validationRules.required) field.value.validationRules.required = { value: true, message: '' }
    field.value.validationRules.required.message = value
  }
})

const minLengthValue = computed({
  get: () => field.value.validationRules?.minLength?.value || undefined,
  set: (value) => {
    if (!field.value.validationRules) field.value.validationRules = {}
    if (value !== undefined && value > 0) {
      field.value.validationRules.minLength = {
        value,
        message: `最少输入${value}个字符`
      }
    } else {
      delete field.value.validationRules.minLength
    }
  }
})

const maxLengthValue = computed({
  get: () => field.value.validationRules?.maxLength?.value || undefined,
  set: (value) => {
    if (!field.value.validationRules) field.value.validationRules = {}
    if (value !== undefined && value > 0) {
      field.value.validationRules.maxLength = {
        value,
        message: `最多输入${value}个字符`
      }
    } else {
      delete field.value.validationRules.maxLength
    }
  }
})

const minValue = computed({
  get: () => field.value.validationRules?.min?.value || undefined,
  set: (value) => {
    if (!field.value.validationRules) field.value.validationRules = {}
    if (value !== undefined) {
      field.value.validationRules.min = {
        value,
        message: `值不能小于${value}`
      }
    } else {
      delete field.value.validationRules.min
    }
  }
})

const maxValue = computed({
  get: () => field.value.validationRules?.max?.value || undefined,
  set: (value) => {
    if (!field.value.validationRules) field.value.validationRules = {}
    if (value !== undefined) {
      field.value.validationRules.max = {
        value,
        message: `值不能大于${value}`
      }
    } else {
      delete field.value.validationRules.max
    }
  }
})

const patternValue = computed({
  get: () => field.value.validationRules?.pattern?.value || '',
  set: (value) => {
    if (!field.value.validationRules) field.value.validationRules = {}
    if (value) {
      field.value.validationRules.pattern = {
        value,
        message: field.value.validationRules.pattern?.message || '格式不正确'
      }
    } else {
      delete field.value.validationRules.pattern
    }
  }
})

const patternMessage = computed({
  get: () => field.value.validationRules?.pattern?.message || '格式不正确',
  set: (value) => {
    if (field.value.validationRules?.pattern) {
      field.value.validationRules.pattern.message = value
    }
  }
})

// 事件处理
const handleChange = () => {
  emit('update:modelValue', field.value)
  emit('change', field.value)
}

const handleFieldTypeChange = () => {
  // 字段类型改变时，重置选项和验证规则
  const defaultConfig = getDefaultFieldConfig(field.value.fieldType)
  field.value.fieldOptions = { ...defaultConfig.fieldOptions }
  field.value.validationRules = { ...defaultConfig.validationRules }
  handleChange()
}

// 验证配置变化处理
const handleValidationChange = (validationRules: any) => {
  // 同步必填状态
  field.value.isRequired = !!validationRules.required
  handleChange()
}

const handleRequiredMessageChange = () => {
  if (!field.value.validationRules) field.value.validationRules = {}
  if (!field.value.validationRules.required) {
    field.value.validationRules.required = { value: true, message: requiredMessage.value }
  } else {
    field.value.validationRules.required.message = requiredMessage.value
  }
  handleChange()
}

const handleMinLengthChange = (value: number | undefined) => {
  minLengthValue.value = value
  handleChange()
}

const handleMaxLengthChange = (value: number | undefined) => {
  maxLengthValue.value = value
  handleChange()
}

const handleMinValueChange = (value: number | undefined) => {
  minValue.value = value
  handleChange()
}

const handleMaxValueChange = (value: number | undefined) => {
  maxValue.value = value
  handleChange()
}

const handlePatternChange = () => {
  handleChange()
}

const handlePatternMessageChange = () => {
  handleChange()
}

// 选项管理
const addOption = () => {
  if (!field.value.fieldOptions) field.value.fieldOptions = {}
  if (!field.value.fieldOptions.options) field.value.fieldOptions.options = []

  field.value.fieldOptions.options.push({
    label: `选项${field.value.fieldOptions.options.length + 1}`,
    value: `option${field.value.fieldOptions.options.length + 1}`,
    disabled: false
  })
  handleChange()
}

const removeOption = (index: number) => {
  if (field.value.fieldOptions?.options) {
    field.value.fieldOptions.options.splice(index, 1)
    handleChange()
  }
}

// 默认值组件
const getDefaultValueComponent = () => {
  switch (field.value.fieldType) {
    case 'select':
      return 'el-select'
    case 'radio':
      return 'el-radio-group'
    case 'checkbox':
      return 'el-checkbox-group'
    case 'date':
    case 'datetime':
      return 'el-date-picker'
    case 'number':
      return 'el-input-number'
    case 'textarea':
      return 'el-input'
    default:
      return 'el-input'
  }
}

const getDefaultValueProps = () => {
  switch (field.value.fieldType) {
    case 'textarea':
      return { type: 'textarea', rows: 2 }
    case 'date':
      return { type: 'date', valueFormat: 'YYYY-MM-DD' }
    case 'datetime':
      return { type: 'datetime', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    default:
      return {}
  }
}
</script>

<style lang="scss" scoped>
.field-properties {
  .form-item-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }

  .unit {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
  }

  .options-config {
    width: 100%;

    .option-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;

      .el-input {
        flex: 1;
      }

      .el-switch {
        margin: 0 8px;
      }
    }
  }

  :deep(.el-divider__text) {
    font-weight: 600;
    color: #303133;
  }

  :deep(.el-slider) {
    margin: 12px 0;
  }

  :deep(.el-form-item) {
    margin-bottom: 18px;
  }

  :deep(.el-form-item__label) {
    font-size: 13px;
    color: #606266;
  }
}
</style>
