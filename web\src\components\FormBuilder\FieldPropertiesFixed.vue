<!-- 字段属性编辑组件 - 修复布尔值反选问题 -->
<template>
  <div class="field-properties">
    <el-form 
      :model="fieldModel" 
      :rules="fieldRules" 
      ref="fieldFormRef"
      label-width="100px" 
      size="default"
    >
      <!-- 基础属性 -->
      <el-divider content-position="left">基础属性</el-divider>
      
      <el-form-item label="字段键名" prop="fieldKey">
        <el-input 
          v-model="fieldModel.fieldKey" 
          placeholder="英文字母开头，只能包含字母、数字、下划线"
          @blur="validateFieldKey"
        />
      </el-form-item>
      
      <el-form-item label="字段标签" prop="fieldLabel">
        <el-input 
          v-model="fieldModel.fieldLabel" 
          placeholder="字段显示名称"
        />
      </el-form-item>
      
      <el-form-item label="字段类型" prop="fieldType">
        <el-select 
          v-model="fieldModel.fieldType" 
          placeholder="请选择字段类型"
          @change="handleFieldTypeChange"
        >
          <el-option
            v-for="type in fieldTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          >
            <el-icon><component :is="type.icon" /></el-icon>
            <span style="margin-left: 8px">{{ type.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="占位符">
        <el-input 
          v-model="fieldModel.fieldPlaceholder" 
          placeholder="请输入占位符提示文本"
        />
      </el-form-item>

      <!-- 布局属性 -->
      <el-divider content-position="left">布局属性</el-divider>
      
      <el-form-item label="栅格占位">
        <el-slider 
          v-model="fieldModel.gridSpan"
          :min="1"
          :max="24"
          :step="1"
          show-input
          style="width: 100%"
        />
        <div class="help-text">占用列数（1-24），24表示占满整行</div>
      </el-form-item>
      
      <el-form-item label="排序">
        <el-input-number 
          v-model="fieldModel.sortOrder"
          :min="0"
          :step="1"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 状态属性 - 重点修复部分 -->
      <el-divider content-position="left">状态属性</el-divider>
      
      <el-form-item label="是否必填">
        <el-switch 
          v-model="fieldModel.isRequired"
          :active-value="true"
          :inactive-value="false"
          active-text="必填"
          inactive-text="选填"
        />
      </el-form-item>
      
      <el-form-item label="是否禁用">
        <el-switch 
          v-model="fieldModel.isDisabled"
          :active-value="true"
          :inactive-value="false"
          active-text="禁用"
          inactive-text="启用"
        />
      </el-form-item>
      
      <el-form-item label="是否只读">
        <el-switch 
          v-model="fieldModel.isReadonly"
          :active-value="true"
          :inactive-value="false"
          active-text="只读"
          inactive-text="可编辑"
        />
      </el-form-item>
      
      <el-form-item label="字段状态">
        <el-switch 
          v-model="fieldModel.status"
          :active-value="1"
          :inactive-value="0"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>

    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElForm } from 'element-plus'

// 定义基本类型
interface FormField {
  id: string
  fieldKey: string
  fieldLabel: string
  fieldType: string
  fieldPlaceholder?: string
  isRequired: boolean
  isDisabled: boolean
  isReadonly: boolean
  gridSpan: number
  sortOrder: number
  status: number
}

interface Props {
  modelValue: FormField
}

interface Emits {
  (e: 'update:modelValue', value: FormField): void
  (e: 'change', value: FormField): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const fieldFormRef = ref<InstanceType<typeof ElForm>>()

// 字段类型选项
const fieldTypes = [
  { value: 'text', label: '单行文本', icon: 'Edit' },
  { value: 'textarea', label: '多行文本', icon: 'DocumentCopy' },
  { value: 'select', label: '下拉选择', icon: 'ArrowDown' },
  { value: 'radio', label: '单选按钮', icon: 'CircleCheck' },
  { value: 'checkbox', label: '多选框', icon: 'Select' },
  { value: 'date', label: '日期选择', icon: 'Calendar' },
  { value: 'datetime', label: '日期时间', icon: 'Timer' },
  { value: 'number', label: '数字输入', icon: 'Histogram' },
  { value: 'email', label: '邮箱', icon: 'Message' },
  { value: 'phone', label: '手机号', icon: 'Phone' },
  { value: 'file', label: '文件上传', icon: 'Upload' },
  { value: 'image', label: '图片上传', icon: 'Picture' }
]

// 字段模型 - 关键修复点：确保布尔值类型正确
const fieldModel = reactive<FormField>({
  id: '',
  fieldKey: '',
  fieldLabel: '',
  fieldType: 'text',
  fieldPlaceholder: '',
  isRequired: false,  // 明确设置为布尔类型
  isDisabled: false,  // 明确设置为布尔类型
  isReadonly: false,  // 明确设置为布尔类型
  gridSpan: 24,
  sortOrder: 0,
  status: 1
})

// 表单验证规则
const fieldRules = {
  fieldKey: [
    { required: true, message: '请输入字段键名', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段键名格式不正确', trigger: 'blur' }
  ],
  fieldLabel: [
    { required: true, message: '请输入字段标签', trigger: 'blur' }
  ],
  fieldType: [
    { required: true, message: '请选择字段类型', trigger: 'change' }
  ]
}

// 初始化数据 - 关键修复函数
const initializeFieldData = (field: FormField) => {
  console.log('初始化字段数据 - 原始数据:', field)
  
  // 确保布尔值类型的正确转换
  const normalizedField = {
    ...field,
    // 强制转换为布尔值，处理数据库中的 0/1 或字符串 '0'/'1' 
    isRequired: field.isRequired === true || field.isRequired === 1 || field.isRequired === '1',
    isDisabled: field.isDisabled === true || field.isDisabled === 1 || field.isDisabled === '1',
    isReadonly: field.isReadonly === true || field.isReadonly === 1 || field.isReadonly === '1',
    // 确保status为数字类型
    status: Number(field.status) || 1
  }
  
  console.log('初始化字段数据 - 转换后:', normalizedField)

  // 复制基础属性
  Object.assign(fieldModel, normalizedField)
}

// 监听属性变化 - 防止重复初始化
let isInitializing = false

watch(() => props.modelValue, (newValue) => {
  if (newValue && !isInitializing) {
    isInitializing = true
    console.log('Props变化，重新初始化:', newValue)
    initializeFieldData(newValue)
    nextTick(() => {
      isInitializing = false
    })
  }
}, { immediate: true, deep: true })

// 监听字段模型变化并更新父组件
watch(fieldModel, () => {
  if (!isInitializing) {
    console.log('字段模型变化:', fieldModel)
    nextTick(() => {
      emitFieldChange()
    })
  }
}, { deep: true })

// 发出字段变化事件
const emitFieldChange = () => {
  // 确保发出的数据类型正确
  const cleanField: FormField = {
    ...fieldModel,
    // 确保布尔值类型
    isRequired: Boolean(fieldModel.isRequired),
    isDisabled: Boolean(fieldModel.isDisabled),
    isReadonly: Boolean(fieldModel.isReadonly),
    // 确保数字类型
    status: Number(fieldModel.status)
  }
  
  console.log('发出字段变化事件:', cleanField)
  
  emit('update:modelValue', cleanField)
  emit('change', cleanField)
}

// 字段类型变化处理
const handleFieldTypeChange = (newType: string) => {
  console.log('字段类型变化:', newType)
  // 清空不相关的默认值
  if (!['text', 'textarea', 'email', 'phone'].includes(newType)) {
    // 可以在这里处理类型变化后的逻辑
  }
}

// 验证字段键名
const validateFieldKey = () => {
  fieldFormRef.value?.validateField('fieldKey')
}

// 暴露验证方法
const validate = async () => {
  if (!fieldFormRef.value) return false
  try {
    await fieldFormRef.value.validate()
    return true
  } catch {
    return false
  }
}

defineExpose({
  validate
})
</script>

<style lang="scss" scoped>
.field-properties {
  height: 100%;
  overflow-y: auto;
  
  .help-text {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
  
  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }
  
  :deep(.el-divider__text) {
    font-weight: 600;
    color: #409eff;
  }
  
  :deep(.el-switch__label) {
    font-size: 12px;
  }
}
</style>