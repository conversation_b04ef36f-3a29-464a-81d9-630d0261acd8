<!-- 表单构建器主组件 -->
<template>
  <div class="form-builder">
    <div class="form-builder-layout">
      <!-- 左侧组件面板 -->
      <div class="component-panel">
        <h3>表单组件</h3>
        <div class="component-groups">
          <div v-for="group in componentGroups" :key="group.name" class="component-group">
            <h4>{{ group.label }}</h4>
            <div class="component-list">
              <div v-for="component in group.components" :key="component.type" class="component-item" draggable="true"
                @dragstart="handleDragStart($event, component)">
                <el-icon>
                  <component :is="component.icon" />
                </el-icon>
                <span>{{ component.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间编辑区域 -->
      <div class="edit-panel">
        <div class="form-header">
          <el-input v-model="formConfig.title" placeholder="表单标题" class="form-title-input" />
          <el-input v-model="formConfig.description" type="textarea" :rows="2" placeholder="表单描述"
            class="form-desc-input" />
        </div>

        <div class="form-canvas" @drop="handleDrop" @dragover.prevent @dragenter.prevent>
          <template v-if="fields.length === 0">
            <div class="empty-canvas">
              <el-icon size="48">
                <DocumentAdd />
              </el-icon>
              <p>拖拽左侧组件到此处开始构建表单</p>
            </div>
          </template>

          <!-- 表单预览容器 -->
          <div v-else class="form-preview-container">
            <div class="form-title-preview">
              <h2>{{ formConfig.title }}</h2>
              <p v-if="formConfig.description" class="form-description">{{ formConfig.description }}</p>
            </div>

            <el-form ref="previewForm" :model="previewData" label-width="120px"
              :label-position="formConfig.layout?.type === 'vertical' ? 'top' : 'right'" class="form-content-preview">
              <el-row :gutter="formConfig.layout?.spacing || 16">
                <draggable v-model="fields" group="formFields" item-key="id" @end="handleFieldSort" class="field-list"
                  :class="{ 'vertical-layout': formConfig.layout?.type === 'vertical' }">
                  <template #item="{ element: field, index }">
                    <el-col :span="formConfig.layout?.type === 'vertical' ? 24 : (field.gridSpan || 12)"
                      class="field-item">
                      <div class="field-wrapper" :class="{
                          active: selectedFieldId === field.id,
                          'field-required': field.isRequired,
                          'field-disabled': field.isDisabled
                        }" @click="selectField(field)">
                        <!-- 字段操作控制按钮 -->
                        <div class="field-controls">
                          <el-tooltip content="编辑字段" placement="top">
                            <el-button size="small" type="primary" icon="Edit" circle @click.stop="editField(field)" />
                          </el-tooltip>
                          <el-tooltip content="复制字段" placement="top">
                            <el-button size="small" type="success" icon="CopyDocument" circle
                              @click.stop="copyField(field)" />
                          </el-tooltip>
                          <el-tooltip content="删除字段" placement="top">
                            <el-button size="small" type="danger" icon="Delete" circle
                              @click.stop="deleteField(field)" />
                          </el-tooltip>
                        </div>

                        <!-- 字段标签和拖拽指示器 -->
                        <div class="field-drag-indicator">
                          <el-icon class="drag-handle">
                            <Rank />
                          </el-icon>
                          <span class="field-order">{{ field.sortOrder + 1 }}</span>
                        </div>

                        <!-- 表单项预览 -->
                        <el-form-item :label="field.fieldLabel" :required="field.isRequired" :prop="field.fieldKey"
                          class="preview-form-item" :class="{ 'full-width': formConfig.layout?.type === 'vertical' }">
                          <!-- 字段类型指示器 -->
                          <div class="field-type-badge">
                            {{ getFieldTypeName(field.fieldType) }}
                          </div>

                          <!-- 动态字段组件 -->
                          <div class="field-preview">
                            <DynamicField :field="field" :model-value="getPreviewValue(field)"
                              @update:model-value="updatePreviewValue(field, $event)" />

                            <!-- 字段验证提示 -->
                            <div v-if="field.validationRules && hasValidationRules(field)" class="validation-hints">
                              <el-tag v-if="field.isRequired" size="small" type="danger" effect="plain">必填</el-tag>
                              <el-tag v-if="field.validationRules.minLength" size="small" type="info" effect="plain">
                                最少{{ field.validationRules.minLength.value }}字符
                              </el-tag>
                              <el-tag v-if="field.validationRules.maxLength" size="small" type="info" effect="plain">
                                最多{{ field.validationRules.maxLength.value }}字符
                              </el-tag>
                              <el-tag v-if="field.validationRules.pattern" size="small" type="warning" effect="plain">
                                格式验证
                              </el-tag>
                            </div>
                          </div>
                        </el-form-item>
                      </div>
                    </el-col>
                  </template>
                </draggable>
              </el-row>

              <!-- 表单操作按钮预览 -->
              <div class="form-actions-preview">
                <el-button type="primary" size="large">
                  {{ formConfig.settings.submitButtonText || '提交' }}
                </el-button>
                <el-button v-if="formConfig.settings.showResetButton" size="large">
                  {{ formConfig.settings.resetButtonText || '重置' }}
                </el-button>
              </div>
            </el-form>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="property-panel">
        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane label="表单设置" name="form">
            <FormSettings v-model="formConfig" />
          </el-tab-pane>

          <el-tab-pane label="字段属性" name="field" :disabled="!selectedField">
            <FieldProperties v-if="selectedField" v-model="selectedField" @change="handleFieldChange" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 字段编辑对话框 -->
    <el-dialog v-model="fieldEditDialog.visible" :title="fieldEditDialog.title" width="800px" append-to-body>
      <FieldEditor v-if="fieldEditDialog.visible" v-model="fieldEditDialog.field" @save="saveFieldEdit"
        @cancel="fieldEditDialog.visible = false" />
    </el-dialog>

    <!-- 表单预览对话框 -->
    <el-dialog v-model="previewDialog.visible" title="表单预览" width="900px" append-to-body>
      <FormPreview :form-config="formConfig" :fields="fields" />
    </el-dialog>

    <!-- 底部操作栏 -->
    <div class="form-builder-footer">
      <el-button @click="handlePreview">
        <el-icon>
          <View />
        </el-icon>
        预览表单
      </el-button>
      <el-button @click="handleClear">
        <el-icon>
          <Delete />
        </el-icon>
        清空表单
      </el-button>
      <el-button type="primary" @click="handleSave">
        <el-icon>
          <Check />
        </el-icon>
        保存表单
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import draggable from 'vuedraggable'
  import DynamicField from '@/components/FormBuilder/DynamicField.vue'
  import FormSettings from '@/components/FormBuilder/FormSettings.vue'
  import FieldProperties from '@/components/FormBuilder/FieldPropertiesFixed.vue'
  import FieldEditor from '@/components/FormBuilder/FieldEditor.vue'
  import FormPreview from '@/components/FormBuilder/FormPreview.vue'
  import { generateFieldId, getDefaultFieldConfig } from '@/components/FormBuilder/utils/formBuilder.ts'
  import { getFormConfigByEventId } from '@/api/event/form'
  import { getDefaultFormConfig } from '@/components/FormBuilder/config/defaultTemplates'
  import type { FormConfig, FormField, ComponentItem } from '@/components/FormBuilder/types/form.ts'

  interface Props {
    eventId ?: number
    formConfigId ?: number
    initialConfig ?: FormConfig
    initialFields ?: FormField[]
  }

  interface Emits {
    (e : 'save', data : { formConfig : FormConfig; fields : FormField[] }) : void
    (e : 'change', data : { formConfig : FormConfig; fields : FormField[] }) : void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 表单配置
  const formConfig = ref<FormConfig>({
    id: null,
    title: '赛事报名表单',
    description: '请填写以下信息完成报名',
    layout: {
      type: 'grid',
      columns: 2,
      spacing: 16
    },
    settings: {
      showProgress: false,
      allowSave: true,
      submitButtonText: '提交报名',
      resetButtonText: '重置表单',
      showResetButton: false
    }
  })

  // 表单字段列表
  const fields = ref<FormField[]>([])

  // 选中的字段
  const selectedFieldId = ref<string>('')
  const selectedField = computed(() =>
    fields.value.find(field => field.id === selectedFieldId.value)
  )

  // 当前激活的标签页
  const activeTab = ref('form')

  // 预览数据
  const previewData = ref<Record<string, any>>({})

  // 对话框状态
  const fieldEditDialog = reactive({
    visible: false,
    title: '',
    field: null as FormField | null
  })

  const previewDialog = reactive({
    visible: false
  })

  // 组件分组
  const componentGroups = ref([
    {
      name: 'input',
      label: '输入组件',
      components: [
        { type: 'text', label: '单行文本', icon: 'Edit' },
        { type: 'textarea', label: '多行文本', icon: 'DocumentCopy' },
        { type: 'number', label: '数字输入', icon: 'Histogram' },
        { type: 'email', label: '邮箱', icon: 'Message' },
        { type: 'phone', label: '手机号', icon: 'Phone' }
      ]
    },
    {
      name: 'select',
      label: '选择组件',
      components: [
        { type: 'select', label: '下拉选择', icon: 'ArrowDown' },
        { type: 'radio', label: '单选', icon: 'CircleCheck' },
        { type: 'checkbox', label: '多选', icon: 'Select' }
      ]
    },
    {
      name: 'date',
      label: '日期组件',
      components: [
        { type: 'date', label: '日期', icon: 'Calendar' },
        { type: 'datetime', label: '日期时间', icon: 'Timer' }
      ]
    },
    {
      name: 'upload',
      label: '上传组件',
      components: [
        { type: 'file', label: '文件上传', icon: 'Upload' },
        { type: 'image', label: '图片上传', icon: 'Picture' }
      ]
    }
  ])

  // 初始化数据
  if (props.initialConfig) {
    formConfig.value = { ...formConfig.value, ...props.initialConfig }
  }
  if (props.initialFields) {
    fields.value = [...props.initialFields]
  }

  // 监听变化并触发事件
  watch(
    [formConfig, fields],
    () => {
      emit('change', {
        formConfig: formConfig.value,
        fields: fields.value
      })
    },
    { deep: true }
  )

  // 拖拽开始
  const handleDragStart = (event : DragEvent, component : ComponentItem) => {
    if (event.dataTransfer) {
      event.dataTransfer.setData('application/json', JSON.stringify(component))
    }
  }

  // 拖拽放置
  const handleDrop = (event : DragEvent) => {
    event.preventDefault()

    if (event.dataTransfer) {
      try {
        const component = JSON.parse(event.dataTransfer.getData('application/json'))
        addField(component)
      } catch (error) {
        console.error('解析拖拽数据失败:', error)
      }
    }
  }

  // 添加字段
  const addField = (component : ComponentItem) => {
    const newField : FormField = {
      id: generateFieldId(),
      fieldKey: `field_${Date.now()}`,
      fieldLabel: component.label,
      fieldType: component.type,
      fieldPlaceholder: `请输入${component.label}`,
      fieldOptions: getDefaultFieldConfig(component.type).fieldOptions,
      fieldDefaultValue: '',
      validationRules: getDefaultFieldConfig(component.type).validationRules,
      isRequired: false,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 24,
      sortOrder: fields.value.length,
      status: 1
    }

    fields.value.push(newField)
    selectField(newField)
    activeTab.value = 'field'
  }

  // 选择字段
  const selectField = (field : FormField) => {
    selectedFieldId.value = field.id
    activeTab.value = 'field'
  }

  // 编辑字段
  const editField = (field : FormField) => {
    fieldEditDialog.field = { ...field }
    fieldEditDialog.title = `编辑字段: ${field.fieldLabel}`
    fieldEditDialog.visible = true
  }

  // 复制字段
  const copyField = (field : FormField) => {
    const newField : FormField = {
      ...field,
      id: generateFieldId(),
      fieldKey: `${field.fieldKey}_copy`,
      fieldLabel: `${field.fieldLabel}(副本)`,
      sortOrder: fields.value.length
    }

    fields.value.push(newField)
    selectField(newField)
  }

  // 删除字段
  const deleteField = async (field : FormField) => {
    try {
      await ElMessageBox.confirm(
        `确认删除字段"${field.fieldLabel}"吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const index = fields.value.findIndex(f => f.id === field.id)
      if (index > -1) {
        fields.value.splice(index, 1)
        if (selectedFieldId.value === field.id) {
          selectedFieldId.value = ''
          activeTab.value = 'form'
        }
      }
    } catch {
      // 用户取消删除
    }
  }

  // 字段排序
  const handleFieldSort = () => {
    fields.value.forEach((field, index) => {
      field.sortOrder = index
    })
  }

  // 字段属性变化
  const handleFieldChange = (field : FormField) => {
    const index = fields.value.findIndex(f => f.id === field.id)
    if (index > -1) {
      fields.value[index] = { ...field }
    }
  }

  // 保存字段编辑
  const saveFieldEdit = (field : FormField) => {
    const index = fields.value.findIndex(f => f.id === field.id)
    if (index > -1) {
      fields.value[index] = { ...field }
    }
    fieldEditDialog.visible = false
  }

  // 获取预览值
  const getPreviewValue = (field : FormField) => {
    return previewData.value[field.fieldKey] || field.fieldDefaultValue || ''
  }

  // 更新预览值
  const updatePreviewValue = (field : FormField, value : any) => {
    previewData.value[field.fieldKey] = value
  }

  // 预览表单
  const handlePreview = () => {
    if (fields.value.length === 0) {
      ElMessage.warning('请先添加表单字段')
      return
    }
    previewDialog.visible = true
  }

  // 清空表单
  const handleClear = async () => {
    try {
      await ElMessageBox.confirm(
        '确认清空整个表单吗？此操作不可恢复！',
        '清空确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      fields.value = []
      selectedFieldId.value = ''
      previewData.value = {}
      activeTab.value = 'form'

      ElMessage.success('表单已清空')
    } catch {
      // 用户取消清空
    }
  }

  // 保存表单
  const handleSave = () => {
    if (!formConfig.value.title) {
      ElMessage.error('请输入表单标题')
      return
    }

    if (fields.value.length === 0) {
      ElMessage.error('请至少添加一个表单字段')
      return
    }

    // 验证字段配置
    const errors = validateFields()
    if (errors.length > 0) {
      ElMessage.error(`表单配置有误：${errors.join(', ')}`)
      return
    }

    emit('save', {
      formConfig: formConfig.value,
      fields: fields.value
    })
  }

  // 获取字段类型显示名称
  const getFieldTypeName = (fieldType : string) => {
    const typeNames : Record<string, string> = {
      text: '文本',
      textarea: '多行文本',
      select: '下拉选择',
      radio: '单选',
      checkbox: '多选',
      date: '日期',
      datetime: '日期时间',
      number: '数字',
      email: '邮箱',
      phone: '手机号',
      file: '文件',
      image: '图片'
    }
    return typeNames[fieldType] || fieldType
  }

  // 检查是否有验证规则
  const hasValidationRules = (field : FormField) => {
    if (!field.validationRules) return false
    return Object.keys(field.validationRules).some(key =>
      field.validationRules![key] && field.validationRules![key]!.value !== undefined
    )
  }

  // 验证字段配置
  const validateFields = () => {
    const errors : string[] = []
    const fieldKeys = new Set<string>()

    fields.value.forEach((field, index) => {
      // 检查字段键名重复
      if (fieldKeys.has(field.fieldKey)) {
        errors.push(`字段"${field.fieldLabel}"的键名重复`)
      } else {
        fieldKeys.add(field.fieldKey)
      }

      // 检查必填字段
      if (!field.fieldLabel) {
        errors.push(`第${index + 1}个字段缺少标签`)
      }

      // 检查选择类字段的选项
      if (['select', 'radio', 'checkbox'].includes(field.fieldType)) {
        if (!field.fieldOptions?.options || field.fieldOptions.options.length === 0) {
          errors.push(`字段"${field.fieldLabel}"缺少选项配置`)
        }
      }
    })

    return errors
  }

  // 加载赛事现有表单配置
  const loadEventFormConfig = async () => {
    if (!props.eventId) return

    try {
      const response = await getFormConfigByEventId(props.eventId)
      if (response.code === 200 && response.data) {
        // 如果存在现有配置，加载它
        const { formConfig: config, fields: formFields } = response.data
        formConfig.value = { ...formConfig.value, ...config }
        if (formFields && formFields.length > 0) {
          fields.value = processFormFields(response.data) || []
        } else {
          fields.value = [];
        }
        formConfig.value.id = response.data.id;
        console.log(formConfig.value)
        ElMessage.success('已加载现有表单配置')
      } else {
        // 如果不存在现有配置，使用默认配置
        initializeDefaultConfig()
      }
    } catch (error) {
      console.warn('加载表单配置失败，使用默认配置:', error)
      initializeDefaultConfig()
    }
  }
  // 处理表单字段的函数
  const processFormFields = (formData) => {
    const { fields: formFields } = formData;

    // 检查fields是否存在且有效
    if (formFields && Array.isArray(formFields) && formFields.length > 0) {
      return formFields.map(field => {
        // 处理fieldOptions：确保为对象/数组（解析JSON字符串）
        let fieldOptions = field.fieldOptions;
        if (typeof fieldOptions === 'string' && fieldOptions.trim() !== '') {
          try {
            fieldOptions = JSON.parse(fieldOptions);
          } catch (e) {
            console.error(`解析fieldOptions失败（id: ${field.id}）:`, e);
            fieldOptions = {}; // 解析失败时默认空对象
          }
        } else if (!fieldOptions || (typeof fieldOptions !== 'object')) {
          fieldOptions = {}; // 非对象/数组时默认空对象
        }

        // 处理validationRules：确保为对象/数组（解析JSON字符串）
        let validationRules = field.validationRules;
        if (typeof validationRules === 'string' && validationRules.trim() !== '') {
          try {
            validationRules = JSON.parse(validationRules);
          } catch (e) {
            console.error(`解析validationRules失败（id: ${field.id}）:`, e);
            validationRules = {}; // 解析失败时默认空对象
          }
        } else if (!validationRules || (typeof validationRules !== 'object')) {
          validationRules = {}; // 非对象/数组时默认空对象
        }

        // 处理布尔类型字段（0=true，1=false）
        // 先统一转换为数字值，再映射为布尔值
        const convertBoolField = (value) => {
          // 处理原始布尔值（true→0→true，false→1→false）
          if (typeof value === 'boolean') {
            return value ? 1 : 0;
          }
          // 处理数字或其他类型（0→true，1→false，默认false）
          const numValue = Number(value);
          return isNaN(numValue) ? false : numValue === 1;
        };

        const isRequired = convertBoolField(field.isRequired);
        const isDisabled = convertBoolField(field.isDisabled);
        const isReadonly = convertBoolField(field.isReadonly);

        // 返回处理后的字段
        return {
          ...field,
          fieldOptions,
          validationRules,
          isRequired,
          isDisabled,
          isReadonly
        };
      });
    }

    // 无效字段时返回空数组
    return [];
  }
  // 初始化默认配置
  const initializeDefaultConfig = () => {
    const defaultConfig = getDefaultFormConfig()
    formConfig.value = { ...formConfig.value, ...defaultConfig.formConfig }
    fields.value = [...defaultConfig.fields]
    ElMessage.info('已加载默认表单配置')
  }

  // 初始化
  const initialize = async () => {
    if (props.initialConfig) {
      formConfig.value = { ...formConfig.value, ...props.initialConfig }
    }

    if (props.initialFields && props.initialFields.length > 0) {
      fields.value = [...props.initialFields]
    } else if (props.eventId) {
      // 如果有eventId但没有初始数据，尝试加载现有配置
      await loadEventFormConfig()
    } else {
      // 如果既没有初始数据也没有eventId，使用默认配置
      initializeDefaultConfig()
    }
  }

  // 组件挂载时初始化
  onMounted(() => {
    initialize()
  })
</script>

<style lang="scss" scoped>
  .form-builder {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f5f7fa;

    .form-builder-layout {
      flex: 1;
      display: flex;
      overflow: hidden;
    }

    .component-panel {
      width: 250px;
      background: white;
      border-right: 1px solid #e4e7ed;
      overflow-y: auto;
      padding: 16px;

      h3 {
        margin: 0 0 16px 0;
        font-size: 16px;
        color: #303133;
      }

      .component-group {
        margin-bottom: 20px;

        h4 {
          margin: 0 0 8px 0;
          font-size: 14px;
          color: #606266;
          border-bottom: 1px solid #ebeef5;
          padding-bottom: 4px;
        }

        .component-list {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .component-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          cursor: grab;
          background: white;
          transition: all 0.3s;

          &:hover {
            border-color: #409eff;
            background: #ecf5ff;
          }

          &:active {
            cursor: grabbing;
          }

          span {
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }

    .edit-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .form-header {
        padding: 16px;
        background: white;
        border-bottom: 1px solid #e4e7ed;

        .form-title-input {
          margin-bottom: 12px;

          :deep(.el-input__inner) {
            font-size: 18px;
            font-weight: 600;
          }
        }
      }

      .form-canvas {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        background: white;
        position: relative;

        .empty-canvas {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 300px;
          border: 2px dashed #dcdfe6;
          border-radius: 8px;
          color: #909399;

          p {
            margin-top: 16px;
            font-size: 14px;
          }
        }

        // 表单预览容器
        .form-preview-container {
          max-width: 800px;
          margin: 0 auto;
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          overflow: hidden;

          .form-title-preview {
            padding: 24px 32px 16px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-bottom: 1px solid #e4e7ed;

            h2 {
              margin: 0 0 8px 0;
              font-size: 24px;
              font-weight: 600;
              color: #303133;
            }

            .form-description {
              margin: 0;
              font-size: 14px;
              color: #606266;
              line-height: 1.6;
            }
          }

          .form-content-preview {
            padding: 32px;
          }

          .form-actions-preview {
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e4e7ed;
            text-align: center;

            .el-button {
              margin: 0 8px;
            }
          }
        }

        .field-list {
          min-height: 100px;

          &.vertical-layout {
            .field-item {
              .preview-form-item {
                :deep(.el-form-item__label) {
                  text-align: left;
                  padding: 0 0 8px 0;
                }
              }
            }
          }
        }

        .field-item {
          margin-bottom: 16px;

          .field-wrapper {
            position: relative;
            padding: 16px;
            border: 2px solid transparent;
            border-radius: 8px;
            transition: all 0.3s;
            background: #fafafa;

            &:hover {
              border-color: #c6e2ff;
              background: #ecf5ff;
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
            }

            &.active {
              border-color: #409eff;
              background: #ecf5ff;
              transform: translateY(-2px);
              box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
            }

            &.field-required {
              border-left: 4px solid #f56c6c;
            }

            &.field-disabled {
              opacity: 0.7;
              background: #f5f5f5;
            }

            .field-controls {
              position: absolute;
              top: -8px;
              right: -8px;
              opacity: 0;
              transition: opacity 0.3s;
              z-index: 10;
              display: flex;
              gap: 4px;
            }

            &:hover .field-controls,
            &.active .field-controls {
              opacity: 1;
            }

            .field-drag-indicator {
              position: absolute;
              top: 8px;
              left: 8px;
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              color: #909399;
              opacity: 0;
              transition: opacity 0.3s;

              .drag-handle {
                cursor: move;
                font-size: 14px;
              }

              .field-order {
                background: #409eff;
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
              }
            }

            &:hover .field-drag-indicator,
            &.active .field-drag-indicator {
              opacity: 1;
            }

            .field-type-badge {
              position: absolute;
              top: 8px;
              right: 8px;
              background: #e1f3d8;
              color: #67c23a;
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 11px;
              font-weight: 500;
              opacity: 0;
              transition: opacity 0.3s;
            }

            &:hover .field-type-badge,
            &.active .field-type-badge {
              opacity: 1;
            }

            .preview-form-item {
              margin-bottom: 0;

              &.full-width {
                :deep(.el-form-item__content) {
                  width: 100%;
                }
              }

              .field-preview {
                position: relative;

                .validation-hints {
                  margin-top: 8px;
                  display: flex;
                  flex-wrap: wrap;
                  gap: 4px;
                  opacity: 0;
                  transition: opacity 0.3s;
                }
              }

              :deep(.el-form-item__label) {
                font-weight: 500;
                color: #303133;
              }

              :deep(.el-input),
              :deep(.el-select),
              :deep(.el-date-picker) {
                width: 100%;
              }
            }

            &:hover .validation-hints,
            &.active .validation-hints {
              opacity: 1;
            }
          }
        }
      }
    }

    .property-panel {
      width: 500px;
      background: white;
      border-left: 1px solid #e4e7ed;
      overflow-y: auto;

      :deep(.el-tabs__content) {
        padding: 16px;
      }
    }

    .form-builder-footer {
      height: 60px;
      background: white;
      border-top: 1px solid #e4e7ed;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      padding: 0 20px;
    }
  }

  // 拖拽相关样式
  .sortable-ghost {
    opacity: 0.5;
  }

  .sortable-chosen {
    transform: scale(1.02);
  }

  .sortable-drag {
    transform: rotate(5deg);
  }
</style>
