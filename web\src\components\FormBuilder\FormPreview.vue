<!-- 表单预览组件 -->
<template>
  <div class="form-preview">
    <div class="preview-header">
      <el-radio-group v-model="previewMode" size="small">
        <el-radio-button label="desktop">桌面端</el-radio-button>
        <el-radio-button label="tablet">平板</el-radio-button>
        <el-radio-button label="mobile">手机端</el-radio-button>
      </el-radio-group>

      <div class="preview-actions">
        <el-button size="small" @click="resetForm">
          <el-icon><RefreshLeft /></el-icon>
          重置表单
        </el-button>
        <el-button type="primary" size="small" @click="submitForm">
          <el-icon><Check /></el-icon>
          测试提交
        </el-button>
      </div>
    </div>

    <div
      class="preview-container"
      :class="{
        'preview-desktop': previewMode === 'desktop',
        'preview-tablet': previewMode === 'tablet',
        'preview-mobile': previewMode === 'mobile'
      }"
    >
      <div class="form-wrapper" :style="formStyles">
        <!-- 表单头部 -->
        <div class="form-header" v-if="formConfig.title || formConfig.description">
          <h2 v-if="formConfig.title" class="form-title">{{ formConfig.title }}</h2>
          <p v-if="formConfig.description" class="form-description">{{ formConfig.description }}</p>
        </div>

        <!-- 进度条 -->
        <div v-if="formConfig.settings.showProgress" class="form-progress">
          <el-progress
            :percentage="progressPercentage"
            :stroke-width="6"
            :color="formConfig.styles?.primaryColor || '#409eff'"
          />
          <div class="progress-text">
            已完成 {{ completedFields }}/{{ totalFields }} 个必填项
          </div>
        </div>

        <!-- 表单内容 -->
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          :label-position="labelPosition"
          :label-width="labelWidth"
        >
          <el-row :gutter="formConfig.layout?.spacing || 16">
            <template v-for="field in visibleFields" :key="field.id">
              <el-col
                :span="getFieldSpan(field)"
                :xs="getResponsiveSpan(field, 'xs')"
                :sm="getResponsiveSpan(field, 'sm')"
                :md="getResponsiveSpan(field, 'md')"
                :lg="getResponsiveSpan(field, 'lg')"
              >
                <el-form-item
                  :label="field.fieldLabel"
                  :prop="field.fieldKey"
                  :required="field.isRequired"
                  :class="[field.cssClass, { 'form-item-error': fieldErrors[field.fieldKey] }]"
                >
                  <!-- 动态表单字段 -->
                  <DynamicField
                    :field="field"
                    v-model="formData[field.fieldKey]"
                    @change="handleFieldChange(field, $event)"
                  />

                  <!-- 字段说明 -->
                  <div v-if="field.remark" class="field-help">
                    <el-icon><InfoFilled /></el-icon>
                    {{ field.remark }}
                  </div>
                </el-form-item>
              </el-col>
            </template>
          </el-row>

          <!-- 表单操作按钮 -->
          <div class="form-actions">
            <el-button
              v-if="formConfig.settings.allowSave"
              @click="saveForm"
            >
              <el-icon><Document /></el-icon>
              保存草稿
            </el-button>

            <el-button
              v-if="formConfig.settings.showResetButton"
              @click="resetForm"
            >
              <el-icon><RefreshLeft /></el-icon>
              {{ formConfig.settings.resetButtonText || '重置表单' }}
            </el-button>

            <el-button
              type="primary"
              @click="submitForm"
              :loading="submitting"
            >
              <el-icon><Check /></el-icon>
              {{ formConfig.settings.submitButtonText || '提交报名' }}
            </el-button>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 表单数据查看 -->
    <el-collapse v-model="activeCollapse" class="preview-debug">
      <el-collapse-item name="formData" title="表单数据">
        <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
      </el-collapse-item>

      <el-collapse-item name="validation" title="验证结果">
        <div v-if="Object.keys(fieldErrors).length === 0" class="success-text">
          <el-icon><SuccessFilled /></el-icon>
          所有字段验证通过
        </div>
        <div v-else>
          <div v-for="(error, field) in fieldErrors" :key="field" class="error-item">
            <strong>{{ getFieldLabel(field) }}:</strong> {{ error }}
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import DynamicField from './DynamicField.vue'
import {
  generateFormRules,
  generateDefaultFormData,
  evaluateConditionalLogic,
  getFieldDisplayValue
} from '@/components/FormBuilder/utils/formBuilder'
import type { FormConfig, FormField } from '@/components/FormBuilder/types/form'

interface Props {
  formConfig: FormConfig
  fields: FormField[]
}

const props = defineProps<Props>()

const formRef = ref()
const previewMode = ref('desktop')
const submitting = ref(false)
const activeCollapse = ref<string[]>([])

// 表单数据
const formData = reactive<Record<string, any>>({})
const fieldErrors = reactive<Record<string, string>>({})

// 生成表单验证规则
const formRules = computed(() => generateFormRules(props.fields))

// 计算表单样式
const formStyles = computed(() => ({
  '--primary-color': props.formConfig.styles?.primaryColor || '#409eff',
  '--border-radius': props.formConfig.styles?.borderRadius || '4px'
}))

// 响应式布局
const labelPosition = computed(() => {
  if (previewMode.value === 'mobile') return 'top'
  return 'right'
})

const labelWidth = computed(() => {
  if (previewMode.value === 'mobile') return 'auto'
  return '120px'
})

// 获取字段跨度
const getFieldSpan = (field: FormField) => {
  if (previewMode.value === 'mobile') return 24
  return field.gridSpan || 24
}

const getResponsiveSpan = (field: FormField, breakpoint: string) => {
  const spans = {
    xs: 24, // 手机端全宽
    sm: field.gridSpan || 24, // 平板端使用配置的宽度
    md: field.gridSpan || 24, // 桌面端使用配置的宽度
    lg: field.gridSpan || 24
  }
  return spans[breakpoint]
}

// 可见字段（考虑条件逻辑）
const visibleFields = computed(() => {
  return props.fields
    .filter(field => field.status === 1)
    .filter(field => evaluateConditionalLogic(field, formData))
    .sort((a, b) => a.sortOrder - b.sortOrder)
})

// 进度计算
const totalFields = computed(() =>
  visibleFields.value.filter(field => field.isRequired).length
)

const completedFields = computed(() => {
  return visibleFields.value.filter(field => {
    if (!field.isRequired) return false
    const value = formData[field.fieldKey]
    return value !== undefined && value !== '' && value !== null
  }).length
})

const progressPercentage = computed(() => {
  if (totalFields.value === 0) return 100
  return Math.round((completedFields.value / totalFields.value) * 100)
})

// 初始化表单数据
const initFormData = () => {
  const defaultData = generateDefaultFormData(props.fields)
  Object.assign(formData, defaultData)
}

// 字段变化处理
const handleFieldChange = (field: FormField, value: any) => {
  formData[field.fieldKey] = value

  // 清除该字段的错误状态
  if (fieldErrors[field.fieldKey]) {
    delete fieldErrors[field.fieldKey]
  }

  // 实时验证
  validateField(field, value)
}

// 单个字段验证
const validateField = async (field: FormField, value: any) => {
  try {
    // 使用Element Plus的验证方法
    if (formRef.value) {
      await formRef.value.validateField(field.fieldKey)
    }
  } catch (error) {
    fieldErrors[field.fieldKey] = error.message || '验证失败'
  }
}

// 获取字段标签
const getFieldLabel = (fieldKey: string) => {
  const field = props.fields.find(f => f.fieldKey === fieldKey)
  return field ? field.fieldLabel : fieldKey
}

// 表单操作
const submitForm = async () => {
  try {
    submitting.value = true

    // 验证表单
    await formRef.value?.validate()

    // 清空错误状态
    Object.keys(fieldErrors).forEach(key => {
      delete fieldErrors[key]
    })

    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success({
      message: '表单提交成功！',
      duration: 3000
    })

    // 显示提交的数据
    console.log('提交的数据:', formData)

  } catch (error) {
    ElMessage.error('请完善表单信息')

    // 收集验证错误
    const errors = await formRef.value?.validate().catch(err => err)
    if (errors && typeof errors === 'object') {
      Object.assign(fieldErrors, errors)
    }
  } finally {
    submitting.value = false
  }
}

const saveForm = () => {
  ElMessage.success('草稿保存成功！')
  console.log('保存草稿:', formData)
}

const resetForm = () => {
  formRef.value?.resetFields()
  initFormData()
  Object.keys(fieldErrors).forEach(key => {
    delete fieldErrors[key]
  })
  ElMessage.info('表单已重置')
}

// 监听字段变化，重新初始化表单数据
watch(
  () => props.fields,
  () => {
    nextTick(() => {
      initFormData()
    })
  },
  { immediate: true, deep: true }
)

// 初始化
initFormData()
</script>

<style lang="scss" scoped>
.form-preview {
  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;

    .preview-actions {
      display: flex;
      gap: 8px;
    }
  }

  .preview-container {
    background: #f0f2f5;
    min-height: 500px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;

    &.preview-desktop {
      .form-wrapper {
        width: 800px;
      }
    }

    &.preview-tablet {
      .form-wrapper {
        width: 600px;
      }
    }

    &.preview-mobile {
      .form-wrapper {
        width: 375px;
      }
    }
  }

  .form-wrapper {
    background: white;
    border-radius: var(--border-radius, 4px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 24px;
    transition: all 0.3s;

    .form-header {
      text-align: center;
      margin-bottom: 24px;

      .form-title {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 12px 0;
      }

      .form-description {
        font-size: 14px;
        color: #606266;
        margin: 0;
        line-height: 1.6;
      }
    }

    .form-progress {
      margin-bottom: 24px;

      .progress-text {
        text-align: center;
        font-size: 12px;
        color: #909399;
        margin-top: 8px;
      }
    }

    .field-help {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: #909399;
      margin-top: 4px;

      .el-icon {
        font-size: 14px;
      }
    }

    .form-item-error {
      :deep(.el-form-item__content) {
        border-left: 3px solid #f56c6c;
        padding-left: 8px;
      }
    }

    .form-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
      margin-top: 32px;
      padding-top: 24px;
      border-top: 1px solid #ebeef5;
    }
  }

  .preview-debug {
    margin-top: 16px;

    pre {
      background: #f5f7fa;
      padding: 12px;
      border-radius: 4px;
      font-size: 12px;
      max-height: 300px;
      overflow: auto;
    }

    .success-text {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #67c23a;
      font-size: 14px;
    }

    .error-item {
      padding: 4px 0;
      color: #f56c6c;
      font-size: 14px;

      strong {
        color: #303133;
      }
    }
  }
}

// 移动端优化
@media (max-width: 768px) {
  .form-preview {
    .preview-header {
      flex-direction: column;
      gap: 12px;

      .preview-actions {
        width: 100%;
        justify-content: center;
      }
    }

    .preview-container {
      padding: 12px;

      .form-wrapper {
        width: 100% !important;
        padding: 16px;
      }
    }
  }
}
</style>
