<!-- 表单设置组件 -->
<template>
  <div class="form-settings">
    <el-form :model="localConfig" label-width="100px" size="small">
      <el-form-item label="表单标题">
        <el-input
          v-model="localConfig.title"
          placeholder="请输入表单标题"
          @change="handleChange"
        />
      </el-form-item>

      <el-form-item label="表单描述">
        <el-input
          v-model="localConfig.description"
          type="textarea"
          :rows="3"
          placeholder="请输入表单描述"
          @change="handleChange"
        />
      </el-form-item>

      <el-divider content-position="left">布局设置</el-divider>

      <el-form-item label="布局类型">
        <el-radio-group v-model="localConfig.layout.type" @change="handleChange">
          <el-radio label="grid">网格布局</el-radio>
          <el-radio label="vertical">垂直布局</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        v-if="localConfig.layout.type === 'grid'"
        label="网格列数"
      >
        <el-slider
          v-model="localConfig.layout.columns"
          :min="1"
          :max="4"
          :marks="{ 1: '1列', 2: '2列', 3: '3列', 4: '4列' }"
          @change="handleChange"
        />
      </el-form-item>

      <el-form-item label="组件间距">
        <el-input-number
          v-model="localConfig.layout.spacing"
          :min="0"
          :max="50"
          :step="2"
          @change="handleChange"
        />
        <span class="input-suffix">px</span>
      </el-form-item>

      <el-divider content-position="left">功能设置</el-divider>

      <el-form-item label="显示进度">
        <el-switch
          v-model="localConfig.settings.showProgress"
          @change="handleChange"
        />
      </el-form-item>

      <el-form-item label="允许保存">
        <el-switch
          v-model="localConfig.settings.allowSave"
          @change="handleChange"
        />
      </el-form-item>

      <el-form-item label="提交按钮">
        <el-input
          v-model="localConfig.settings.submitButtonText"
          placeholder="提交按钮文字"
          @change="handleChange"
        />
      </el-form-item>

      <el-form-item label="显示重置">
        <el-switch
          v-model="localConfig.settings.showResetButton"
          @change="handleChange"
        />
      </el-form-item>

      <el-form-item
        v-if="localConfig.settings.showResetButton"
        label="重置按钮"
      >
        <el-input
          v-model="localConfig.settings.resetButtonText"
          placeholder="重置按钮文字"
          @change="handleChange"
        />
      </el-form-item>

      <el-divider content-position="left">样式设置</el-divider>

      <el-form-item label="主题色">
        <el-color-picker
          v-model="localConfig.styles.primaryColor"
          @change="handleChange"
        />
      </el-form-item>

      <el-form-item label="圆角大小">
        <el-select
          v-model="localConfig.styles.borderRadius"
          @change="handleChange"
        >
          <el-option label="无圆角" value="0px" />
          <el-option label="小圆角" value="2px" />
          <el-option label="中圆角" value="4px" />
          <el-option label="大圆角" value="8px" />
        </el-select>
      </el-form-item>

      <el-form-item label="表单主题">
        <el-select
          v-model="localConfig.styles.theme"
          @change="handleChange"
        >
          <el-option label="默认主题" value="default" />
          <el-option label="简约主题" value="minimal" />
          <el-option label="卡片主题" value="card" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { FormConfig } from '@/components/FormBuilder/types/form'

interface Props {
  modelValue: FormConfig
}

interface Emits {
  (e: 'update:modelValue', value: FormConfig): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const localConfig = ref<FormConfig>({
  ...props.modelValue,
  styles: {
    theme: 'default',
    primaryColor: '#409eff',
    borderRadius: '4px',
    ...props.modelValue.styles
  }
})

watch(
  () => props.modelValue,
  (newValue) => {
    localConfig.value = {
      ...newValue,
      styles: {
        theme: 'default',
        primaryColor: '#409eff',
        borderRadius: '4px',
        ...newValue.styles
      }
    }
  },
  { deep: true }
)

const handleChange = () => {
  emit('update:modelValue', localConfig.value)
}
</script>

<style lang="scss" scoped>
.form-settings {
  .input-suffix {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
  }

  :deep(.el-slider) {
    margin: 12px 0;
  }

  :deep(.el-divider__text) {
    font-size: 14px;
    font-weight: 600;
  }
}
</style>
