<!-- 报名信息展示组件 - 自动根据表单配置展示报名数据 -->
<template>
  <div class="registration-data-viewer">
    <!-- 基础信息优先展示 -->
    <div class="basic-info-section" v-if="hasBasicInfo">
      <div class="info-row" v-for="field in basicFields" :key="field.fieldKey">
        <span class="field-label">{{ field.fieldLabel }}:</span>
        <span class="field-value" :class="getFieldValueClass(field.fieldType)"
          v-html="formatFieldValue(field, getFieldValue(field.fieldKey))">
        </span>
      </div>
    </div>

    <!-- 分组展示其他信息 -->
    <div class="grouped-info-section" v-if="hasGroupedInfo">
      <div v-for="group in groupedFields" :key="group.name" class="info-group" :class="`group-${group.type}`">
        <h4 class="group-title" v-if="group.fields.length > 0">
          <i :class="group.icon"></i>
          {{ group.name }}
        </h4>
        <div class="group-content">
          <div class="info-row" v-for="field in group.fields" :key="field.fieldKey">
            <span class="field-label">{{ field.fieldLabel }}:</span>
            <span class="field-value" :class="getFieldValueClass(field.fieldType)"
              v-html="formatFieldValue(field, getFieldValue(field.fieldKey))">
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 紧凑模式：一行显示多个字段 -->
    <div class="compact-info-section" v-if="compact && otherFields.length > 0">
      <div class="compact-row">
        <span v-for="field in otherFields" :key="field.fieldKey" class="compact-item">
          <strong>{{ field.fieldLabel }}:</strong>
          <span v-html="formatFieldValue(field, getFieldValue(field.fieldKey))"></span>
        </span>
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="isEmpty">
      <i class="el-icon-document"></i>
      <span>暂无报名信息</span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'

  interface FormField {
    fieldKey : string
    fieldLabel : string
    fieldType : string
    fieldOptions ?: {
      options ?: Array<{ label : string; value : any; disabled ?: boolean }>
      [key : string] : any
    }
    isRequired ?: boolean
    sortOrder ?: number
  }

  interface Props {
    // 表单字段配置信息 - 可以是数组或对象
    formFieldsInfo ?: FormField[] | Record<string, any>
    // 用户填写的数据
    formData ?: Record<string, any> | string
    // 是否使用紧凑模式
    compact ?: boolean
    // 最大显示字段数（紧凑模式下）
    maxFields ?: number
    // 是否显示空字段
    showEmptyFields ?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    compact: false,
    maxFields: 6,
    showEmptyFields: false
  })

  // 标准化表单字段配置信息为数组格式
  const normalizedFormFieldsInfo = computed(() => {
    if (!props.formFieldsInfo) return []
    // 如果已经是数组，直接返回
    if (Array.isArray(props.formFieldsInfo)) {
      return props.formFieldsInfo
    }

    // 如果是对象，尝试提取字段信息
    if (typeof props.formFieldsInfo === 'object') {
      // 如果对象有fields属性且是数组，使用fields
      if (props.formFieldsInfo.fields && Array.isArray(props.formFieldsInfo.fields)) {
        return props.formFieldsInfo.fields
      }

      // 如果对象有formFields属性且是数组，使用formFields
      if (props.formFieldsInfo.formFields && Array.isArray(props.formFieldsInfo.formFields)) {
        return props.formFieldsInfo.formFields
      }

      // 否则尝试将对象的值转换为字段数组
      const keys = Object.keys(props.formFieldsInfo)
      return keys.map(key => {
        const field = props.formFieldsInfo[key]
        console.log(field)
        // 如果值本身就是字段对象
        if (field && typeof field === 'object' && field.fieldKey) {
          return field
        }
        // 否则创建一个基础字段对象
        return {
          fieldKey: key,
          fieldLabel: field.label,
          fieldType: field.type,
          value: field.value,
          sortOrder: 0
        }
      })
    }

    return []
  })

  // 解析表单数据
  const parsedFormData = computed(() => {
    if (!props.formData) return {}

    if (typeof props.formData === 'string') {
      try {
        return JSON.parse(props.formData)
      } catch {
        return {}
      }
    }

    return props.formData
  })

  // 获取字段值（智能匹配并转换为可读标签）
  const getFieldValue = (fieldKey : string) : any => {
    const rawValue = parsedFormData.value[fieldKey] || ''

    // 如果没有原始值，直接返回
    if (!rawValue) return ''

    // 查找对应的字段配置
    const fieldConfig = normalizedFormFieldsInfo.value.find(field => field.fieldKey === fieldKey)

    // 如果没有找到字段配置，返回原始值
    if (!fieldConfig) return rawValue

    // 根据字段类型进行值转换
    return convertValueToLabel(fieldConfig, rawValue)
  }

  // 将原始值转换为用户可读的标签
  const convertValueToLabel = (fieldConfig : FormField, rawValue : any) : any => {
    if (!fieldConfig.fieldOptions?.options || !Array.isArray(fieldConfig.fieldOptions.options)) {
      return rawValue
    }

    const options = fieldConfig.fieldOptions.options

    switch (fieldConfig.fieldType) {
      case 'select':
      case 'radio':
        // 单选：根据value查找对应的label
        const option = options.find(opt => opt.value === rawValue || opt.value == rawValue)
        return option ? option.label : rawValue

      case 'checkbox':
        // 多选：处理数组值
        if (Array.isArray(rawValue)) {
          return rawValue.map(val => {
            const opt = options.find(o => o.value === val || o.value == val)
            return opt ? opt.label : val
          })
        } else {
          // 单个值的情况
          const opt = options.find(o => o.value === rawValue || o.value == rawValue)
          return opt ? opt.label : rawValue
        }

      default:
        return rawValue
    }
  }

  // 基础信息字段（优先展示）
  const basicFields = computed(() => {
    if (!normalizedFormFieldsInfo.value.length) return []
    console.log(normalizedFormFieldsInfo.value)
    const basicKeys = ['name', 'realName', 'phone', 'mobile', 'email', 'gender', 'age']
    return normalizedFormFieldsInfo.value
      .filter(field => basicKeys.includes(field.fieldKey))
      .filter(field => props.showEmptyFields || getFieldValue(field.fieldKey))
      .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
  })

  // 按类型分组的字段
  const groupedFields = computed(() => {
    if (!normalizedFormFieldsInfo.value.length || props.compact) return []
    console.log(normalizedFormFieldsInfo.value)
    const basicKeys = ['name', 'realName', 'phone', 'mobile', 'email', 'gender', 'age']
    const otherFields = normalizedFormFieldsInfo.value
      .filter(field => !basicKeys.includes(field.fieldKey))
      .filter(field => props.showEmptyFields || getFieldValue(field.fieldKey))

    const groups = [
      {
        name: '个人信息',
        type: 'personal',
        icon: 'el-icon-user',
        fields: otherFields.filter(field =>
          ['idCard', 'identityCard', 'birthday', 'address', 'emergencyContact', 'emergencyPhone'].includes(field.fieldKey)
        )
      },
      {
        name: '参赛信息',
        type: 'competition',
        icon: 'el-icon-trophy',
        fields: otherFields.filter(field =>
          ['category', 'level', 'team', 'coach', 'experience', 'tshirtSize', 'dietary'].includes(field.fieldKey)
        )
      },
      {
        name: '健康信息',
        type: 'health',
        icon: 'el-icon-first-aid-kit',
        fields: otherFields.filter(field =>
          ['medicalHistory', 'medication', 'allergies', 'healthCondition'].includes(field.fieldKey)
        )
      },
      {
        name: '其他信息',
        type: 'other',
        icon: 'el-icon-more',
        fields: otherFields.filter(field =>
          !['idCard', 'identityCard', 'birthday', 'address', 'emergencyContact', 'emergencyPhone',
            'category', 'level', 'team', 'coach', 'experience', 'tshirtSize', 'dietary',
            'medicalHistory', 'medication', 'allergies', 'healthCondition'].includes(field.fieldKey)
        )
      }
    ]

    return groups.filter(group => group.fields.length > 0)
  })

  // 紧凑模式下的其他字段
  const otherFields = computed(() => {
    if (!props.compact || !normalizedFormFieldsInfo.value.length) return []

    const basicKeys = ['name', 'realName', 'phone', 'mobile', 'email', 'gender', 'age']
    return normalizedFormFieldsInfo.value
      .filter(field => !basicKeys.includes(field.fieldKey))
      .filter(field => props.showEmptyFields || getFieldValue(field.fieldKey))
      .slice(0, props.maxFields)
      .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
  })

  // 是否有基础信息
  const hasBasicInfo = computed(() => basicFields.value.length > 0)

  // 是否有分组信息
  const hasGroupedInfo = computed(() => !props.compact && groupedFields.value.length > 0)

  // 是否为空状态
  const isEmpty = computed(() => {
    return !hasBasicInfo.value && !hasGroupedInfo.value && otherFields.value.length === 0
  })

  // 格式化字段值（用于最终显示）
  const formatFieldValue = (field : FormField, value : any) : string => {
    if (value === null || value === undefined || value === '') {
      return '<span class="empty-value">-</span>'
    }

    console.log('field:', field, 'value:', value)

    switch (field.fieldType) {
      case 'select':
        return field.value;
      case 'radio':
        // 单选：这里的value已经通过convertValueToLabel转换过了，直接返回
        return field.value;

      case 'checkbox':
        // // 多选：如果已经转换为标签数组，直接join
        // if (Array.isArray(value)) {
        //   const labels = value.map(v => `<span class="checkbox-item">${v}</span>`).join('')
        //   return `<div class="checkbox-values">${labels}</div>`
        // }
        // return `<span class="checkbox-value">${String(value)}</span>`
        return field.value;
      case 'date':
        // 日期格式化
        if (value instanceof Date) {
          return `<span class="date-value">${value.toLocaleDateString('zh-CN')}</span>`
        }
        if (typeof value === 'string' && value.includes('-')) {
          try {
            return `<span class="date-value">${new Date(value).toLocaleDateString('zh-CN')}</span>`
          } catch {
            return `<span class="date-value">${String(value)}</span>`
          }
        }
        return `<span class="date-value">${String(value)}</span>`

      case 'datetime':
        // 日期时间格式化
        if (value instanceof Date) {
          return `<span class="datetime-value">${value.toLocaleString('zh-CN')}</span>`
        }
        if (typeof value === 'string' && (value.includes('-') || value.includes('T'))) {
          try {
            return `<span class="datetime-value">${new Date(value).toLocaleString('zh-CN')}</span>`
          } catch {
            return `<span class="datetime-value">${String(value)}</span>`
          }
        }
        return `<span class="datetime-value">${String(value)}</span>`

      case 'number':
        // 数字格式化
        return `<span class="number-value">${Number(value).toLocaleString()}</span>`

      case 'phone':
        // 手机号格式化，添加可点击拨号
        return `<a href="tel:${value}" class="phone-value" style="color: #409eff; text-decoration: none;">${String(value)}</a>`

      case 'email':
        // 邮箱格式化，添加可点击发邮件
        return `<a href="mailto:${value}" class="email-value" style="color: #409eff; text-decoration: none;">${String(value)}</a>`

      case 'file':
        // 文件：生成下载链接
        if (value && Array.isArray(value) && value.length > 0) {
          const links = value.map((file, index) => {
            const fileName = file.name || file.fileName || `文件${index + 1}`
            const fileUrl = file.url || file.fileUrl || '#'
            const fileIcon = getFileIcon(fileName)
            return `
              <div class="file-item" style="display: inline-block; margin-right: 12px; margin-bottom: 4px;">
                <a href="${fileUrl}" target="_blank" download="${fileName}"
                   style="color: #409eff; text-decoration: none; display: flex; align-items: center; gap: 4px; padding: 4px 8px; background: #f0f9ff; border-radius: 4px; border: 1px solid #b3d8ff; font-size: 12px;">
                  <i class="${fileIcon}" style="font-size: 14px;"></i>
                  <span style="max-width: 80px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${fileName}</span>
                </a>
              </div>`
          }).join('')
          return `<div class="file-list">${links}</div>`
        }
        if (value && typeof value === 'string') {
          const fileName = value.split('/').pop() || '下载文件'
          const fileIcon = getFileIcon(fileName)
          return `
            <div class="file-item">
              <a href="${value}" target="_blank" download
                 style="color: #409eff; text-decoration: none; display: flex; align-items: center; gap: 4px; padding: 4px 8px; background: #f0f9ff; border-radius: 4px; border: 1px solid #b3d8ff; font-size: 12px;">
                <i class="${fileIcon}" style="font-size: 14px;"></i>
                <span>${fileName}</span>
              </a>
            </div>`
        }
        return '<span class="file-placeholder">已上传</span>'

      case 'image':
        // 图片：生成预览缩略图
        if (value && Array.isArray(value) && value.length > 0) {
          const images = value.map((img, index) => {
            const imgUrl = img.url || img.imageUrl || img.src || '#'
            const imgName = img.name || img.fileName || `图片${index + 1}`
            return `
              <div class="image-item" style="display: inline-block; margin-right: 8px; margin-bottom: 4px; position: relative;">
                <img src="${imgUrl}" alt="${imgName}"
                     style="width: 50px; height: 50px; object-fit: cover; border-radius: 6px; border: 2px solid #e4e7ed; cursor: pointer; transition: all 0.3s;"
                     onclick="window.open('${imgUrl}', '_blank')"
                     title="点击查看大图 - ${imgName}"
                     onmouseover="this.style.borderColor='#409eff'; this.style.transform='scale(1.1)'"
                     onmouseout="this.style.borderColor='#e4e7ed'; this.style.transform='scale(1)'" />
                <div style="position: absolute; top: -8px; right: -8px; background: #409eff; color: white; border-radius: 50%; width: 16px; height: 16px; display: flex; align-items: center; justify-content: center; font-size: 10px; cursor: pointer;"
                     onclick="window.open('${imgUrl}', '_blank')" title="查看大图">⚲</div>
              </div>`
          }).join('')
          return `<div class="image-list">${images}</div>`
        }
        if (value && typeof value === 'string') {
          return `
            <div class="image-item" style="position: relative; display: inline-block;">
              <img src="${value}" alt="图片"
                   style="width: 50px; height: 50px; object-fit: cover; border-radius: 6px; border: 2px solid #e4e7ed; cursor: pointer; transition: all 0.3s;"
                   onclick="window.open('${value}', '_blank')"
                   title="点击查看大图"
                   onmouseover="this.style.borderColor='#409eff'; this.style.transform='scale(1.1)'"
                   onmouseout="this.style.borderColor='#e4e7ed'; this.style.transform='scale(1)'" />
              <div style="position: absolute; top: -8px; right: -8px; background: #409eff; color: white; border-radius: 50%; width: 16px; height: 16px; display: flex; align-items: center; justify-content: center; font-size: 10px; cursor: pointer;"
                   onclick="window.open('${value}', '_blank')" title="查看大图">⚲</div>
            </div>`
        }
        return '<span class="image-placeholder">已上传</span>'

      case 'textarea':
        // 长文本：限制显示长度，支持展开
        const text = String(value)
        if (text.length > 100) {
          const id = `textarea-${Math.random().toString(36).substr(2, 9)}`
          return `
            <div class="textarea-content">
              <div id="${id}-short" style="display: block; line-height: 1.5; color: #606266;">
                ${text.substring(0, 100)}...
                <a href="javascript:void(0)" onclick="document.getElementById('${id}-short').style.display='none';document.getElementById('${id}-full').style.display='block'"
                   style="color: #409eff; margin-left: 8px; text-decoration: none; font-size: 12px;">[展开]</a>
              </div>
              <div id="${id}-full" style="display: none; line-height: 1.5; color: #606266; white-space: pre-wrap;">
                ${text}
                <a href="javascript:void(0)" onclick="document.getElementById('${id}-full').style.display='none';document.getElementById('${id}-short').style.display='block'"
                   style="color: #409eff; margin-left: 8px; text-decoration: none; font-size: 12px;">[收起]</a>
              </div>
            </div>`
        }
        return `<span class="textarea-value" style="line-height: 1.5; color: #606266; white-space: pre-wrap;">${text}</span>`

      case 'url':
        // URL格式化为可点击链接
        return `<a href="${value}" target="_blank" rel="noopener noreferrer" class="url-value" style="color: #409eff; text-decoration: none;">${String(value)}</a>`

      default:
        // 其他类型：直接显示已转换的标签值
        return `<span class="default-value">${String(value)}</span>`
    }
  }

  // 根据文件名获取文件图标
  const getFileIcon = (fileName : string) : string => {
    const ext = fileName.split('.').pop()?.toLowerCase() || ''
    const iconMap : Record<string, string> = {
      'pdf': 'el-icon-document',
      'doc': 'el-icon-document',
      'docx': 'el-icon-document',
      'xls': 'el-icon-s-grid',
      'xlsx': 'el-icon-s-grid',
      'ppt': 'el-icon-picture-outline',
      'pptx': 'el-icon-picture-outline',
      'txt': 'el-icon-document-copy',
      'zip': 'el-icon-folder-opened',
      'rar': 'el-icon-folder-opened',
      '7z': 'el-icon-folder-opened',
      'jpg': 'el-icon-picture-outline',
      'jpeg': 'el-icon-picture-outline',
      'png': 'el-icon-picture-outline',
      'gif': 'el-icon-picture-outline',
      'mp4': 'el-icon-video-camera',
      'mp3': 'el-icon-headset',
      'wav': 'el-icon-headset'
    }
    return iconMap[ext] || 'el-icon-document'
  }

  // 获取字段值的CSS类名
  const getFieldValueClass = (fieldType : string) : string => {
    const classMap : Record<string, string> = {
      'phone': 'field-phone',
      'email': 'field-email',
      'date': 'field-date',
      'datetime': 'field-datetime',
      'number': 'field-number',
      'file': 'field-file',
      'image': 'field-image',
      'textarea': 'field-textarea'
    }

    return classMap[fieldType] || 'field-text'
  }
</script>

<style lang="scss" scoped>
  .registration-data-viewer {
    font-size: 14px;
    line-height: 1.6;

    // 基础信息区域
    .basic-info-section {
      margin-bottom: 16px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #409eff;
    }

    // 分组信息区域
    .grouped-info-section {
      .info-group {
        margin-bottom: 16px;
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #e4e7ed;

        &.group-personal {
          border-left: 4px solid #67c23a;
        }

        &.group-competition {
          border-left: 4px solid #e6a23c;
        }

        &.group-health {
          border-left: 4px solid #f56c6c;
        }

        &.group-other {
          border-left: 4px solid #909399;
        }

        .group-title {
          margin: 0;
          padding: 12px 16px;
          background: #fafbfc;
          border-bottom: 1px solid #e4e7ed;
          font-size: 14px;
          font-weight: 600;
          color: #2c3e50;
          display: flex;
          align-items: center;

          i {
            margin-right: 8px;
            color: #409eff;
          }
        }

        .group-content {
          padding: 12px 16px;
        }
      }
    }

    // 紧凑模式
    .compact-info-section {
      .compact-row {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
      }

      .compact-item {
        font-size: 13px;
        color: #606266;

        strong {
          color: #2c3e50;
          margin-right: 4px;
        }
      }
    }

    // 信息行
    .info-row {
      display: flex;
      margin-bottom: 8px;
      align-items: flex-start;

      &:last-child {
        margin-bottom: 0;
      }

      .field-label {
        color: #909399;
        min-width: 80px;
        font-size: 13px;
        flex-shrink: 0;
        margin-right: 8px;
      }

      .field-value {
        color: #2c3e50;
        word-break: break-all;
        flex: 1;

        &.field-phone {
          font-family: 'Monaco', 'Consolas', monospace;
          color: #409eff;
        }

        &.field-email {
          color: #409eff;
        }

        &.field-date,
        &.field-datetime {
          color: #67c23a;
        }

        &.field-number {
          font-family: 'Monaco', 'Consolas', monospace;
          color: #e6a23c;
        }

        &.field-file,
        &.field-image {
          color: #f56c6c;
        }

        &.field-textarea {
          white-space: pre-wrap;
        }
      }
    }

    // 空状态
    .empty-state {
      text-align: center;
      color: #c0c4cc;
      padding: 40px 20px;

      i {
        font-size: 32px;
        display: block;
        margin-bottom: 8px;
      }
    }

    // 增强的字段值样式
    :deep(.field-value) {

      // 空值样式
      .empty-value {
        color: #c0c4cc;
        font-style: italic;
      }

      // 选择框值样式
      .select-value {
        padding: 2px 6px;
        background: #f0f2f5;
        border-radius: 3px;
        color: #606266;
        font-size: 12px;
      }

      // 多选框值样式
      .checkbox-values {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .checkbox-item {
          padding: 2px 6px;
          background: #e6f7ff;
          border: 1px solid #91d5ff;
          border-radius: 3px;
          color: #1890ff;
          font-size: 11px;
        }
      }

      // 日期时间样式
      .date-value,
      .datetime-value {
        color: #52c41a;
        font-weight: 500;
      }

      // 数字样式
      .number-value {
        color: #fa8c16;
        font-weight: 500;
        font-family: 'Monaco', 'Consolas', monospace;
      }

      // 文件列表样式
      .file-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .file-item {
          a:hover {
            background: #e6f7ff !important;
            border-color: #91d5ff !important;
          }
        }
      }

      // 图片列表样式
      .image-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .image-item {
          img:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }
        }
      }

      // 文本域内容样式
      .textarea-content {
        max-width: 100%;

        a:hover {
          text-decoration: underline;
        }
      }

      // 占位符样式
      .file-placeholder,
      .image-placeholder {
        color: #bfbfbf;
        font-style: italic;
        font-size: 12px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .registration-data-viewer {
      .info-row {
        flex-direction: column;

        .field-label {
          min-width: auto;
          margin-bottom: 4px;
        }
      }

      .compact-info-section {
        .compact-row {
          flex-direction: column;
          gap: 8px;
        }
      }
    }
  }
</style>
