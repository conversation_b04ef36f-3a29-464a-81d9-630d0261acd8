<!-- 表单验证规则配置组件 -->
<template>
  <div class="validation-config">
    <el-form label-width="100px" size="small">
      <!-- 必填验证 -->
      <el-form-item label="必填验证">
        <el-switch
          v-model="localRules.required.enabled"
          @change="handleRequiredChange"
        />
        <el-input
          v-if="localRules.required.enabled"
          v-model="localRules.required.message"
          placeholder="必填提示信息"
          style="margin-top: 8px;"
          @input="emitChange"
        />
      </el-form-item>

      <!-- 字符串长度验证 -->
      <template v-if="['text', 'textarea', 'email', 'phone'].includes(fieldType)">
        <el-form-item label="最小长度">
          <el-input-number
            v-model="localRules.minLength.value"
            :min="0"
            :max="10000"
            placeholder="不限制"
            style="width: 120px;"
            @change="handleMinLengthChange"
          />
          <el-input
            v-if="localRules.minLength.value > 0"
            v-model="localRules.minLength.message"
            placeholder="长度不足提示"
            style="margin-left: 8px; width: 200px;"
            @input="emitChange"
          />
        </el-form-item>

        <el-form-item label="最大长度">
          <el-input-number
            v-model="localRules.maxLength.value"
            :min="0"
            :max="10000"
            placeholder="不限制"
            style="width: 120px;"
            @change="handleMaxLengthChange"
          />
          <el-input
            v-if="localRules.maxLength.value > 0"
            v-model="localRules.maxLength.message"
            placeholder="长度超限提示"
            style="margin-left: 8px; width: 200px;"
            @input="emitChange"
          />
        </el-form-item>
      </template>

      <!-- 数值范围验证 -->
      <template v-if="fieldType === 'number'">
        <el-form-item label="最小值">
          <el-input-number
            v-model="localRules.min.value"
            placeholder="不限制"
            style="width: 120px;"
            @change="handleMinValueChange"
          />
          <el-input
            v-if="localRules.min.value !== undefined"
            v-model="localRules.min.message"
            placeholder="最小值提示"
            style="margin-left: 8px; width: 200px;"
            @input="emitChange"
          />
        </el-form-item>

        <el-form-item label="最大值">
          <el-input-number
            v-model="localRules.max.value"
            placeholder="不限制"
            style="width: 120px;"
            @change="handleMaxValueChange"
          />
          <el-input
            v-if="localRules.max.value !== undefined"
            v-model="localRules.max.message"
            placeholder="最大值提示"
            style="margin-left: 8px; width: 200px;"
            @input="emitChange"
          />
        </el-form-item>
      </template>

      <!-- 内置验证规则 -->
      <template v-if="fieldType === 'email'">
        <el-form-item label="邮箱验证">
          <el-switch
            v-model="localRules.email.enabled"
            @change="handleEmailChange"
          />
          <el-input
            v-if="localRules.email.enabled"
            v-model="localRules.email.message"
            placeholder="邮箱格式错误提示"
            style="margin-top: 8px;"
            @input="emitChange"
          />
        </el-form-item>
      </template>

      <template v-if="fieldType === 'phone'">
        <el-form-item label="手机验证">
          <el-switch
            v-model="localRules.phone.enabled"
            @change="handlePhoneChange"
          />
          <el-input
            v-if="localRules.phone.enabled"
            v-model="localRules.phone.message"
            placeholder="手机号格式错误提示"
            style="margin-top: 8px;"
            @input="emitChange"
          />
        </el-form-item>
      </template>

      <!-- 正则表达式验证 -->
      <el-form-item label="自定义验证">
        <div class="custom-validation">
          <el-input
            v-model="localRules.pattern.value"
            placeholder="输入正则表达式"
            @input="handlePatternChange"
          />
          <div class="validation-help">
            <el-text size="small" type="info">
              常用正则：身份证 ^[1-9]\d{17}[\dXx]$ ｜ 中文 ^[\u4e00-\u9fa5]+$
            </el-text>
          </div>
          <el-input
            v-if="localRules.pattern.value"
            v-model="localRules.pattern.message"
            placeholder="验证失败提示信息"
            style="margin-top: 8px;"
            @input="emitChange"
          />
        </div>
      </el-form-item>

      <!-- 验证预览 -->
      <el-divider content-position="left">验证预览</el-divider>
      <div class="validation-preview">
        <el-form-item label="测试输入">
          <component
            :is="getTestComponent()"
            v-model="testValue"
            v-bind="getTestProps()"
            @blur="validateTest"
          />
        </el-form-item>

        <div v-if="validationResult" class="validation-result">
          <el-alert
            :title="validationResult.isValid ? '验证通过' : '验证失败'"
            :type="validationResult.isValid ? 'success' : 'error'"
            :description="validationResult.message"
            show-icon
            :closable="false"
          />
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import type { ValidationRules, ValidationRule } from '@/components/FormBuilder/types/form'

interface Props {
  modelValue: ValidationRules
  fieldType: string
  fieldLabel?: string
}

interface Emits {
  (e: 'update:modelValue', value: ValidationRules): void
  (e: 'change', value: ValidationRules): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 本地验证规则状态
const localRules = reactive<{
  required: { enabled: boolean; message: string }
  minLength: { value?: number; message: string }
  maxLength: { value?: number; message: string }
  min: { value?: number; message: string }
  max: { value?: number; message: string }
  email: { enabled: boolean; message: string }
  phone: { enabled: boolean; message: string }
  pattern: { value: string; message: string }
}>({
  required: {
    enabled: !!props.modelValue.required,
    message: props.modelValue.required?.message || `请输入${props.fieldLabel || '内容'}`
  },
  minLength: {
    value: props.modelValue.minLength?.value,
    message: props.modelValue.minLength?.message || '输入长度不足'
  },
  maxLength: {
    value: props.modelValue.maxLength?.value,
    message: props.modelValue.maxLength?.message || '输入长度超限'
  },
  min: {
    value: props.modelValue.min?.value,
    message: props.modelValue.min?.message || '值太小'
  },
  max: {
    value: props.modelValue.max?.value,
    message: props.modelValue.max?.message || '值太大'
  },
  email: {
    enabled: !!props.modelValue.email,
    message: props.modelValue.email?.message || '请输入正确的邮箱格式'
  },
  phone: {
    enabled: !!props.modelValue.pattern?.value?.includes('1[3-9]'),
    message: props.modelValue.pattern?.message || '请输入正确的手机号码'
  },
  pattern: {
    value: props.modelValue.pattern?.value || '',
    message: props.modelValue.pattern?.message || '格式不正确'
  }
})

// 测试值和验证结果
const testValue = ref('')
const validationResult = ref<{ isValid: boolean; message: string } | null>(null)

// 生成验证规则对象
const generateValidationRules = (): ValidationRules => {
  const rules: ValidationRules = {}

  if (localRules.required.enabled) {
    rules.required = { value: true, message: localRules.required.message }
  }

  if (localRules.minLength.value && localRules.minLength.value > 0) {
    rules.minLength = { value: localRules.minLength.value, message: localRules.minLength.message }
  }

  if (localRules.maxLength.value && localRules.maxLength.value > 0) {
    rules.maxLength = { value: localRules.maxLength.value, message: localRules.maxLength.message }
  }

  if (localRules.min.value !== undefined) {
    rules.min = { value: localRules.min.value, message: localRules.min.message }
  }

  if (localRules.max.value !== undefined) {
    rules.max = { value: localRules.max.value, message: localRules.max.message }
  }

  if (localRules.email.enabled) {
    rules.email = { value: true, message: localRules.email.message }
  }

  if (localRules.phone.enabled || (props.fieldType === 'phone' && !localRules.pattern.value)) {
    rules.pattern = {
      value: '^1[3-9]\\d{9}$',
      message: localRules.phone.message
    }
  } else if (localRules.pattern.value) {
    rules.pattern = { value: localRules.pattern.value, message: localRules.pattern.message }
  }

  return rules
}

// 触发变化事件
const emitChange = () => {
  const rules = generateValidationRules()
  emit('update:modelValue', rules)
  emit('change', rules)
}

// 事件处理
const handleRequiredChange = () => {
  emitChange()
}

const handleMinLengthChange = (value: number | undefined) => {
  if (value && value > 0) {
    localRules.minLength.message = `最少输入${value}个字符`
  }
  emitChange()
}

const handleMaxLengthChange = (value: number | undefined) => {
  if (value && value > 0) {
    localRules.maxLength.message = `最多输入${value}个字符`
  }
  emitChange()
}

const handleMinValueChange = (value: number | undefined) => {
  if (value !== undefined) {
    localRules.min.message = `值不能小于${value}`
  }
  emitChange()
}

const handleMaxValueChange = (value: number | undefined) => {
  if (value !== undefined) {
    localRules.max.message = `值不能大于${value}`
  }
  emitChange()
}

const handleEmailChange = () => {
  emitChange()
}

const handlePhoneChange = () => {
  if (localRules.phone.enabled) {
    localRules.pattern.value = ''
  }
  emitChange()
}

const handlePatternChange = () => {
  if (localRules.pattern.value) {
    localRules.phone.enabled = false
  }
  emitChange()
}

// 验证测试
const validateTest = () => {
  const rules = generateValidationRules()
  const value = testValue.value

  try {
    // 必填验证
    if (rules.required && (!value || value.trim() === '')) {
      validationResult.value = { isValid: false, message: rules.required.message }
      return
    }

    // 长度验证
    if (rules.minLength && value.length < rules.minLength.value) {
      validationResult.value = { isValid: false, message: rules.minLength.message }
      return
    }

    if (rules.maxLength && value.length > rules.maxLength.value) {
      validationResult.value = { isValid: false, message: rules.maxLength.message }
      return
    }

    // 数值验证
    if (props.fieldType === 'number') {
      const numValue = Number(value)
      if (isNaN(numValue)) {
        validationResult.value = { isValid: false, message: '请输入有效数字' }
        return
      }

      if (rules.min && numValue < rules.min.value) {
        validationResult.value = { isValid: false, message: rules.min.message }
        return
      }

      if (rules.max && numValue > rules.max.value) {
        validationResult.value = { isValid: false, message: rules.max.message }
        return
      }
    }

    // 邮箱验证
    if (rules.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(value)) {
        validationResult.value = { isValid: false, message: rules.email.message }
        return
      }
    }

    // 正则验证
    if (rules.pattern && value) {
      const regex = new RegExp(rules.pattern.value)
      if (!regex.test(value)) {
        validationResult.value = { isValid: false, message: rules.pattern.message }
        return
      }
    }

    validationResult.value = { isValid: true, message: '验证通过' }
  } catch (error) {
    validationResult.value = { isValid: false, message: '验证规则配置错误' }
  }
}

// 获取测试组件
const getTestComponent = () => {
  switch (props.fieldType) {
    case 'textarea':
      return 'el-input'
    case 'number':
      return 'el-input-number'
    default:
      return 'el-input'
  }
}

const getTestProps = () => {
  switch (props.fieldType) {
    case 'textarea':
      return { type: 'textarea', rows: 2 }
    case 'email':
      return { type: 'email' }
    case 'phone':
      return { type: 'tel' }
    default:
      return {}
  }
}

// 监听外部变化
watch(
  () => props.modelValue,
  (newRules) => {
    localRules.required.enabled = !!newRules.required
    localRules.required.message = newRules.required?.message || `请输入${props.fieldLabel || '内容'}`

    localRules.minLength.value = newRules.minLength?.value
    localRules.minLength.message = newRules.minLength?.message || '输入长度不足'

    localRules.maxLength.value = newRules.maxLength?.value
    localRules.maxLength.message = newRules.maxLength?.message || '输入长度超限'

    localRules.min.value = newRules.min?.value
    localRules.min.message = newRules.min?.message || '值太小'

    localRules.max.value = newRules.max?.value
    localRules.max.message = newRules.max?.message || '值太大'

    localRules.email.enabled = !!newRules.email
    localRules.email.message = newRules.email?.message || '请输入正确的邮箱格式'

    localRules.pattern.value = newRules.pattern?.value || ''
    localRules.pattern.message = newRules.pattern?.message || '格式不正确'

    // 检测手机号正则
    localRules.phone.enabled = !!newRules.pattern?.value?.includes('1[3-9]')
    if (localRules.phone.enabled) {
      localRules.phone.message = newRules.pattern?.message || '请输入正确的手机号码'
    }
  },
  { deep: true, immediate: true }
)
</script>

<style lang="scss" scoped>
.validation-config {
  .custom-validation {
    width: 100%;

    .validation-help {
      margin-top: 4px;
      font-size: 12px;
      color: #909399;
      line-height: 1.4;
    }
  }

  .validation-preview {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .validation-result {
      margin-top: 12px;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 18px;
  }

  :deep(.el-form-item__label) {
    font-size: 13px;
    color: #606266;
  }

  :deep(.el-input-number) {
    .el-input__inner {
      text-align: left;
    }
  }

  :deep(.el-switch) {
    .el-switch__label {
      color: #606266;
    }
  }
}
</style>
