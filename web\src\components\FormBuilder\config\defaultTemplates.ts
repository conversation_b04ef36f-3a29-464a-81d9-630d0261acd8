// 默认表单配置
import type { FormConfig, FormField, FormBuilderData } from '@/components/FormBuilder/types/form'

// 通用报名表单的默认配置
export const getDefaultFormConfig = (): FormBuilderData => {
  const formConfig: FormConfig = {
    title: '赛事报名表单',
    description: '请填写以下信息完成报名',
    layout: {
      type: 'grid',
      columns: 2,
      spacing: 16
    },
    settings: {
      showProgress: true,
      allowSave: true,
      submitButtonText: '提交报名',
      resetButtonText: '重置表单',
      showResetButton: false
    },
    styles: {
      theme: 'default',
      primaryColor: '#409eff',
      borderRadius: '4px'
    }
  }

  const fields: FormField[] = [
    {
      id: 'field_name',
      fieldKey: 'name',
      fieldLabel: '姓名',
      fieldType: 'text',
      fieldPlaceholder: '请输入您的姓名',
      fieldDefaultValue: '',
      validationRules: {
        required: { value: true, message: '请输入姓名' },
        maxLength: { value: 20, message: '姓名长度不能超过20个字符' }
      },
      isRequired: true,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 12,
      sortOrder: 1,
      status: 1
    },
    {
      id: 'field_gender',
      fieldKey: 'gender',
      fieldLabel: '性别',
      fieldType: 'radio',
      fieldPlaceholder: '',
      fieldOptions: {
        options: [
          { label: '男', value: 'male', disabled: false },
          { label: '女', value: 'female', disabled: false }
        ]
      },
      fieldDefaultValue: '',
      validationRules: {
        required: { value: true, message: '请选择性别' }
      },
      isRequired: true,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 12,
      sortOrder: 2,
      status: 1
    },
    {
      id: 'field_phone',
      fieldKey: 'phone',
      fieldLabel: '手机号码',
      fieldType: 'phone',
      fieldPlaceholder: '请输入您的手机号码',
      fieldDefaultValue: '',
      validationRules: {
        required: { value: true, message: '请输入手机号码' },
        pattern: { value: '^1[3-9]\\d{9}$', message: '请输入正确的手机号码' }
      },
      isRequired: true,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 12,
      sortOrder: 3,
      status: 1
    },
    {
      id: 'field_email',
      fieldKey: 'email',
      fieldLabel: '邮箱',
      fieldType: 'email',
      fieldPlaceholder: '请输入您的邮箱地址',
      fieldDefaultValue: '',
      validationRules: {
        email: { value: true, message: '请输入正确的邮箱格式' }
      },
      isRequired: false,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 12,
      sortOrder: 4,
      status: 1
    },
    {
      id: 'field_age',
      fieldKey: 'age',
      fieldLabel: '年龄',
      fieldType: 'number',
      fieldPlaceholder: '请输入您的年龄',
      fieldOptions: {
        step: 1,
        precision: 0
      },
      fieldDefaultValue: undefined,
      validationRules: {
        required: { value: true, message: '请输入年龄' },
        min: { value: 1, message: '年龄必须大于0' },
        max: { value: 120, message: '年龄不能超过120' }
      },
      isRequired: true,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 12,
      sortOrder: 5,
      status: 1
    },
    {
      id: 'field_emergency_contact',
      fieldKey: 'emergencyContact',
      fieldLabel: '紧急联系人',
      fieldType: 'text',
      fieldPlaceholder: '请输入紧急联系人姓名和电话',
      fieldDefaultValue: '',
      validationRules: {
        maxLength: { value: 50, message: '紧急联系人信息长度不能超过50个字符' }
      },
      isRequired: false,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 24,
      sortOrder: 6,
      status: 1
    },
    {
      id: 'field_remark',
      fieldKey: 'remark',
      fieldLabel: '备注',
      fieldType: 'textarea',
      fieldPlaceholder: '请输入其他需要说明的信息',
      fieldOptions: {
        rows: 3,
        showWordLimit: true
      },
      fieldDefaultValue: '',
      validationRules: {
        maxLength: { value: 200, message: '备注长度不能超过200个字符' }
      },
      isRequired: false,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 24,
      sortOrder: 7,
      status: 1
    }
  ]

  return {
    formConfig,
    fields
  }
}

// 体育赛事专用表单模板
export const getSportsEventFormConfig = (): FormBuilderData => {
  const defaultConfig = getDefaultFormConfig()
  
  // 在默认配置基础上添加体育赛事特有字段
  const sportsFields: FormField[] = [
    {
      id: 'field_id_card',
      fieldKey: 'idCard',
      fieldLabel: '身份证号',
      fieldType: 'text',
      fieldPlaceholder: '请输入身份证号码',
      fieldDefaultValue: '',
      validationRules: {
        required: { value: true, message: '请输入身份证号码' },
        pattern: { value: '^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$', message: '请输入正确的身份证号码' }
      },
      isRequired: true,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 12,
      sortOrder: 4.5,
      status: 1
    },
    {
      id: 'field_t_shirt_size',
      fieldKey: 'tShirtSize',
      fieldLabel: 'T恤尺寸',
      fieldType: 'select',
      fieldPlaceholder: '请选择T恤尺寸',
      fieldOptions: {
        options: [
          { label: 'XS', value: 'XS', disabled: false },
          { label: 'S', value: 'S', disabled: false },
          { label: 'M', value: 'M', disabled: false },
          { label: 'L', value: 'L', disabled: false },
          { label: 'XL', value: 'XL', disabled: false },
          { label: 'XXL', value: 'XXL', disabled: false }
        ],
        clearable: true
      },
      fieldDefaultValue: '',
      validationRules: {
        required: { value: true, message: '请选择T恤尺寸' }
      },
      isRequired: true,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 12,
      sortOrder: 6.5,
      status: 1
    },
    {
      id: 'field_medical_conditions',
      fieldKey: 'medicalConditions',
      fieldLabel: '健康状况',
      fieldType: 'checkbox',
      fieldPlaceholder: '',
      fieldOptions: {
        options: [
          { label: '无重大疾病史', value: 'healthy', disabled: false },
          { label: '有心脏病史', value: 'heart_disease', disabled: false },
          { label: '有高血压', value: 'hypertension', disabled: false },
          { label: '有糖尿病', value: 'diabetes', disabled: false },
          { label: '有其他慢性疾病', value: 'other_chronic', disabled: false }
        ]
      },
      fieldDefaultValue: [],
      validationRules: {
        required: { value: true, message: '请选择健康状况' }
      },
      isRequired: true,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 24,
      sortOrder: 7.5,
      status: 1
    }
  ]

  // 合并字段并重新排序
  const allFields = [...defaultConfig.fields, ...sportsFields].sort((a, b) => a.sortOrder - b.sortOrder)
  
  return {
    formConfig: {
      ...defaultConfig.formConfig,
      title: '体育赛事报名表单',
      description: '请填写以下信息完成体育赛事报名，我们将严格保护您的个人信息'
    },
    fields: allFields
  }
}

// 学术会议表单模板
export const getAcademicConferenceFormConfig = (): FormBuilderData => {
  const defaultConfig = getDefaultFormConfig()
  
  const academicFields: FormField[] = [
    {
      id: 'field_organization',
      fieldKey: 'organization',
      fieldLabel: '所在单位',
      fieldType: 'text',
      fieldPlaceholder: '请输入您所在的单位或机构',
      fieldDefaultValue: '',
      validationRules: {
        required: { value: true, message: '请输入所在单位' },
        maxLength: { value: 100, message: '单位名称长度不能超过100个字符' }
      },
      isRequired: true,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 12,
      sortOrder: 4.5,
      status: 1
    },
    {
      id: 'field_job_title',
      fieldKey: 'jobTitle',
      fieldLabel: '职务/职称',
      fieldType: 'text',
      fieldPlaceholder: '请输入您的职务或职称',
      fieldDefaultValue: '',
      validationRules: {
        maxLength: { value: 50, message: '职务职称长度不能超过50个字符' }
      },
      isRequired: false,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 12,
      sortOrder: 5.5,
      status: 1
    },
    {
      id: 'field_research_area',
      fieldKey: 'researchArea',
      fieldLabel: '研究领域',
      fieldType: 'select',
      fieldPlaceholder: '请选择您的主要研究领域',
      fieldOptions: {
        options: [
          { label: '计算机科学', value: 'computer_science', disabled: false },
          { label: '人工智能', value: 'artificial_intelligence', disabled: false },
          { label: '数据科学', value: 'data_science', disabled: false },
          { label: '软件工程', value: 'software_engineering', disabled: false },
          { label: '网络安全', value: 'cybersecurity', disabled: false },
          { label: '其他', value: 'other', disabled: false }
        ],
        clearable: true
      },
      fieldDefaultValue: '',
      validationRules: {
        required: { value: true, message: '请选择研究领域' }
      },
      isRequired: true,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 12,
      sortOrder: 6.5,
      status: 1
    },
    {
      id: 'field_dietary_requirements',
      fieldKey: 'dietaryRequirements',
      fieldLabel: '饮食需求',
      fieldType: 'checkbox',
      fieldPlaceholder: '',
      fieldOptions: {
        options: [
          { label: '无特殊要求', value: 'none', disabled: false },
          { label: '素食', value: 'vegetarian', disabled: false },
          { label: '清真', value: 'halal', disabled: false },
          { label: '过敏提醒', value: 'allergy', disabled: false }
        ]
      },
      fieldDefaultValue: [],
      isRequired: false,
      isDisabled: false,
      isReadonly: false,
      gridSpan: 24,
      sortOrder: 7.5,
      status: 1
    }
  ]

  const allFields = [...defaultConfig.fields, ...academicFields].sort((a, b) => a.sortOrder - b.sortOrder)
  
  return {
    formConfig: {
      ...defaultConfig.formConfig,
      title: '学术会议注册表单',
      description: '请填写以下信息完成学术会议注册'
    },
    fields: allFields
  }
}

// 获取预定义的表单模板列表
export const getPredefinedTemplates = () => [
  {
    id: 'default',
    name: '通用报名表单',
    description: '包含姓名、性别、联系方式等基础信息的通用表单',
    category: 'general',
    config: getDefaultFormConfig()
  },
  {
    id: 'sports',
    name: '体育赛事表单',
    description: '适用于马拉松、球类比赛等体育赛事的报名表单',
    category: 'sports',
    config: getSportsEventFormConfig()
  },
  {
    id: 'academic',
    name: '学术会议表单',
    description: '适用于学术会议、研讨会等学术活动的注册表单',
    category: 'academic',
    config: getAcademicConferenceFormConfig()
  }
]