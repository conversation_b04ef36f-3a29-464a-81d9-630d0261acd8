// 表单构建器类型定义
export interface FormConfig {
  id : number | string
  title : string
  description : string
  layout : {
    type : 'grid' | 'vertical'
    columns ?: number
    spacing ?: number
  }
  settings : {
    showProgress : boolean
    allowSave : boolean
    submitButtonText : string
    resetButtonText ?: string
    showResetButton ?: boolean
  }
  styles ?: {
    theme ?: string
    primaryColor ?: string
    borderRadius ?: string
  }
}

export interface ValidationRule {
  value : any
  message : string
}

export interface ValidationRules {
  required ?: ValidationRule
  minLength ?: ValidationRule
  maxLength ?: ValidationRule
  min ?: ValidationRule
  max ?: ValidationRule
  pattern ?: ValidationRule
  email ?: ValidationRule
  phone ?: ValidationRule
  custom ?: ValidationRule
}

export interface FieldOption {
  label : string
  value : any
  disabled ?: boolean
}

export interface FieldOptions {
  // 选择类字段
  options ?: FieldOption[]
  multiple ?: boolean
  clearable ?: boolean
  filterable ?: boolean
  allowOther ?: boolean
  otherLabel ?: string

  // 文本字段
  rows ?: number
  maxlength ?: number
  showWordLimit ?: boolean

  // 数字字段
  step ?: number
  precision ?: number

  // 日期字段
  format ?: string
  disabledDate ?: string
  shortcuts ?: any[]

  // 文件上传字段
  accept ?: string
  maxSize ?: number
  maxCount ?: number
  fileType ?: string[]
  showFileList ?: boolean
  uploadText ?: string
}

export interface ConditionalLogic {
  show : boolean
  conditions : {
    field : string
    operator : 'equals' | 'not_equals' | 'contains' | 'in' | 'not_in'
    value : any
  }[]
  logic : 'and' | 'or'
}

export interface FormField {
  id : string
  formConfigId ?: number
  fieldKey : string
  fieldLabel : string
  fieldType : 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'datetime' | 'number' | 'email' | 'phone' | 'file' | 'image'
  fieldPlaceholder ?: string
  fieldOptions ?: FieldOptions
  fieldDefaultValue ?: any
  validationRules ?: ValidationRules
  isRequired : boolean
  isDisabled : boolean
  isReadonly : boolean
  gridSpan : number
  sortOrder : number
  cssClass ?: string
  fieldStyle ?: Record<string, any>
  conditionalLogic ?: ConditionalLogic
  status : number
  remark ?: string
}

export interface ComponentItem {
  type : string
  label : string
  icon : string
  category ?: string
}

export interface FormTemplate {
  id ?: number
  templateName : string
  templateDescription ?: string
  templateConfig : {
    formConfig : FormConfig
    fields : FormField[]
  }
  templateType : 'system' | 'custom'
  isPublic : boolean
  usageCount ?: number
  status : number
}

export interface FormBuilderData {
  formConfig : FormConfig
  fields : FormField[]
}

// 表单验证结果
export interface ValidationResult {
  isValid : boolean
  errors : string[]
  warnings ?: string[]
}

// 表单预览数据
export interface PreviewData {
  html : string
  formData : FormBuilderData
}
