// 布尔值类型转换工具函数 - 解决反选问题
// utils/booleanConverter.ts

/**
 * 将各种类型的值转换为标准的布尔值
 * 主要解决数据库存储的 0/1、字符串 '0'/'1'、'true'/'false' 等与前端布尔值不匹配的问题
 */
export function normalizeBoolean(value: any): boolean {
  // 如果已经是严格的布尔值，直接返回
  if (value === true || value === false) {
    return value
  }
  
  // 处理数字类型 (数据库中常见)
  if (typeof value === 'number') {
    return value === 1
  }
  
  // 处理字符串类型
  if (typeof value === 'string') {
    const lowerValue = value.toLowerCase()
    return lowerValue === '1' || lowerValue === 'true' || lowerValue === 'yes'
  }
  
  // 其他情况返回 false
  return false
}

/**
 * 将布尔值转换为数据库存储格式 (通常是0/1)
 */
export function booleanToNumber(value: boolean): number {
  return value ? 1 : 0
}

/**
 * 批量转换对象中的布尔字段
 */
export function normalizeBooleanFields<T extends Record<string, any>>(
  obj: T, 
  booleanFields: (keyof T)[]
): T {
  const result = { ...obj }
  
  booleanFields.forEach(field => {
    if (field in result) {
      result[field] = normalizeBoolean(result[field])
    }
  })
  
  return result
}

/**
 * 表单字段专用的布尔值规范化函数
 */
export function normalizeFormFieldBooleans(field: any) {
  return {
    ...field,
    isRequired: normalizeBoolean(field.isRequired),
    isDisabled: normalizeBoolean(field.isDisabled),
    isReadonly: normalizeBoolean(field.isReadonly),
    status: Number(field.status) || 1
  }
}

/**
 * Element Plus Switch 组件的值修正 Hook
 * 确保 Switch 组件的 active-value 和 inactive-value 与绑定值完全匹配
 */
export function useBooleanSwitch(initialValue: any) {
  const switchValue = ref(normalizeBoolean(initialValue))
  
  // 监听外部值变化
  const updateValue = (newValue: any) => {
    const normalized = normalizeBoolean(newValue)
    if (switchValue.value !== normalized) {
      switchValue.value = normalized
    }
  }
  
  return {
    switchValue: readonly(switchValue),
    updateValue,
    // 为 Element Switch 提供的标准配置
    switchProps: {
      activeValue: true,
      inactiveValue: false
    }
  }
}

// 调试辅助函数
export function debugBooleanConversion(originalValue: any, fieldName?: string) {
  const converted = normalizeBoolean(originalValue)
  console.group(`布尔值转换调试${fieldName ? ` - ${fieldName}` : ''}`)
  console.log('原始值:', originalValue, '类型:', typeof originalValue)
  console.log('转换后:', converted, '类型:', typeof converted)
  console.log('是否发生转换:', originalValue !== converted)
  console.groupEnd()
  return converted
}