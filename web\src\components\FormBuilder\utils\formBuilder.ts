// 表单构建器工具函数
import type { FormField, FieldOptions, ValidationRules } from '../types/form'

// 生成唯一字段ID
export const generateFieldId = (): string => {
  return `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 获取字段类型的默认配置
export const getDefaultFieldConfig = (fieldType: string) => {
  const configs: Record<string, { fieldOptions?: FieldOptions; validationRules?: ValidationRules }> = {
    text: {
      validationRules: {
        maxLength: { value: 255, message: '长度不能超过255个字符' }
      }
    },
    textarea: {
      fieldOptions: {
        rows: 3,
        showWordLimit: true
      },
      validationRules: {
        maxLength: { value: 500, message: '长度不能超过500个字符' }
      }
    },
    select: {
      fieldOptions: {
        options: [
          { label: '选项1', value: 'option1' },
          { label: '选项2', value: 'option2' }
        ],
        clearable: true,
        filterable: false,
        multiple: false
      }
    },
    radio: {
      fieldOptions: {
        options: [
          { label: '选项1', value: 'option1' },
          { label: '选项2', value: 'option2' }
        ]
      }
    },
    checkbox: {
      fieldOptions: {
        options: [
          { label: '选项1', value: 'option1' },
          { label: '选项2', value: 'option2' }
        ]
      }
    },
    date: {
      fieldOptions: {
        format: 'YYYY-MM-DD'
      }
    },
    datetime: {
      fieldOptions: {
        format: 'YYYY-MM-DD HH:mm:ss'
      }
    },
    number: {
      validationRules: {
        min: { value: 0, message: '值不能小于0' }
      }
    },
    email: {
      validationRules: {
        email: { value: true, message: '请输入正确的邮箱格式' }
      }
    },
    phone: {
      validationRules: {
        pattern: { value: '^1[3-9]\\d{9}$', message: '请输入正确的手机号码' }
      }
    },
    file: {
      fieldOptions: {
        accept: '.pdf,.doc,.docx,.xls,.xlsx',
        maxSize: 5,
        maxCount: 1,
        fileType: ['pdf', 'doc', 'docx', 'xls', 'xlsx']
      }
    },
    image: {
      fieldOptions: {
        accept: '.jpg,.jpeg,.png,.gif',
        maxSize: 2,
        maxCount: 1,
        fileType: ['jpg', 'jpeg', 'png', 'gif']
      }
    }
  }
  
  return configs[fieldType] || {}
}

// 生成默认字段键名
export const generateFieldKey = (fieldType: string, existingKeys: string[]): string => {
  const baseKey = fieldType
  let counter = 1
  let fieldKey = baseKey
  
  while (existingKeys.includes(fieldKey)) {
    fieldKey = `${baseKey}_${counter}`
    counter++
  }
  
  return fieldKey
}

// 验证字段配置
export const validateField = (field: FormField): string[] => {
  const errors: string[] = []
  
  // 基础验证
  if (!field.fieldKey) {
    errors.push('字段键名不能为空')
  }
  
  if (!field.fieldLabel) {
    errors.push('字段标签不能为空')
  }
  
  if (!field.fieldType) {
    errors.push('字段类型不能为空')
  }
  
  // 字段键名格式验证
  if (field.fieldKey && !/^[a-zA-Z][a-zA-Z0-9_]*$/.test(field.fieldKey)) {
    errors.push('字段键名格式不正确，应以字母开头，只能包含字母、数字和下划线')
  }
  
  // 选择类字段验证
  if (['select', 'radio', 'checkbox'].includes(field.fieldType)) {
    if (!field.fieldOptions?.options || field.fieldOptions.options.length === 0) {
      errors.push('选择类字段必须配置选项')
    } else {
      // 检查选项值重复
      const values = field.fieldOptions.options.map(opt => opt.value)
      const uniqueValues = new Set(values)
      if (values.length !== uniqueValues.size) {
        errors.push('选项值不能重复')
      }
    }
  }
  
  // 文件上传字段验证
  if (['file', 'image'].includes(field.fieldType)) {
    if (field.fieldOptions?.maxSize && field.fieldOptions.maxSize <= 0) {
      errors.push('文件大小限制必须大于0')
    }
    
    if (field.fieldOptions?.maxCount && field.fieldOptions.maxCount <= 0) {
      errors.push('文件数量限制必须大于0')
    }
  }
  
  // 数字字段验证
  if (field.fieldType === 'number') {
    const minRule = field.validationRules?.min
    const maxRule = field.validationRules?.max
    
    if (minRule && maxRule && minRule.value >= maxRule.value) {
      errors.push('最小值必须小于最大值')
    }
  }
  
  return errors
}

// 验证整个表单配置
export const validateFormConfig = (fields: FormField[]): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []
  const fieldKeys = new Set<string>()
  
  if (fields.length === 0) {
    errors.push('表单至少需要一个字段')
  }
  
  fields.forEach((field, index) => {
    // 检查字段键名重复
    if (fieldKeys.has(field.fieldKey)) {
      errors.push(`字段"${field.fieldLabel}"的键名"${field.fieldKey}"重复`)
    } else {
      fieldKeys.add(field.fieldKey)
    }
    
    // 验证单个字段
    const fieldErrors = validateField(field)
    fieldErrors.forEach(error => {
      errors.push(`第${index + 1}个字段: ${error}`)
    })
  })
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// 生成表单验证规则
export const generateFormRules = (fields: FormField[]) => {
  const rules: Record<string, any[]> = {}
  
  fields.forEach(field => {
    const fieldRules: any[] = []
    
    // 必填验证
    if (field.isRequired) {
      fieldRules.push({
        required: true,
        message: field.validationRules?.required?.message || `请输入${field.fieldLabel}`,
        trigger: ['blur', 'change']
      })
    }
    
    // 字符串长度验证
    if (field.validationRules?.minLength) {
      fieldRules.push({
        min: field.validationRules.minLength.value,
        message: field.validationRules.minLength.message,
        trigger: 'blur'
      })
    }
    
    if (field.validationRules?.maxLength) {
      fieldRules.push({
        max: field.validationRules.maxLength.value,
        message: field.validationRules.maxLength.message,
        trigger: 'blur'
      })
    }
    
    // 数值范围验证
    if (field.validationRules?.min) {
      fieldRules.push({
        type: 'number',
        min: field.validationRules.min.value,
        message: field.validationRules.min.message,
        trigger: 'change'
      })
    }
    
    if (field.validationRules?.max) {
      fieldRules.push({
        type: 'number',
        max: field.validationRules.max.value,
        message: field.validationRules.max.message,
        trigger: 'change'
      })
    }
    
    // 正则表达式验证
    if (field.validationRules?.pattern) {
      fieldRules.push({
        pattern: new RegExp(field.validationRules.pattern.value),
        message: field.validationRules.pattern.message,
        trigger: 'blur'
      })
    }
    
    // 邮箱验证
    if (field.validationRules?.email?.value) {
      fieldRules.push({
        type: 'email',
        message: field.validationRules.email.message,
        trigger: 'blur'
      })
    }
    
    if (fieldRules.length > 0) {
      rules[field.fieldKey] = fieldRules
    }
  })
  
  return rules
}

// 生成默认表单数据
export const generateDefaultFormData = (fields: FormField[]) => {
  const formData: Record<string, any> = {}
  
  fields.forEach(field => {
    if (field.fieldDefaultValue !== undefined && field.fieldDefaultValue !== '') {
      formData[field.fieldKey] = field.fieldDefaultValue
    } else {
      // 根据字段类型设置默认值
      switch (field.fieldType) {
        case 'checkbox':
          formData[field.fieldKey] = []
          break
        case 'number':
          formData[field.fieldKey] = undefined
          break
        default:
          formData[field.fieldKey] = ''
      }
    }
  })
  
  return formData
}

// 处理条件逻辑
export const evaluateConditionalLogic = (
  field: FormField, 
  formData: Record<string, any>
): boolean => {
  if (!field.conditionalLogic) return true
  
  const { conditions, logic, show } = field.conditionalLogic
  
  const results = conditions.map(condition => {
    const fieldValue = formData[condition.field]
    
    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value
      case 'not_equals':
        return fieldValue !== condition.value
      case 'contains':
        return String(fieldValue).includes(String(condition.value))
      case 'in':
        return Array.isArray(condition.value) && condition.value.includes(fieldValue)
      case 'not_in':
        return Array.isArray(condition.value) && !condition.value.includes(fieldValue)
      default:
        return true
    }
  })
  
  const conditionMet = logic === 'and' 
    ? results.every(result => result)
    : results.some(result => result)
  
  return show ? conditionMet : !conditionMet
}

// 获取字段的显示值
export const getFieldDisplayValue = (field: FormField, value: any): string => {
  if (value === null || value === undefined || value === '') {
    return ''
  }
  
  switch (field.fieldType) {
    case 'select':
    case 'radio':
      const option = field.fieldOptions?.options?.find(opt => opt.value === value)
      return option ? option.label : String(value)
      
    case 'checkbox':
      if (Array.isArray(value)) {
        const labels = value.map(val => {
          const option = field.fieldOptions?.options?.find(opt => opt.value === val)
          return option ? option.label : String(val)
        })
        return labels.join(', ')
      }
      return String(value)
      
    case 'date':
    case 'datetime':
      if (value instanceof Date) {
        const format = field.fieldOptions?.format || 'YYYY-MM-DD'
        // 这里可以使用日期格式化库，比如 dayjs
        return value.toLocaleDateString()
      }
      return String(value)
      
    default:
      return String(value)
  }
}

// 深度克隆对象
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as unknown as T
  }
  
  const cloned = {} as T
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      cloned[key] = deepClone(obj[key])
    }
  }
  
  return cloned
}