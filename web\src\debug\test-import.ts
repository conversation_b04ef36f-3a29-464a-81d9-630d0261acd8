// 测试动态导入修复
console.log('Testing dynamic import fix...');

// 测试 import.meta.glob 配置
const modules = import.meta.glob('/src/views/**/*.vue');
console.log('Available modules:', Object.keys(modules));

// 查找事件管理页面
const eventModules = Object.keys(modules).filter(path => path.includes('event'));
console.log('Event related modules:', eventModules);

// 测试加载特定组件
const eventEventsPath = '/src/views/event/events/index.vue';
if (modules[eventEventsPath]) {
  console.log('✅ Found event/events/index.vue module');
  modules[eventEventsPath]().then(module => {
    console.log('✅ Successfully loaded module:', module);
  }).catch(error => {
    console.error('❌ Failed to load module:', error);
  });
} else {
  console.error('❌ Module not found:', eventEventsPath);
  console.log('Available paths that might match:');
  Object.keys(modules).forEach(path => {
    if (path.includes('event') || path.includes('Events')) {
      console.log(' -', path);
    }
  });
}

export {}; // 使这个文件成为模块