/**
 * 后台返回的路由动态生成name 解决缓存问题
 * 感谢 @fourteendp
 * 详见 https://github.com/vbenjs/vue-vben-admin/issues/3927
 */
import { Component, defineComponent, h } from 'vue';

interface Options {
  name?: string;
}

export function createCustomNameComponent(loader: () => Promise<any>, options: Options = {}): () => Promise<Component> {
  const { name } = options;
  let component: Component | null = null;
  let loadPromise: Promise<void> | null = null;

  const load = async () => {
    if (loadPromise) {
      return loadPromise;
    }
    
    loadPromise = (async () => {
      try {
        const loadedModule = await loader();
        component = loadedModule.default || loadedModule;
        if (!component) {
          throw new Error(`Component module does not have a default export`);
        }
      } catch (error) {
        console.error(`Cannot resolve component ${name}, error:`, error);
        // 创建一个错误组件来显示错误信息
        component = defineComponent({
          name: `Error_${name}`,
          render() {
            return h('div', {
              style: 'color: red; padding: 20px; border: 1px solid red; margin: 10px;'
            }, [
              h('h3', `组件加载失败: ${name}`),
              h('p', `错误信息: ${error.message}`),
              h('p', '请检查组件路径或联系开发人员')
            ]);
          }
        });
      }
    })();
    
    return loadPromise;
  };

  return async () => {
    if (!component) {
      await load();
    }

    return Promise.resolve(
      defineComponent({
        name,
        render() {
          return h(component as Component);
        }
      })
    );
  };
}
