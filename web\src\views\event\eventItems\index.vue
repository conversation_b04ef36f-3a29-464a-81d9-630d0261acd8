<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="所属赛事ID" prop="eventId">
              <el-input v-model="queryParams.eventId" placeholder="请输入所属赛事ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入项目名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目描述" prop="description">
              <el-input v-model="queryParams.description" placeholder="请输入项目描述" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="最大参与人数" prop="maxParticipants">
              <el-input v-model="queryParams.maxParticipants" placeholder="请输入最大参与人数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="当前报名人数" prop="currentParticipants">
              <el-input v-model="queryParams.currentParticipants" placeholder="请输入当前报名人数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="额外费用" prop="additionalFee">
              <el-input v-model="queryParams.additionalFee" placeholder="请输入额外费用" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="最小年龄限制" prop="ageLimitMin">
              <el-input v-model="queryParams.ageLimitMin" placeholder="请输入最小年龄限制" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="最大年龄限制" prop="ageLimitMax">
              <el-input v-model="queryParams.ageLimitMax" placeholder="请输入最大年龄限制" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="性别限制" prop="genderLimit">
              <el-select v-model="queryParams.genderLimit" placeholder="请选择性别限制" clearable >
                <el-option v-for="dict in event_gender_limit" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="排序" prop="sortOrder">
              <el-input v-model="queryParams.sortOrder" placeholder="请输入排序" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['event:eventItems:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['event:eventItems:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['event:eventItems:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['event:eventItems:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="eventItemsList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="项目ID" align="center" prop="id" v-if="true" />
        <el-table-column label="所属赛事ID" align="center" prop="eventId" />
        <el-table-column label="项目名称" align="center" prop="name" />
        <el-table-column label="项目描述" align="center" prop="description" />
        <el-table-column label="最大参与人数" align="center" prop="maxParticipants" />
        <el-table-column label="当前报名人数" align="center" prop="currentParticipants" />
        <el-table-column label="额外费用" align="center" prop="additionalFee" />
        <el-table-column label="最小年龄限制" align="center" prop="ageLimitMin" />
        <el-table-column label="最大年龄限制" align="center" prop="ageLimitMax" />
        <el-table-column label="性别限制" align="center" prop="genderLimit">
          <template #default="scope">
            <dict-tag :options="event_gender_limit" :value="scope.row.genderLimit"/>
          </template>
        </el-table-column>
        <el-table-column label="特殊要求" align="center" prop="requirements" />
        <el-table-column label="排序" align="center" prop="sortOrder" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['event:eventItems:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['event:eventItems:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改赛事项目对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="eventItemsFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="所属赛事ID" prop="eventId">
          <el-input v-model="form.eventId" placeholder="请输入所属赛事ID" />
        </el-form-item>
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="项目描述" prop="description">
            <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="最大参与人数" prop="maxParticipants">
          <el-input v-model="form.maxParticipants" placeholder="请输入最大参与人数" />
        </el-form-item>
        <el-form-item label="当前报名人数" prop="currentParticipants">
          <el-input v-model="form.currentParticipants" placeholder="请输入当前报名人数" />
        </el-form-item>
        <el-form-item label="额外费用" prop="additionalFee">
          <el-input v-model="form.additionalFee" placeholder="请输入额外费用" />
        </el-form-item>
        <el-form-item label="最小年龄限制" prop="ageLimitMin">
          <el-input v-model="form.ageLimitMin" placeholder="请输入最小年龄限制" />
        </el-form-item>
        <el-form-item label="最大年龄限制" prop="ageLimitMax">
          <el-input v-model="form.ageLimitMax" placeholder="请输入最大年龄限制" />
        </el-form-item>
        <el-form-item label="性别限制" prop="genderLimit">
          <el-select v-model="form.genderLimit" placeholder="请选择性别限制">
            <el-option
                v-for="dict in event_gender_limit"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input v-model="form.sortOrder" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="EventItems" lang="ts">
import { listEventItems, getEventItems, delEventItems, addEventItems, updateEventItems } from '@/api/event/eventItems';
import { EventItemsVO, EventItemsQuery, EventItemsForm } from '@/api/event/eventItems/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { event_gender_limit } = toRefs<any>(proxy?.useDict('event_gender_limit'));

const eventItemsList = ref<EventItemsVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const eventItemsFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: EventItemsForm = {
  id: undefined,
  eventId: undefined,
  name: undefined,
  description: undefined,
  maxParticipants: undefined,
  currentParticipants: undefined,
  additionalFee: undefined,
  ageLimitMin: undefined,
  ageLimitMax: undefined,
  genderLimit: undefined,
  requirements: undefined,
  sortOrder: undefined,
  remark: undefined,
}
const data = reactive<PageData<EventItemsForm, EventItemsQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    eventId: undefined,
    name: undefined,
    description: undefined,
    maxParticipants: undefined,
    currentParticipants: undefined,
    additionalFee: undefined,
    ageLimitMin: undefined,
    ageLimitMax: undefined,
    genderLimit: undefined,
    requirements: undefined,
    sortOrder: undefined,
    params: {
    }
  },
  rules: {
    description: [
      { required: true, message: "项目描述不能为空", trigger: "blur" }
    ],
    maxParticipants: [
      { required: true, message: "最大参与人数不能为空", trigger: "blur" }
    ],
    ageLimitMin: [
      { required: true, message: "最小年龄限制不能为空", trigger: "blur" }
    ],
    ageLimitMax: [
      { required: true, message: "最大年龄限制不能为空", trigger: "blur" }
    ],
    requirements: [
      { required: true, message: "特殊要求不能为空", trigger: "blur" }
    ],
    remark: [
      { required: true, message: "备注不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询赛事项目列表 */
const getList = async () => {
  loading.value = true;
  const res = await listEventItems(queryParams.value);
  eventItemsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  eventItemsFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: EventItemsVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加赛事项目";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: EventItemsVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getEventItems(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改赛事项目";
}

/** 提交按钮 */
const submitForm = () => {
  eventItemsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateEventItems(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addEventItems(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: EventItemsVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除赛事项目编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delEventItems(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('event/eventItems/export', {
    ...queryParams.value
  }, `eventItems_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
