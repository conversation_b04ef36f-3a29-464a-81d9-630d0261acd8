<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="微信OpenID" prop="openid">
              <el-input v-model="queryParams.openid" placeholder="请输入微信OpenID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="微信UnionID" prop="unionid">
              <el-input v-model="queryParams.unionid" placeholder="请输入微信UnionID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="queryParams.nickname" placeholder="请输入昵称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="头像URL" prop="avatar">
              <el-input v-model="queryParams.avatar" placeholder="请输入头像URL" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="queryParams.phone" placeholder="请输入手机号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="queryParams.email" placeholder="请输入邮箱" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="queryParams.realName" placeholder="请输入真实姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="身份证号存储)" prop="idCard">
              <el-input v-model="queryParams.idCard" placeholder="请输入身份证号存储)" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="性别" prop="gender">
              <el-select v-model="queryParams.gender" placeholder="请选择性别" clearable >
                <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="生日" prop="birthday">
              <el-date-picker clearable
                v-model="queryParams.birthday"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择生日"
              />
            </el-form-item>
            <el-form-item label="地址" prop="address">
              <el-input v-model="queryParams.address" placeholder="请输入地址" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="紧急联系人" prop="emergencyContact">
              <el-input v-model="queryParams.emergencyContact" placeholder="请输入紧急联系人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="用户状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择用户状态" clearable >
                <el-option v-for="dict in user_status" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['event:eventUsers:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['event:eventUsers:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['event:eventUsers:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['event:eventUsers:export']">导出</el-button>
          </el-col>
          <el-col :span="2">
            <el-input
              v-model="quickSearchKeyword"
              placeholder="快速搜索(姓名/手机号/邮箱)"
              prefix-icon="Search"
              @keyup.enter="handleQuickSearch"
              @clear="handleQuickSearchClear"
              clearable
              style="width: 250px"
            />
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="eventUsersList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="用户ID" align="center" prop="id" v-if="true" />
        <el-table-column label="微信OpenID" align="center" prop="openid" />
        <el-table-column label="微信UnionID" align="center" prop="unionid" />
        <el-table-column label="昵称" align="center" prop="nickname" />
        <el-table-column label="头像URL" align="center" prop="avatar" />
        <el-table-column label="手机号" align="center" prop="phone" />
        <el-table-column label="邮箱" align="center" prop="email" />
        <el-table-column label="真实姓名" align="center" prop="realName" />
        <el-table-column label="身份证号存储)" align="center" prop="idCard" />
        <el-table-column label="性别" align="center" prop="gender">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.gender"/>
          </template>
        </el-table-column>
        <el-table-column label="生日" align="center" prop="birthday" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.birthday, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="地址" align="center" prop="address" />
        <el-table-column label="紧急联系人" align="center" prop="emergencyContact" />
        <el-table-column label="用户状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="user_status" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['event:eventUsers:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['event:eventUsers:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改赛事用户列表对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="eventUsersFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="微信OpenID" prop="openid">
          <el-input v-model="form.openid" placeholder="请输入微信OpenID" />
        </el-form-item>
        <el-form-item label="微信UnionID" prop="unionid">
          <el-input v-model="form.unionid" placeholder="请输入微信UnionID" />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="form.nickname" placeholder="请输入昵称" />
        </el-form-item>
        <el-form-item label="头像URL" prop="avatar">
            <el-input v-model="form.avatar" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="form.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="身份证号存储)" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入身份证号存储)" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select v-model="form.gender" placeholder="请选择性别">
            <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="生日" prop="birthday">
          <el-date-picker clearable
            v-model="form.birthday"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择生日">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="地址" prop="address">
            <el-input v-model="form.address" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="紧急联系人" prop="emergencyContact">
          <el-input v-model="form.emergencyContact" placeholder="请输入紧急联系人" />
        </el-form-item>
        <el-form-item label="用户状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择用户状态">
            <el-option
                v-for="dict in user_status"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="EventUsers" lang="ts">
import { listEventUsers, getEventUsers, delEventUsers, addEventUsers, updateEventUsers } from '@/api/event/eventUsers';
import { EventUsersVO, EventUsersQuery, EventUsersForm } from '@/api/event/eventUsers/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { user_status, sys_user_sex } = toRefs<any>(proxy?.useDict('user_status', 'sys_user_sex'));

const eventUsersList = ref<EventUsersVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const quickSearchKeyword = ref('');

const queryFormRef = ref<ElFormInstance>();
const eventUsersFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: EventUsersForm = {
  id: undefined,
  openid: undefined,
  unionid: undefined,
  nickname: undefined,
  avatar: undefined,
  phone: undefined,
  email: undefined,
  realName: undefined,
  idCard: undefined,
  gender: undefined,
  birthday: undefined,
  address: undefined,
  emergencyContact: undefined,
  status: undefined,
  remark: undefined,
}
const data = reactive<PageData<EventUsersForm, EventUsersQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    openid: undefined,
    unionid: undefined,
    nickname: undefined,
    avatar: undefined,
    phone: undefined,
    email: undefined,
    realName: undefined,
    idCard: undefined,
    gender: undefined,
    birthday: undefined,
    address: undefined,
    emergencyContact: undefined,
    status: undefined,
    params: {
    }
  },
  rules: {
    openid: [
      { required: true, message: "微信OpenID不能为空", trigger: "blur" }
    ],
    unionid: [
      { required: true, message: "微信UnionID不能为空", trigger: "blur" }
    ],
    nickname: [
      { required: true, message: "昵称不能为空", trigger: "blur" }
    ],
    avatar: [
      { required: true, message: "头像URL不能为空", trigger: "blur" }
    ],
    phone: [
      { required: true, message: "手机号不能为空", trigger: "blur" }
    ],
    email: [
      { required: true, message: "邮箱不能为空", trigger: "blur" }
    ],
    realName: [
      { required: true, message: "真实姓名不能为空", trigger: "blur" }
    ],
    idCard: [
      { required: true, message: "身份证号存储)不能为空", trigger: "blur" }
    ],
    birthday: [
      { required: true, message: "生日不能为空", trigger: "blur" }
    ],
    address: [
      { required: true, message: "地址不能为空", trigger: "blur" }
    ],
    emergencyContact: [
      { required: true, message: "紧急联系人不能为空", trigger: "blur" }
    ],
    remark: [
      { required: true, message: "备注不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询赛事用户列表列表 */
const getList = async () => {
  loading.value = true;
  const res = await listEventUsers(queryParams.value);
  eventUsersList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  eventUsersFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 快速搜索 */
const handleQuickSearch = async () => {
  if (!quickSearchKeyword.value.trim()) {
    proxy?.$modal.msgWarning('请输入搜索关键词');
    return;
  }
  
  try {
    loading.value = true;
    // 模拟快速搜索，实际项目中应该调用搜索API
    const searchParams = {
      pageNum: 1,
      pageSize: queryParams.value.pageSize,
      realName: quickSearchKeyword.value,
      phone: quickSearchKeyword.value,
      email: quickSearchKeyword.value
    };
    
    const res = await listEventUsers(searchParams);
    eventUsersList.value = res.rows;
    total.value = res.total;
    queryParams.value.pageNum = 1;
  } catch (error) {
    proxy?.$modal.msgError('搜索失败');
  } finally {
    loading.value = false;
  }
}

/** 清除快速搜索 */
const handleQuickSearchClear = () => {
  quickSearchKeyword.value = '';
  getList();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: EventUsersVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加赛事用户列表";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: EventUsersVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getEventUsers(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改赛事用户列表";
}

/** 提交按钮 */
const submitForm = () => {
  eventUsersFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateEventUsers(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addEventUsers(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: EventUsersVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除赛事用户列表编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delEventUsers(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('event/eventUsers/export', {
    ...queryParams.value
  }, `eventUsers_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
