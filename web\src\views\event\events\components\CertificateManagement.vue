<template>
  <el-dialog :title="certificateManageDialog.title" v-model="certificateManageDialog.visible" width="95%"
    append-to-body class="certificate-manage-dialog">
    <el-tabs v-model="certificateManageDialog.activeTab" type="border-card">
      <!-- 模板管理标签页 -->
      <el-tab-pane label="模板管理" name="template">
        <div class="template-management">
          <!-- 搜索区域 -->
          <div class="mb-4">
            <el-card shadow="hover">
              <el-form :inline="true" :model="templatesQueryParams">
                <el-form-item label="模板名称">
                  <el-input v-model="templatesQueryParams.templateName" placeholder="请输入模板名称" clearable
                    style="width: 200px" />
                </el-form-item>
                <el-form-item label="模板类型">
                  <el-select v-model="templatesQueryParams.templateType" placeholder="请选择模板类型" clearable
                    style="width: 150px">
                    <el-option label="奖状" :value="1" />
                    <el-option label="证书" :value="2" />
                  </el-select>
                </el-form-item>
                <el-form-item label="启用状态">
                  <el-select v-model="templatesQueryParams.isEnabled" placeholder="请选择状态" clearable
                    style="width: 120px">
                    <el-option label="启用" :value="1" />
                    <el-option label="停用" :value="0" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleTemplateQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetTemplateQuery">重置</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>

          <!-- 操作按钮 -->
          <div class="mb-4">
            <el-button type="primary" icon="Plus" @click="handleAddTemplate">新增模板</el-button>
            <el-button type="success" icon="Upload" @click="handleBatchImportTemplate">批量导入</el-button>
          </div>

          <!-- 模板列表 -->
          <el-table v-loading="templatesLoading" :data="templatesList" border style="width: 100%">
            <el-table-column label="模板信息" min-width="250">
              <template #default="scope">
                <div class="template-info">
                  <div class="template-name">{{ scope.row.templateName }}</div>
                  <div class="template-type">
                    <el-tag :type="scope.row.templateType === 1 ? 'warning' : 'success'" size="small">
                      {{ scope.row.templateType === 1 ? '奖状' : '证书' }}
                    </el-tag>
                    <el-tag v-if="scope.row.isDefault" type="danger" size="small" style="margin-left: 5px">
                      默认
                    </el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="模板预览" width="120" align="center">
              <template #default="scope">
                <el-image v-if="scope.row.templateImageUrl" :src="scope.row.templateImageUrl"
                  :preview-src-list="[scope.row.templateImageUrl]" fit="cover"
                  style="width: 80px; height: 60px; border-radius: 4px;" />
                <span v-else class="text-muted">暂无</span>
              </template>
            </el-table-column>

            <el-table-column label="启用状态" width="100" align="center">
              <template #default="scope">
                <el-switch v-model="scope.row.isEnabled" :active-value="1" :inactive-value="0"
                  @change="handleTemplateStatusChange(scope.row)" />
              </template>
            </el-table-column>

            <el-table-column label="排序" width="80" align="center" prop="sortOrder" />

            <el-table-column label="创建时间" width="160" align="center">
              <template #default="scope">
                {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}
              </template>
            </el-table-column>

            <el-table-column label="操作" align="center" fixed="right">
              <template #default="scope">
                <el-button-group size="small">
                  <el-tooltip content="查看" placement="top">
                    <el-button link type="primary" icon="View" @click="handleViewTemplate(scope.row)" />
                  </el-tooltip>
                  <el-tooltip content="编辑" placement="top">
                    <el-button link type="primary" icon="Edit" @click="handleEditTemplate(scope.row)" />
                  </el-tooltip>
                  <el-tooltip content="参数配置" placement="top">
                    <el-button link type="success" icon="Setting" @click="handleTemplateParams(scope.row)" />
                  </el-tooltip>
                  <el-tooltip content="删除" placement="top">
                    <el-button link type="danger" icon="Delete" @click="handleDeleteTemplate(scope.row)" />
                  </el-tooltip>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="mt-4">
            <pagination v-show="templatesTotal > 0" :total="templatesTotal"
              v-model:page="templatesQueryParams.pageNum" v-model:limit="templatesQueryParams.pageSize"
              @pagination="getTemplatesList" />
          </div>
        </div>
      </el-tab-pane>

      <!-- 证书生成标签页 -->
      <el-tab-pane label="证书生成" name="generate">
        <div class="certificate-generation">
          <!-- 证书配置区域 -->
          <div class="config-section mb-6">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span class="section-title">
                    <el-icon>
                      <Setting />
                    </el-icon>
                    证书生成配置
                  </span>
                </div>
              </template>

              <div class="config-content">
                <el-form :inline="true" label-width="120px">
                  <el-form-item label="选择模板:">
                    <el-select v-model="selectedTemplateId" placeholder="请选择证书模板" style="width: 300px"
                      @change="handleTemplateChange">
                      <el-option v-for="template in enabledTemplates" :key="template.id"
                        :label="template.templateName" :value="template.id">
                        <div class="template-option">
                          <span>{{ template.templateName }}</span>
                          <el-tag :type="template.templateType === 1 ? 'warning' : 'success'" size="small">
                            {{ template.templateType === 1 ? '奖状' : '证书' }}
                          </el-tag>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" icon="Edit" @click="handleBatchGenerate"
                      :disabled="!selectedTemplateId">
                      批量生成证书
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-card>
          </div>

          <!-- 生成记录区域 -->
          <div class="records-section">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span class="section-title">
                    <el-icon>
                      <Document />
                    </el-icon>
                    生成记录
                  </span>
                </div>
              </template>

              <!-- 搜索区域 -->
              <div class="mb-4">
                <el-form :inline="true" label-width="120px" :model="generationQueryParams">
                  <el-form-item label="参赛者姓名">
                    <el-input v-model="generationQueryParams.participantName" placeholder="请输入姓名" clearable
                      style="width: 180px" />
                  </el-form-item>
                  <el-form-item label="生成状态">
                    <el-select v-model="generationQueryParams.generationStatus" placeholder="请选择状态" clearable
                      style="width: 150px">
                      <el-option label="待生成" :value="0" />
                      <el-option label="生成中" :value="1" />
                      <el-option label="生成成功" :value="2" />
                      <el-option label="生成失败" :value="3" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleGenerationQuery">搜索</el-button>
                    <el-button icon="Refresh" @click="resetGenerationQuery">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>

              <p class="text-muted">证书生成功能开发中...</p>
            </el-card>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="certificateManageDialog.visible = false">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="CertificateManagement" lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus';

const emits = defineEmits<{
  (event: 'success'): void
}>()

// 证书管理对话框
const certificateManageDialog = reactive({
  visible: false,
  title: '',
  eventId: null as string | number | null,
  eventTitle: '',
  activeTab: 'template'
});

// 模板相关数据
const templatesList = ref<any[]>([]);
const templatesLoading = ref(false);
const templatesTotal = ref(0);
const templatesQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  templateName: '',
  templateType: undefined,
  isEnabled: undefined,
  eventId: undefined
});

// 生成相关数据
const generationQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  participantName: '',
  generationStatus: undefined,
  eventId: undefined
});

const selectedTemplateId = ref<string | number | null>(null);

// 计算属性
const enabledTemplates = computed(() => {
  return templatesList.value.filter(t => t.isEnabled === 1);
});

/** 模板搜索 */
const handleTemplateQuery = () => {
  templatesQueryParams.value.pageNum = 1;
  getTemplatesList();
}

/** 重置模板搜索 */
const resetTemplateQuery = () => {
  templatesQueryParams.value = {
    pageNum: 1,
    pageSize: 10,
    templateName: '',
    templateType: undefined,
    isEnabled: undefined,
    eventId: certificateManageDialog.eventId
  };
  getTemplatesList();
}

/** 获取模板列表 */
const getTemplatesList = async () => {
  try {
    templatesLoading.value = true;
    // 这里应该调用实际的API
    // const res = await getCertificateTemplates(templatesQueryParams.value);
    // templatesList.value = res.rows;
    // templatesTotal.value = res.total;
    
    // 模拟数据
    templatesList.value = [];
    templatesTotal.value = 0;
  } catch (error) {
    console.error('获取模板列表失败:', error);
    ElMessage.error('获取模板列表失败');
  } finally {
    templatesLoading.value = false;
  }
}

/** 生成记录搜索 */
const handleGenerationQuery = () => {
  generationQueryParams.value.pageNum = 1;
  // 调用获取生成记录的方法
}

/** 重置生成记录搜索 */
const resetGenerationQuery = () => {
  generationQueryParams.value = {
    pageNum: 1,
    pageSize: 10,
    participantName: '',
    generationStatus: undefined,
    eventId: certificateManageDialog.eventId
  };
}

/** 添加模板 */
const handleAddTemplate = () => {
  ElMessage.info('添加模板功能开发中...');
}

/** 批量导入模板 */
const handleBatchImportTemplate = () => {
  ElMessage.info('批量导入功能开发中...');
}

/** 查看模板 */
const handleViewTemplate = (row: any) => {
  ElMessage.info('查看模板功能开发中...');
}

/** 编辑模板 */
const handleEditTemplate = (row: any) => {
  ElMessage.info('编辑模板功能开发中...');
}

/** 模板参数配置 */
const handleTemplateParams = (row: any) => {
  ElMessage.info('参数配置功能开发中...');
}

/** 删除模板 */
const handleDeleteTemplate = async (row: any) => {
  try {
    await ElMessageBox.confirm('确认删除该模板吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    ElMessage.success('删除成功');
    await getTemplatesList();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
}

/** 模板状态更改 */
const handleTemplateStatusChange = async (row: any) => {
  try {
    // await updateTemplateStatus(row.id, row.isEnabled);
    ElMessage.success('状态更新成功');
    emits('success');
  } catch (error) {
    console.error('更新状态失败:', error);
    ElMessage.error('更新状态失败');
  }
}

/** 模板选择改变 */
const handleTemplateChange = (value: string | number) => {
  selectedTemplateId.value = value;
}

/** 批量生成证书 */
const handleBatchGenerate = () => {
  ElMessage.info('批量生成功能开发中...');
}

const open = async (option: { eventId: string | number, eventTitle: string }) => {
  certificateManageDialog.eventId = option.eventId;
  certificateManageDialog.eventTitle = option.eventTitle;
  certificateManageDialog.title = `"${option.eventTitle}" 的奖状证书管理`;
  certificateManageDialog.activeTab = 'template';
  certificateManageDialog.visible = true;

  // 设置查询参数
  templatesQueryParams.value.eventId = option.eventId;
  generationQueryParams.value.eventId = option.eventId;

  // 加载数据
  await getTemplatesList();
}

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
@import '../styles/certificate-management.scss';
</style>