<template>
  <el-dialog :title="competitionDialog.title" v-model="competitionDialog.visible" width="800px" append-to-body>
    <div class="competition-result">
      <!-- 搜索区域 -->
      <div class="search-section mb-4">
        <el-form :inline="true" :model="queryParams">
          <el-form-item label="参赛者姓名">
            <el-input v-model="queryParams.participantName" placeholder="请输入参赛者姓名" clearable style="width: 200px" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section mb-4">
        <el-button type="primary" icon="Plus" @click="handleBatchSetResult">批量设置成绩</el-button>
        <el-button type="success" icon="Download" @click="handleExportResults">导出成绩</el-button>
      </div>

      <!-- 成绩列表 -->
      <el-table v-loading="loading" :data="resultsList" border style="width: 100%">
        <el-table-column type="selection" width="55" align="center" />
        
        <el-table-column label="参赛者" min-width="120">
          <template #default="scope">
            <div>
              <div class="participant-name">{{ scope.row.participantName }}</div>
              <div class="participant-phone">{{ scope.row.phone }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="项目" prop="projectName" width="150" />
        
        <el-table-column label="成绩" width="120" align="center">
          <template #default="scope">
            <div v-if="scope.row.result" class="result-display">
              <span class="score">{{ scope.row.result }}</span>
            </div>
            <el-tag v-else type="info" size="small">未录入</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="排名" width="80" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.ranking" :type="getRankingType(scope.row.ranking)" size="small">
              第{{ scope.row.ranking }}名
            </el-tag>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>

        <el-table-column label="获奖情况" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.award" :type="getAwardType(scope.row.award)" size="small">
              {{ scope.row.award }}
            </el-tag>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button-group size="small">
              <el-tooltip content="设置成绩" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleSetResult(scope.row)" />
              </el-tooltip>
              <el-tooltip content="查看详情" placement="top">
                <el-button link type="info" icon="View" @click="handleViewDetail(scope.row)" />
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section mt-4">
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getResultsList" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="competitionDialog.visible = false">关 闭</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 设置成绩对话框 -->
  <el-dialog title="设置成绩" v-model="resultEditDialog.visible" width="500px" append-to-body>
    <el-form :model="resultForm" label-width="100px">
      <el-form-item label="参赛者">
        <span>{{ resultForm.participantName }}</span>
      </el-form-item>
      <el-form-item label="项目">
        <span>{{ resultForm.projectName }}</span>
      </el-form-item>
      <el-form-item label="成绩" required>
        <el-input v-model="resultForm.result" placeholder="请输入成绩" />
      </el-form-item>
      <el-form-item label="排名">
        <el-input-number v-model="resultForm.ranking" :min="1" placeholder="请输入排名" style="width: 100%" />
      </el-form-item>
      <el-form-item label="获奖情况">
        <el-select v-model="resultForm.award" placeholder="请选择获奖情况" clearable style="width: 100%">
          <el-option label="一等奖" value="一等奖" />
          <el-option label="二等奖" value="二等奖" />
          <el-option label="三等奖" value="三等奖" />
          <el-option label="优秀奖" value="优秀奖" />
          <el-option label="参与奖" value="参与奖" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="resultForm.remark" type="textarea" :rows="3" placeholder="请输入备注" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="resultEditDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveResult">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="CompetitionResult" lang="ts">
import { ElMessage } from 'element-plus';

const emits = defineEmits<{
  (event: 'success'): void
}>()

// 成绩管理对话框
const competitionDialog = reactive({
  visible: false,
  title: '',
  eventId: null as string | number | null,
  eventTitle: ''
});

// 成绩编辑对话框
const resultEditDialog = reactive({
  visible: false
});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  eventId: undefined as string | number | undefined,
  participantName: ''
});

// 数据
const resultsList = ref<any[]>([]);
const loading = ref(false);
const total = ref(0);

// 表单数据
const resultForm = ref({
  id: '',
  participantName: '',
  projectName: '',
  result: '',
  ranking: undefined as number | undefined,
  award: '',
  remark: ''
});

/** 搜索 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getResultsList();
}

/** 重置 */
const resetQuery = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    eventId: competitionDialog.eventId,
    participantName: ''
  };
  getResultsList();
}

/** 获取成绩列表 */
const getResultsList = async () => {
  try {
    loading.value = true;
    // 这里应该调用实际的API
    // const res = await listCompetitionResults(queryParams.value);
    // resultsList.value = res.rows;
    // total.value = res.total;
    
    // 模拟数据
    resultsList.value = [];
    total.value = 0;
  } catch (error) {
    console.error('获取成绩列表失败:', error);
    ElMessage.error('获取成绩列表失败');
  } finally {
    loading.value = false;
  }
}

/** 设置成绩 */
const handleSetResult = (row: any) => {
  resultForm.value = {
    id: row.id,
    participantName: row.participantName,
    projectName: row.projectName,
    result: row.result || '',
    ranking: row.ranking,
    award: row.award || '',
    remark: row.remark || ''
  };
  resultEditDialog.visible = true;
}

/** 保存成绩 */
const handleSaveResult = async () => {
  try {
    // 这里应该调用实际的API
    // await updateCompetitionResult(resultForm.value);
    ElMessage.success('成绩设置成功');
    resultEditDialog.visible = false;
    await getResultsList();
    emits('success');
  } catch (error) {
    console.error('设置成绩失败:', error);
    ElMessage.error('设置成绩失败');
  }
}

/** 批量设置成绩 */
const handleBatchSetResult = () => {
  ElMessage.info('批量设置成绩功能开发中...');
}

/** 导出成绩 */
const handleExportResults = () => {
  ElMessage.info('导出成绩功能开发中...');
}

/** 查看详情 */
const handleViewDetail = (row: any) => {
  ElMessage.info('查看详情功能开发中...');
}

/** 获取排名类型 */
const getRankingType = (ranking: number) => {
  if (ranking <= 3) return 'danger';
  if (ranking <= 10) return 'warning';
  return 'info';
}

/** 获取获奖类型 */
const getAwardType = (award: string) => {
  switch (award) {
    case '一等奖': return 'danger';
    case '二等奖': return 'warning';
    case '三等奖': return 'success';
    default: return 'info';
  }
}

const open = (option: { eventId: string | number, eventTitle: string }) => {
  competitionDialog.eventId = option.eventId;
  competitionDialog.eventTitle = option.eventTitle;
  competitionDialog.title = `"${option.eventTitle}" 的成绩管理`;
  competitionDialog.visible = true;

  // 设置查询参数
  queryParams.value.eventId = option.eventId;
  queryParams.value.pageNum = 1;
  queryParams.value.participantName = '';

  // 加载数据
  getResultsList();
}

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.competition-result {
  .search-section,
  .action-section {
    margin-bottom: 16px;
  }

  .mb-4 {
    margin-bottom: 16px;
  }

  .mt-4 {
    margin-top: 16px;
  }

  .participant-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
  }

  .participant-phone {
    font-size: 12px;
    color: #909399;
  }

  .result-display {
    .score {
      font-weight: 600;
      color: #409eff;
      font-size: 16px;
    }
  }

  .text-muted {
    color: #909399;
  }

  .pagination-section {
    display: flex;
    justify-content: center;
  }
}
</style>