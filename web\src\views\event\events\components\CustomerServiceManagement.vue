<template>
  <el-dialog :title="customerServiceDialog.title" v-model="customerServiceDialog.visible" width="900px" append-to-body
    class="customer-service-dialog">

    <!-- 当前绑定的客服列表 -->
    <div class="current-services-section" v-if="currentEventCustomerServices.length > 0">
      <div class="section-header">
        <h4 class="section-title">
          <el-icon>
            <User />
          </el-icon>
          <span>当前绑定客服</span>
          <el-badge :value="currentEventCustomerServices.length" type="primary" />
        </h4>
        <div class="primary-info">
          主要负责人：<span
            class="primary-name">{{ currentEventCustomerServices.find(s => s.isPrimary)?.customerServiceName || '未设置' }}</span>
        </div>
      </div>

      <div class="services-list">
        <div v-for="service in currentEventCustomerServices" :key="service.id" class="service-item"
          :class="{ 'is-primary': service.isPrimary }">

          <!-- 左侧：基本信息 -->
          <div class="service-info">
            <div class="info-main">
              <div class="name-line">
                <el-tag v-if="service.isPrimary" type="danger" size="small">主要负责人</el-tag>
                <span class="service-name">{{ service.customerServiceName }}</span>
                <el-tag :type="service.status === 1 ? 'success' : 'info'" size="small">
                  {{ service.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </div>
              <div class="contact-line">
                <span class="phone">{{ service.phone || '暂无电话' }}</span>
                <span class="email">{{ service.email || '暂无邮箱' }}</span>
              </div>
              <div class="time-line">
                工作时间：{{ service.workingHours || '暂未设置' }}
              </div>
            </div>
          </div>

          <!-- 右侧：操作 -->
          <div class="service-actions">
            <el-button-group size="small">
              <el-tooltip content="设为主要负责人" v-if="!service.isPrimary">
                <el-button link type="warning" icon="Star" @click="handleSetPrimary(service.id)"></el-button>
              </el-tooltip>
              <el-tooltip content="解除绑定">
                <el-button link type="danger" icon="Delete" @click="handleRemoveService(service.id)"></el-button>
              </el-tooltip>
            </el-button-group>
          </div>
        </div>
      </div>
    </div>

    <el-divider v-if="currentEventCustomerServices.length > 0" />

    <!-- 可用的客服列表 -->
    <div class="available-services-section">
      <div class="section-header">
        <h4 class="section-title">
          <el-icon>
            <UserFilled />
          </el-icon>
          添加客服
        </h4>
      </div>

      <!-- 搜索框 -->
      <div class="search-section">
        <el-input v-model="serviceSearchKeyword" placeholder="搜索客服姓名或电话" prefix-icon="Search" clearable
          @input="filterAvailableServices" />
      </div>

      <div class="available-services-list">
        <div class="services-selection">
          <el-checkbox-group v-model="selectedCustomerServiceIds">
            <div v-for="service in filteredAvailableServices" :key="service.id" class="available-service-item">
              <el-checkbox :value="service.id" class="service-checkbox">
                <div class="service-content">
                  <div class="service-basic">
                    <span class="service-name">{{ service.customerServiceName }}</span>
                    <el-tag :type="service.status === 1 ? 'success' : 'info'" size="small">
                      {{ service.status === 1 ? '启用' : '禁用' }}
                    </el-tag>
                  </div>
                  <div class="service-contact">
                    <span class="phone">{{ service.phone || '暂无电话' }}</span>
                    <span class="email">{{ service.email || '暂无邮箱' }}</span>
                  </div>
                  <div class="service-hours">
                    工作时间：{{ service.workingHours || '暂未设置' }}
                  </div>
                </div>
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </div>

      <!-- 批量添加按钮 -->
      <div class="batch-actions" v-if="selectedCustomerServiceIds.length > 0">
        <el-button type="primary" :loading="addingServices" @click="handleAddEventServices">
          <el-icon>
            <Plus />
          </el-icon>
          添加选中的客服 ({{ selectedCustomerServiceIds.length }})
        </el-button>
        <el-button @click="selectedCustomerServiceIds = []">取消选择</el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="customerServiceDialog.visible = false">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="CustomerServiceManagement" lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  getAllCustomerService,
  getEventCustomerServices,
  batchAddEventCustomerService,
  removeEventCustomerService,
  setPrimaryService
} from '@/api/system/customerService';
import { CustomerServiceVO } from '@/api/system/customerService/types';

const emits = defineEmits<{
  (event: 'success'): void
}>()

// 客服管理对话框
const customerServiceDialog = reactive({
  visible: false,
  title: '',
  eventId: null as string | number | null,
  eventTitle: ''
});

// 客服相关数据
const allCustomerServices = ref<CustomerServiceVO[]>([]);
const currentEventCustomerServices = ref<CustomerServiceVO[]>([]);
const selectedCustomerServiceIds = ref<string[]>([]);
const addingServices = ref(false);

// 搜索和筛选
const serviceSearchKeyword = ref('');
const filteredAvailableServices = computed(() => {
  const available = allCustomerServices.value.filter(service => 
    !currentEventCustomerServices.value.some(bound => bound.id === service.id)
  );
  
  if (!serviceSearchKeyword.value.trim()) {
    return available;
  }
  
  const keyword = serviceSearchKeyword.value.toLowerCase();
  return available.filter(service => 
    service.customerServiceName?.toLowerCase().includes(keyword) ||
    service.phone?.includes(keyword) ||
    service.email?.toLowerCase().includes(keyword)
  );
});

/** 过滤可用客服 */
const filterAvailableServices = () => {
  // 这个方法会通过computed属性自动响应
};

/** 加载客服管理数据 */
const loadCustomerServiceData = async () => {
  try {
    // 同时加载所有客服和当前事项绑定的客服
    const [allServicesRes, eventServicesRes] = await Promise.all([
      getAllCustomerService(),
      getEventCustomerServices(customerServiceDialog.eventId!)
    ]);

    allCustomerServices.value = allServicesRes.data;
    currentEventCustomerServices.value = eventServicesRes.data;
  } catch (error) {
    console.error('加载客服数据失败:', error);
    ElMessage.error('加载客服数据失败');
  }
}

/** 添加选中的客服 */
const handleAddEventServices = async () => {
  if (selectedCustomerServiceIds.value.length === 0) {
    ElMessage.warning('请先选择要添加的客服');
    return;
  }

  try {
    addingServices.value = true;
    await batchAddEventCustomerService(customerServiceDialog.eventId!, selectedCustomerServiceIds.value);
    ElMessage.success(`成功添加${selectedCustomerServiceIds.value.length}个客服`);

    // 清空选择并刷新数据
    selectedCustomerServiceIds.value = [];
    await loadCustomerServiceData();
    emits('success');
  } catch (error) {
    console.error('添加客服失败:', error);
    ElMessage.error('添加客服失败');
  } finally {
    addingServices.value = false;
  }
}

/** 移除客服绑定 */
const handleRemoveService = async (serviceId: string) => {
  try {
    await ElMessageBox.confirm('确认要解除该客服的绑定吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await removeEventCustomerService(customerServiceDialog.eventId!, serviceId);
    ElMessage.success('移除成功');
    await loadCustomerServiceData();
    emits('success');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除客服失败:', error);
      ElMessage.error('移除客服失败');
    }
  }
}

/** 设置主要负责人 */
const handleSetPrimary = async (serviceId: string) => {
  try {
    await ElMessageBox.confirm('确认要设置该客服为主要负责人吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    await setPrimaryService(customerServiceDialog.eventId!, serviceId);
    ElMessage.success('设置成功');
    await loadCustomerServiceData();
    emits('success');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('设置主要负责人失败:', error);
      ElMessage.error('设置失败');
    }
  }
}

const open = async (option: { eventId: string | number, eventTitle: string }) => {
  customerServiceDialog.eventId = option.eventId;
  customerServiceDialog.eventTitle = option.eventTitle;
  customerServiceDialog.title = `"${option.eventTitle}" 的客服管理`;
  customerServiceDialog.visible = true;

  // 清空选择
  selectedCustomerServiceIds.value = [];
  serviceSearchKeyword.value = '';

  // 加载数据
  await loadCustomerServiceData();
}

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
@import '../styles/customer-service-management.scss';
</style>