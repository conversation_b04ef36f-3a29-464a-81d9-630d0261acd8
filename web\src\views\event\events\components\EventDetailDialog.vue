<template>
  <el-dialog title="详情" v-model="visible" width="900px" append-to-body class="event-detail-dialog">
    <div class="detail-content">
      <!-- 基础信息 -->
      <div class="detail-section">
        <h4 class="section-title">
          <el-icon>
            <InfoFilled />
          </el-icon>
          基础信息
        </h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="标题" :span="2">
            <span class="event-title">{{ eventDetail.title }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="主办方">
            {{ eventDetail.organizer || '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系方式">
            {{ eventDetail.contactInfo || '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="举办地点" :span="2">
            {{ eventDetail.location || '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            <div v-if="eventDetail.description" class="description-text rich-text"
              v-html="eventDetail.description"></div>
            <span v-else>暂无</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 时间信息 -->
      <div class="detail-section">
        <h4 class="section-title">
          <el-icon>
            <Clock />
          </el-icon>
          时间安排
        </h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="开始时间">
            <el-tag type="success" v-if="eventDetail.startTime">
              {{ parseTime(eventDetail.startTime, '{y}-{m}-{d} {h}:{i}') }}
            </el-tag>
            <span v-else>暂无</span>
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">
            <el-tag type="info" v-if="eventDetail.endTime">
              {{ parseTime(eventDetail.endTime, '{y}-{m}-{d} {h}:{i}') }}
            </el-tag>
            <span v-else>暂无</span>
          </el-descriptions-item>
          <el-descriptions-item label="报名截止时间" :span="2">
            <el-tag :type="isDeadlineExpired(eventDetail.registrationDeadline) ? 'danger' : 'warning'"
              v-if="eventDetail.registrationDeadline">
              {{ parseTime(eventDetail.registrationDeadline, '{y}-{m}-{d} {h}:{i}') }}
              <span v-if="isDeadlineExpired(eventDetail.registrationDeadline)"> (已过期)</span>
            </el-tag>
            <span v-else>暂无</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 报名信息 -->
      <div class="detail-section">
        <h4 class="section-title">
          <el-icon>
            <User />
          </el-icon>
          报名信息
        </h4>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="报名费用">
            <el-tag type="danger" size="large">
              ￥{{ eventDetail.fee || 0 }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="最大人数">
            {{ eventDetail.maxParticipants || '不限' }}
          </el-descriptions-item>
          <el-descriptions-item label="当前报名">
            <el-tag
              :type="getRegistrationProgressType(eventDetail.currentParticipants, eventDetail.maxParticipants)">
              {{ eventDetail.currentParticipants || 0 }}人
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态" :span="3">
            <el-tag :type="getStatusType(eventDetail.status)" size="large" effect="dark">
              {{ getStatusText(eventDetail.status) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 详细设置 -->
      <div class="detail-section"
        v-if="eventDetail.rules || eventDetail.rewards || eventDetail.remark">
        <h4 class="section-title">
          <el-icon>
            <Document />
          </el-icon>
          详细设置
        </h4>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="规则" v-if="eventDetail.rules">
            <div class="rule-content rich-text" v-html="eventDetail.rules"></div>
          </el-descriptions-item>
          <el-descriptions-item label="奖励设置" v-if="eventDetail.rewards">
            <div class="reward-content rich-text" v-html="eventDetail.rewards"></div>
          </el-descriptions-item>
          <el-descriptions-item label="备注信息" v-if="eventDetail.remark">
            <div class="remark-content rich-text" v-html="eventDetail.remark"></div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 海报预览 -->
      <div class="detail-section" v-if="eventDetail.posterUrl">
        <h4 class="section-title">
          <el-icon>
            <Picture />
          </el-icon>
          海报预览
        </h4>
        <div class="poster-preview">
          <el-image :src="eventDetail.posterUrl" :preview-src-list="[eventDetail.posterUrl]"
            fit="contain" style="max-width: 100px; max-height: 100px; border-radius: 8px;" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关 闭</el-button>
        <el-button type="primary" @click="handleRegistrations">
          <el-icon>
            <User />
          </el-icon>
          报名管理
        </el-button>
        <el-button type="success" @click="handleGenerateQR">
          <el-icon>
            <Grid />
          </el-icon>
          生成二维码
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { parseTime } from '@/utils/ruoyi';
import type { EventsVO } from '@/api/event/events/types';
import { InfoFilled, Clock, User, Document, Picture, Grid } from '@element-plus/icons-vue';

interface Emits {
  (e: 'registrations', eventDetail: EventsVO): void;
  (e: 'generate-qr', eventDetail: EventsVO): void;
}

const emit = defineEmits<Emits>();

const visible = ref(false);
const eventDetail = ref<EventsVO>({} as EventsVO);

/** 打开对话框 */
const open = (detail: EventsVO) => {
  eventDetail.value = detail;
  visible.value = true;
};

/** 报名管理 */
const handleRegistrations = () => {
  emit('registrations', eventDetail.value);
};

/** 生成二维码 */
const handleGenerateQR = () => {
  emit('generate-qr', eventDetail.value);
};

// 工具函数
const isDeadlineExpired = (deadline: string) => {
  if (!deadline) return false;
  return new Date(deadline) < new Date();
};

const getRegistrationProgressType = (current: number, max: number) => {
  if (!max) return 'info';
  const progress = (current / max) * 100;
  if (progress >= 90) return 'danger';
  if (progress >= 70) return 'warning';
  return 'success';
};

const getStatusType = (status: number) => {
  const statusMap = {
    0: 'info',
    1: 'success',
    2: 'warning',
    3: 'danger',
    4: 'info'
  };
  return statusMap[status] || 'info';
};

const getStatusText = (status: number) => {
  const statusMap = {
    0: '未开始',
    1: '报名中',
    2: '进行中',
    3: '已结束',
    4: '已取消'
  };
  return statusMap[status] || '未知';
};

defineExpose({
  open
});
</script>

<style scoped>
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.event-title {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.rich-text {
  line-height: 1.6;
}

.poster-preview {
  text-align: center;
  padding: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
