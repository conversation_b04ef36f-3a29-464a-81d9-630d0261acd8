<template>
  <el-dialog :title="dialog.title" v-model="dialog.visible" width="1000px" append-to-body class="event-dialog">
    <el-form ref="eventsFormRef" :model="form" :rules="rules" label-width="120px" class="event-form">
      <!-- 基础必填信息 -->
      <div class="form-section">
        <h4 class="section-title">基础信息 <span class="required-mark">*必填</span></h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="标题" prop="title" required>
              <el-input v-model="form.title" placeholder="请输入标题" maxlength="100" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主办方" prop="organizer" required>
              <el-input v-model="form.organizer" placeholder="请输入主办方" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系方式" prop="contactInfo" required>
              <el-input v-model="form.contactInfo" placeholder="请输入联系方式" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大参与人数" prop="maxParticipants" required>
              <el-input-number v-model="form.maxParticipants" :min="1" :max="10000" placeholder="请输入最大参与人数"
                style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 描述 - 单独一行 -->
        <el-form-item label="描述" prop="description" required>
          <editor v-model="form.description" :min-height="220" />
        </el-form-item>
      </div>

      <!-- 海报上传 -->
      <div class="form-section">
        <h4 class="section-title">海报图片 <span class="required-mark">*必填</span></h4>
        <el-form-item label="海报图片" prop="poster" required>
          <ImageUpload v-model="form.poster" />
        </el-form-item>
      </div>

      <!-- 时间地点信息 -->
      <div class="form-section">
        <h4 class="section-title">时间地点</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker v-model="form.startTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择开始时间" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker v-model="form.endTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择结束时间" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报名截止时间" prop="registrationDeadline">
              <el-date-picker v-model="form.registrationDeadline" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择报名截止时间" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="举办地点" prop="location">
              <el-input v-model="form.location" placeholder="请输入举办地点" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 费用状态信息 -->
      <div class="form-section">
        <h4 class="section-title">费用状态</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报名费用" prop="fee">
              <el-input-number v-model="form.fee" :min="0" :precision="2" placeholder="请输入报名费用" style="width: 100%">
                <template #append>元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                <el-option v-for="dict in event_status" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 详细设置 -->
      <div class="form-section">
        <h4 class="section-title">详细设置</h4>

        <!-- 规则 - 单独一行 -->
        <el-form-item label="规则" prop="rules">
          <editor v-model="form.rules" :min-height="240" />
        </el-form-item>

        <!-- 奖励设置 - 单独一行 -->
        <el-form-item label="奖励设置" prop="rewards">
          <editor v-model="form.rewards" :min-height="200" />
        </el-form-item>

        <!-- 备注 - 单独一行 -->
        <el-form-item label="备注信息" prop="remark">
          <editor v-model="form.remark" :min-height="160" />
        </el-form-item>
      </div>

    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button :loading="buttonLoading" type="primary" @click="handleSubmit">
          <el-icon>
            <Check />
          </el-icon>
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="EventForm" lang="ts">
import { ElMessage } from 'element-plus';
import { addEvents, updateEvents } from '@/api/event/events';
import { EventsForm } from '@/api/event/events/types';
import { createFormConfig } from '@/api/event/form';
import { getDefaultFormConfig } from '@/components/FormBuilder/config/defaultTemplates';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { event_status } = toRefs<any>(proxy?.useDict('event_status'));

const emits = defineEmits<{
  (event: 'success'): void
  (event: 'close'): void
}>()

const eventsFormRef = ref<ElFormInstance>();
const buttonLoading = ref(false);

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: EventsForm = {
  id: undefined,
  title: undefined,
  description: undefined,
  startTime: undefined,
  endTime: undefined,
  location: undefined,
  poster: undefined,
  rules: undefined,
  rewards: undefined,
  registrationDeadline: undefined,
  fee: undefined,
  status: undefined,
  organizer: undefined,
  contactInfo: undefined,
  maxParticipants: undefined,
  currentParticipants: undefined,
  remark: undefined,
}

const form = ref<EventsForm>({ ...initFormData });

const rules = reactive({
  title: [
    { required: true, message: "标题不能为空", trigger: "blur" }
  ],
  description: [
    { required: true, message: "描述不能为空", trigger: "blur" }
  ],
  poster: [
    { required: true, message: "请上传海报图片", trigger: "blur" }
  ],
  organizer: [
    { required: true, message: "主办方不能为空", trigger: "blur" }
  ],
  contactInfo: [
    { required: true, message: "联系方式不能为空", trigger: "blur" }
  ],
  maxParticipants: [
    { required: true, message: "最大参与人数限制不能为空", trigger: "blur" }
  ]
});

const open = (option: { title: string, data?: EventsForm }) => {
  dialog.title = option.title;
  dialog.visible = true;
  
  if (option.data) {
    form.value = { ...option.data };
  } else {
    form.value = { ...initFormData };
  }
  
  nextTick(() => {
    eventsFormRef.value?.resetFields();
  });
}

const handleCancel = () => {
  form.value = { ...initFormData };
  eventsFormRef.value?.resetFields();
  dialog.visible = false;
  emits('close');
}

const handleSubmit = () => {
  eventsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      try {
        if (form.value.id) {
          await updateEvents(form.value);
          ElMessage.success("修改成功");
        } else {
          const result = await addEvents(form.value);
          const eventId = result.data?.id || result.data;

          // 自动创建默认表单配置
          if (eventId) {
            try {
              const defaultTemplate = getDefaultFormConfig();
              await createFormConfig({
                eventId: eventId,
                formName: `${form.value.title} - 报名表单`,
                formDescription: '请填写以下信息完成报名',
                formConfig: defaultTemplate.formConfig,
                fields: defaultTemplate.fields
              });
              console.log('默认表单配置创建成功');
            } catch (formError) {
              console.warn('创建默认表单配置失败:', formError);
            }
          }
          ElMessage.success("添加成功");
        }
        
        dialog.visible = false;
        emits('success');
      } catch (error) {
        console.error('操作失败:', error);
        ElMessage.error("操作失败");
      } finally {
        buttonLoading.value = false;
      }
    }
  });
}

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
@import '../styles/event-form.scss';
</style>