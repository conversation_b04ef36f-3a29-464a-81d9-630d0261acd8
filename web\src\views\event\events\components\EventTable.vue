<template>
  <el-table v-loading="loading" border :data="eventsList" @selection-change="handleSelectionChange">
    <el-table-column type="selection" width="55" align="center" />
    <el-table-column label="事项信息" align="left" min-width="200">
      <template #default="scope">
        <div class="event-info">
          <div class="event-title">{{ scope.row.title }}</div>
          <div class="event-desc">{{ scope.row.description }}</div>
          <div class="event-meta">
            <el-tag size="small" type="info">{{ scope.row.organizer }}</el-tag>
            <span class="fee">报名费：￥{{ scope.row.fee }}</span>
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="时间安排" align="center" width="180">
      <template #default="scope">
        <div class="time-info">
          <div><strong>活动开始:</strong> {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}</div>
          <div><strong>活动结束:</strong> {{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</div>
          <div class="deadline" :class="{ 'expired': isDeadlineExpired(scope.row.registrationDeadline) }">
            <strong>报名截止:</strong> {{ parseTime(scope.row.registrationDeadline, '{y}-{m}-{d}') }}
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="地点" align="center" prop="location" min-width="150" />
    <el-table-column label="报名情况" align="center" width="150">
      <template #default="scope">
        <div class="registration-progress">
          <div class="numbers">
            {{ scope.row.currentParticipants || 0 }} / {{ scope.row.maxParticipants || '不限' }}
          </div>
          <el-progress
            :percentage="getRegistrationProgress(scope.row.currentParticipants, scope.row.maxParticipants)"
            :color="getProgressColor(scope.row.currentParticipants, scope.row.maxParticipants)" :show-text="false"
            size="small" />
        </div>
      </template>
    </el-table-column>
    <el-table-column label="状态" align="center" width="100">
      <template #default="scope">
        <el-tag :type="getStatusType(scope.row.status)" size="small" effect="dark">
          {{ getStatusText(scope.row.status) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="海报" align="center" width="140">
      <template #default="scope">
        <el-image v-if="scope.row.posterUrl" :src="scope.row.posterUrl" :preview-src-list="[scope.row.posterUrl]"
          fit="cover" style="width: 100px; height: 100px; border-radius: 4px;" />
        <span v-else class="no-poster">暂无</span>
      </template>
    </el-table-column>
    <el-table-column label="操作" align="center" width="200" fixed="right">
      <template #default="scope">
        <el-button-group size="small">
          <el-tooltip content="查看详情" placement="top">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="修改" placement="top">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['event:events:edit']"></el-button>
          </el-tooltip>
          <el-tooltip content="报名管理" placement="top">
            <el-button link type="success" icon="User" @click="handleRegistrations(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="编辑表单" placement="top">
            <el-button link type="primary" icon="Setting" @click="handleEditForm(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="生成二维码" placement="top">
            <el-button link type="warning" icon="Grid" @click="handleGenerateQR(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="查看项目" placement="top">
            <el-button link type="info" icon="List" @click="handleViewProjects(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="客服管理" placement="top">
            <el-button link type="success" icon="Service" @click="handleCustomerService(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="奖状证书管理" placement="top">
            <el-button link type="warning" icon="Medal" @click="handleCertificateManage(scope.row)"></el-button>
          </el-tooltip>
          <el-dropdown @command="(command) => handleMoreActions(command, scope.row)" trigger="click">
            <el-button link type="info" icon="MoreFilled"></el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="statistics" icon="DataAnalysis">统计信息</el-dropdown-item>
                <el-dropdown-item command="addProject" icon="Upload">添加项目</el-dropdown-item>
                <el-dropdown-item command="publish" v-if="scope.row.status === 0"
                  icon="Upload">发布</el-dropdown-item>
                <el-dropdown-item command="unpublish" v-else-if="scope.row.status === 1"
                  icon="Download">取消发布</el-dropdown-item>
                <el-dropdown-item command="status-0" v-if="scope.row.status !== 0"
                  icon="CircleClose">设为未开始</el-dropdown-item>
                <el-dropdown-item command="status-1" v-if="scope.row.status !== 1"
                  icon="VideoPlay">开始报名</el-dropdown-item>
                <el-dropdown-item command="status-2" v-if="scope.row.status !== 2"
                  icon="Timer">进行中</el-dropdown-item>
                <el-dropdown-item command="status-3" v-if="scope.row.status !== 3"
                  icon="CircleCheck">已结束</el-dropdown-item>
                <el-dropdown-item command="status-4" v-if="scope.row.status !== 4"
                  icon="CircleClose">取消</el-dropdown-item>
                <el-dropdown-item command="copy" icon="CopyDocument" divided>复制</el-dropdown-item>
                <el-dropdown-item command="export" icon="Download">导出报名表</el-dropdown-item>
                <el-dropdown-item command="delete" icon="Delete" divided>删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-button-group>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { parseTime } from '@/utils/ruoyi';
import type { EventsVO } from '@/api/event/events/types';

interface Props {
  loading: boolean;
  eventsList: EventsVO[];
}

interface Emits {
  (e: 'selection-change', selection: EventsVO[]): void;
  (e: 'view', row: EventsVO): void;
  (e: 'update', row: EventsVO): void;
  (e: 'registrations', row: EventsVO): void;
  (e: 'edit-form', row: EventsVO): void;
  (e: 'generate-qr', row: EventsVO): void;
  (e: 'view-projects', row: EventsVO): void;
  (e: 'customer-service', row: EventsVO): void;
  (e: 'certificate-manage', row: EventsVO): void;
  (e: 'more-actions', command: string, row: EventsVO): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

/** 多选框选中数据 */
const handleSelectionChange = (selection: EventsVO[]) => {
  emit('selection-change', selection);
};

/** 查看详情 */
const handleView = (row: EventsVO) => {
  emit('view', row);
};

/** 修改按钮操作 */
const handleUpdate = (row: EventsVO) => {
  emit('update', row);
};

/** 报名管理 */
const handleRegistrations = (row: EventsVO) => {
  emit('registrations', row);
};

/** 编辑表单 */
const handleEditForm = (row: EventsVO) => {
  emit('edit-form', row);
};

/** 生成二维码 */
const handleGenerateQR = (row: EventsVO) => {
  emit('generate-qr', row);
};

/** 查看项目 */
const handleViewProjects = (row: EventsVO) => {
  emit('view-projects', row);
};

/** 客服管理 */
const handleCustomerService = (row: EventsVO) => {
  emit('customer-service', row);
};

/** 奖状证书管理 */
const handleCertificateManage = (row: EventsVO) => {
  emit('certificate-manage', row);
};

/** 更多操作 */
const handleMoreActions = (command: string, row: EventsVO) => {
  emit('more-actions', command, row);
};

// 工具函数
const isDeadlineExpired = (deadline: string) => {
  if (!deadline) return false;
  return new Date(deadline) < new Date();
};

const getRegistrationProgress = (current: number, max: number) => {
  if (!max || max === 0) return 0;
  return Math.round((current / max) * 100);
};

const getProgressColor = (current: number, max: number) => {
  const progress = getRegistrationProgress(current, max);
  if (progress >= 90) return '#f56c6c';
  if (progress >= 70) return '#e6a23c';
  return '#67c23a';
};

const getStatusType = (status: number) => {
  const statusMap = {
    0: 'info',
    1: 'success',
    2: 'warning',
    3: 'danger',
    4: 'info'
  };
  return statusMap[status] || 'info';
};

const getStatusText = (status: number) => {
  const statusMap = {
    0: '未开始',
    1: '报名中',
    2: '进行中',
    3: '已结束',
    4: '已取消'
  };
  return statusMap[status] || '未知';
};
</script>

<style scoped>
.event-info {
  text-align: left;
}

.event-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.event-desc {
  color: #666;
  font-size: 12px;
  margin-bottom: 5px;
}

.event-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.fee {
  color: #e6a23c;
  font-weight: bold;
  font-size: 12px;
}

.time-info {
  font-size: 12px;
  line-height: 1.5;
}

.deadline.expired {
  color: #f56c6c;
}

.registration-progress {
  text-align: center;
}

.numbers {
  margin-bottom: 5px;
  font-size: 12px;
}

.no-poster {
  color: #999;
  font-size: 12px;
}
</style>
