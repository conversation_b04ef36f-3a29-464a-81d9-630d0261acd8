<template>
  <el-row :gutter="10" class="mb8">
    <el-col :span="1.5">
      <el-button type="primary" plain icon="Plus" @click="handleAdd"
        v-hasPermi="['event:events:add']">新增</el-button>
    </el-col>
    <el-col :span="1.5">
      <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
        v-hasPermi="['event:events:edit']">修改</el-button>
    </el-col>
    <el-col :span="1.5">
      <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
        v-hasPermi="['event:events:remove']">删除</el-button>
    </el-col>
    <el-col :span="1.5">
      <el-button type="warning" plain icon="Download" @click="handleExport"
        v-hasPermi="['event:events:export']">导出数据</el-button>
    </el-col>
    <right-toolbar v-model:showSearch="showSearch" @queryTable="handleRefresh"></right-toolbar>
  </el-row>
</template>

<script setup lang="ts">
interface Props {
  single: boolean;
  multiple: boolean;
  showSearch: boolean;
}

interface Emits {
  (e: 'add'): void;
  (e: 'update'): void;
  (e: 'delete'): void;
  (e: 'export'): void;
  (e: 'refresh'): void;
  (e: 'update:showSearch', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

/** 新增按钮操作 */
const handleAdd = () => {
  emit('add');
};

/** 修改按钮操作 */
const handleUpdate = () => {
  emit('update');
};

/** 删除按钮操作 */
const handleDelete = () => {
  emit('delete');
};

/** 导出按钮操作 */
const handleExport = () => {
  emit('export');
};

/** 刷新操作 */
const handleRefresh = () => {
  emit('refresh');
};
</script>
