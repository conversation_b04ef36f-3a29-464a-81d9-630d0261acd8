<template>
  <el-dialog :title="paymentManageDialog.title" v-model="paymentManageDialog.visible" width="650px" append-to-body
    class="payment-manage-dialog">
    <div class="payment-manage-content">
      <div class="current-status">
        <p>当前支付状态：
          <el-tag :type="getPaymentStatusType(paymentManageDialog.currentStatus)" size="large">
            {{ getPaymentStatusText(paymentManageDialog.currentStatus) }}
          </el-tag>
        </p>
      </div>

      <!-- 显示历史支付凭证和备注 -->
      <div v-if="paymentManageDialog.currentRecord.remark" class="history-payment-info">
        <h4>
          <el-icon>
            <Clock />
          </el-icon>
          历史操作记录
        </h4>

        <!-- 历史支付凭证 -->
        <div v-if="paymentManageDialog.currentRecord.paymentProofList" class="history-proof-section">
          <h5 class="proof-title">
            <el-icon>
              <Picture />
            </el-icon>
            已上传的支付凭证
          </h5>
          <div class="proof-images">
            <div v-for="image in paymentManageDialog.currentRecord.paymentProofList" :key="image.id"
              class="proof-image-item">
              <el-image :src="image.url"
                :preview-src-list="paymentManageDialog.currentRecord.paymentProofList.map(img => img.url)" fit="cover"
                class="proof-image" />
              <div class="image-name">{{ image.name }}</div>
            </div>
          </div>
        </div>

        <!-- 历史备注 -->
        <div v-if="parsePaymentInfo(paymentManageDialog.currentRecord.remark).pureRemark"
          class="history-remark-section">
          <h5 class="remark-title">
            <el-icon>
              <ChatDotRound />
            </el-icon>
            历史操作备注
          </h5>
          <div class="remark-content">
            {{ parsePaymentInfo(paymentManageDialog.currentRecord.remark).pureRemark }}
          </div>
        </div>
      </div>

      <el-divider />

      <!-- 确认支付区域 -->
      <div v-if="paymentManageDialog.currentStatus !== 1" class="payment-confirm-section">
        <h4>确认支付</h4>
        <div class="payment-form">
          <div class="form-item">
            <label>支付凭证截图：</label>
            <div class="upload-area">
              <ImageUpload v-model="paymentManageDialog.paymentProof" :limit="3" :file-size="5"
                :file-type="['jpg', 'jpeg', 'png']" :is-show-tip="true" />
            </div>
            <div class="upload-tip">
              <el-icon>
                <InfoFilled />
              </el-icon>
              <span>请上传支付凭证截图，支持jpg/png格式，最多3张，每张不超过5MB</span>
            </div>
          </div>

          <div class="form-item">
            <label>操作备注：</label>
            <el-input v-model="paymentManageDialog.remark" type="textarea" :rows="3"
              placeholder="请输入操作备注（可选），如转账方式、核实情况等" maxlength="200" show-word-limit />
          </div>

          <div class="confirm-button">
            <el-button type="success" size="large" @click="handleConfirmPayment">
              <el-icon>
                <Check />
              </el-icon>
              确认已支付
            </el-button>
          </div>
        </div>
      </div>

      <!-- 其他操作区域 -->
      <div class="other-actions">
        <h4>其他操作</h4>
        <div class="button-group">
          <el-button v-if="paymentManageDialog.currentStatus !== 0" type="warning"
            @click="handleUpdatePaymentStatus(paymentManageDialog.registrationId, 0, '管理员取消支付确认')">
            <el-icon>
              <Close />
            </el-icon>
            标记为未支付
          </el-button>

          <el-button v-if="paymentManageDialog.currentStatus === 1" type="info"
            @click="handleUpdatePaymentStatus(paymentManageDialog.registrationId, 3, '管理员执行退款操作')">
            <el-icon>
              <RefreshLeft />
            </el-icon>
            退款
          </el-button>

          <el-button v-if="paymentManageDialog.currentStatus !== 2" type="danger"
            @click="handleUpdatePaymentStatus(paymentManageDialog.registrationId, 2, '管理员标记支付失败')">
            <el-icon>
              <CloseBold />
            </el-icon>
            标记支付失败
          </el-button>
        </div>
      </div>

      <el-alert title="操作提醒" type="warning" :closable="false" style="margin-top: 20px">
        <template #default>
          <p>请谨慎操作支付状态，上传的支付凭证将保存在系统中作为核实依据</p>
        </template>
      </el-alert>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="paymentManageDialog.visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="PaymentManagement" lang="ts">
import { ElMessage } from 'element-plus';
import { updatePaymentStatus } from '@/api/event/registrations';
import { RegistrationsVO } from '@/api/event/registrations/types';
import { listByIds } from "@/api/system/oss";

const emits = defineEmits<{
  (event: 'success'): void
}>()

// 支付管理对话框
const paymentManageDialog = reactive({
  visible: false,
  title: '',
  registrationId: null as string | number | null,
  currentStatus: 0,
  paymentProof: '', // 支付凭证图片
  remark: '', // 操作备注
  currentRecord: {} as RegistrationsVO // 当前记录的完整信息
});

/** 更新支付状态 */
const handleUpdatePaymentStatus = async (registrationId: string | number, status: number, remark?: string, paymentProof?: string) => {
  try {
    // 构建完整的备注信息，包含支付凭证
    let fullRemark = remark || '';

    await updatePaymentStatus(registrationId, status, paymentProof, fullRemark);
    ElMessage.success('支付状态更新成功');

    // 重置对话框状态
    paymentManageDialog.visible = false;
    paymentManageDialog.paymentProof = '';
    paymentManageDialog.remark = '';

    emits('success');
  } catch (error) {
    console.error('更新支付状态失败:', error);
    ElMessage.error('更新支付状态失败');
  }
}

/** 确认支付（带支付凭证） */
const handleConfirmPayment = async () => {
  if (!paymentManageDialog.paymentProof && !paymentManageDialog.remark) {
    ElMessage.warning('请上传支付凭证或填写备注信息');
    return;
  }

  const remark = paymentManageDialog.remark || '管理员确认支付';
  await handleUpdatePaymentStatus(
    paymentManageDialog.registrationId,
    1,
    remark,
    paymentManageDialog.paymentProof
  );
}

/** 获取支付状态文本 */
const getPaymentStatusText = (status: number): string => {
  const statusMap = {
    0: '未支付',
    1: '已支付',
    2: '支付失败',
    3: '已退款'
  };
  return statusMap[status] || '未知';
}

/** 获取支付状态类型 */
const getPaymentStatusType = (status: number): string => {
  const typeMap = {
    0: 'warning',
    1: 'success',
    2: 'danger',
    3: 'info'
  };
  return typeMap[status] || 'info';
}

/** 解析支付凭证和备注 */
const parsePaymentInfo = (remark: string) => {
  if (!remark) return { paymentProof: '', pureRemark: '' };

  // 查找支付凭证信息
  const proofMatch = remark.match(/支付凭证:\s*([^|]+)/);
  const paymentProof = proofMatch ? proofMatch[1].trim() : '';

  // 移除支付凭证信息，获取纯备注
  const pureRemark = remark.replace(/\|\s*支付凭证:\s*[^|]*/, '').replace(/支付凭证:\s*[^|]*\s*\|?/, '').trim();

  return { paymentProof, pureRemark };
}

const open = (row: RegistrationsVO) => {
  paymentManageDialog.registrationId = row.id;
  paymentManageDialog.currentStatus = row.paymentStatus;
  paymentManageDialog.title = `支付管理 - ${row.registrationData ? JSON.parse(row.registrationData).name || '未知' : '未知'}`;
  paymentManageDialog.paymentProof = ''; // 重置支付凭证
  paymentManageDialog.remark = ''; // 重置备注

  // 保存当前记录的完整信息，用于显示历史支付凭证
  paymentManageDialog.currentRecord = row;
  if (paymentManageDialog.currentRecord.paymentProof) {
    listByIds(paymentManageDialog.currentRecord.paymentProof).then(res => {
      paymentManageDialog.currentRecord.paymentProofList = res.data
    })
  }

  paymentManageDialog.visible = true;
}

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
@import '../styles/payment-management.scss';
</style>