<template>
  <el-dialog :title="projectDialog.title" v-model="projectDialog.visible" width="1000px" append-to-body
    class="project-dialog">
    <el-form ref="projectFormRef" :model="projectForm" :rules="projectRules" label-width="120px" class="project-form">
      <div class="form-section">
        <h4 class="section-title">基础信息 <span class="required-mark">*必填</span></h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="name" required>
              <el-input v-model="projectForm.name" placeholder="请输入项目名称" maxlength="50" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大参与人数" prop="maxParticipants" required>
              <el-input-number v-model="projectForm.maxParticipants" :min="1" :max="10000" placeholder="请输入最大参与人数"
                style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="项目描述" prop="description" required>
          <editor v-model="projectForm.description" :min-height="220" />
        </el-form-item>
      </div>

      <div class="form-section">
        <h4 class="section-title">参与限制</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="最小年龄">
              <el-input-number v-model="projectForm.ageLimitMin" :min="0" :max="100" placeholder="最小年龄"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大年龄">
              <el-input-number v-model="projectForm.ageLimitMax" :min="0" :max="100" placeholder="最大年龄"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别限制">
              <el-select v-model="projectForm.genderLimit" placeholder="请选择性别限制" style="width: 100%">
                <el-option label="不限制" :value="0"></el-option>
                <el-option label="仅男性" :value="1"></el-option>
                <el-option label="仅女性" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="form-section">
        <h4 class="section-title">其他设置</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="额外费用">
              <el-input-number v-model="projectForm.additionalFee" :min="0" :precision="2" placeholder="额外费用"
                style="width: 100%">
                <template #append>元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序">
              <el-input-number v-model="projectForm.sortOrder" :min="0" placeholder="数字越小越靠前" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="特殊要求">
          <editor v-model="projectForm.requirements" :min-height="180" />
        </el-form-item>

        <el-form-item label="备注">
          <editor v-model="projectForm.remark" :min-height="160" />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button :loading="projectLoading" type="primary" @click="handleSubmit">
          <el-icon>
            <Check />
          </el-icon>
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="ProjectForm" lang="ts">
import { ElMessage } from 'element-plus';
import { addEventItems, updateEventItems } from '@/api/event/eventItems';
import { EventItemsForm } from '@/api/event/eventItems/types';

const emits = defineEmits<{
  (event: 'success'): void
  (event: 'close'): void
}>()

const projectFormRef = ref<ElFormInstance>();
const projectLoading = ref(false);

// 项目对话框
const projectDialog = reactive({
  visible: false,
  title: '',
  eventId: null as string | number | null,
  eventTitle: ''
});

// 项目表单初始数据
const initProjectFormData: EventItemsForm = {
  id: undefined,
  eventId: undefined,
  name: undefined,
  description: undefined,
  maxParticipants: undefined,
  currentParticipants: 0,
  additionalFee: 0,
  ageLimitMin: undefined,
  ageLimitMax: undefined,
  genderLimit: 0, // 0:不限制 1:男性 2:女性
  requirements: undefined,
  sortOrder: 0,
  remark: undefined
};

const projectForm = ref<EventItemsForm>({ ...initProjectFormData });

// 项目表单验证规则
const projectRules = {
  name: [
    { required: true, message: "项目名称不能为空", trigger: "blur" }
  ],
  description: [
    { required: true, message: "项目描述不能为空", trigger: "blur" }
  ],
  maxParticipants: [
    { required: true, message: "最大参与人数不能为空", trigger: "blur" }
  ]
};

const open = (option: { title: string, eventId: string | number, data?: EventItemsForm }) => {
  projectDialog.title = option.title;
  projectDialog.eventId = option.eventId;
  projectDialog.visible = true;

  if (option.data) {
    projectForm.value = { ...option.data };
  } else {
    projectForm.value = { ...initProjectFormData };
    projectForm.value.eventId = option.eventId;
  }

  nextTick(() => {
    projectFormRef.value?.resetFields();
  });
}

const handleCancel = () => {
  projectForm.value = { ...initProjectFormData };
  projectFormRef.value?.resetFields();
  projectDialog.visible = false;
  emits('close');
}

const handleSubmit = () => {
  projectFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      try {
        projectLoading.value = true;
        if (projectForm.value.id) {
          await updateEventItems(projectForm.value);
          ElMessage.success("项目修改成功");
        } else {
          await addEventItems(projectForm.value);
          ElMessage.success("项目添加成功");
        }
        projectDialog.visible = false;
        emits('success');
      } catch (error) {
        console.error('保存项目失败:', error);
        ElMessage.error('保存项目失败');
      } finally {
        projectLoading.value = false;
      }
    }
  });
}

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
// 项目对话框样式
:deep(.project-dialog) {
  .el-dialog__body {
    padding: 20px 30px;
    max-height: 70vh;
    overflow-y: auto;
  }
}

.project-form {
  .form-section {
    margin-bottom: 25px;
    padding: 15px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #e1e6f0;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 15px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #409eff;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .required-mark {
        font-size: 12px;
        color: #f56c6c;
        font-weight: 400;
      }
    }

    .el-form-item {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.dialog-footer {
  .el-button {
    min-width: 100px;
    margin-left: 10px;

    &:first-child {
      margin-left: 0;
    }
  }

  .el-button--primary {
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    border: none;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);

    &:hover {
      background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%);
      box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
    }
  }
}
</style>