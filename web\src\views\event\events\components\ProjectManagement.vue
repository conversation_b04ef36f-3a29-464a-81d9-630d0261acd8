<template>
  <!-- 项目列表对话框 -->
  <el-dialog :title="projectListDialog.title" v-model="projectListDialog.visible" width="1200px" append-to-body
    class="project-list-dialog">
    <!-- 搜索区域 -->
    <div class="mb-4">
      <el-card shadow="hover">
        <el-form ref="projectQueryFormRef" :model="projectQueryParams" :inline="true">
          <el-form-item label="项目名称" prop="name">
            <el-input v-model="projectQueryParams.name" placeholder="请输入项目名称" clearable
              @keyup.enter="handleProjectQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleProjectQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetProjectQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 操作按钮 -->
    <div class="mb-4">
      <el-button type="primary" icon="Plus" @click="handleAddProject">
        添加项目
      </el-button>
    </div>

    <!-- 项目表格 -->
    <el-table v-loading="projectLoading" :data="projectsList" border style="width: 100%">
      <el-table-column label="项目信息" min-width="200">
        <template #default="scope">
          <div class="project-info">
            <div class="project-name">{{ scope.row.name }}</div>
            <div class="project-desc">{{ scope.row.description }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="参与人数" align="center" width="120">
        <template #default="scope">
          <div class="participant-info">
            <div>{{ scope.row.currentParticipants || 0 }} / {{ scope.row.maxParticipants || '不限' }}</div>
            <el-progress
              :percentage="getRegistrationProgress(scope.row.currentParticipants, scope.row.maxParticipants)"
              :color="getProgressColor(scope.row.currentParticipants, scope.row.maxParticipants)" :show-text="false"
              size="small" />
          </div>
        </template>
      </el-table-column>

      <el-table-column label="年龄限制" align="center" width="100">
        <template #default="scope">
          <span v-if="scope.row.ageLimitMin || scope.row.ageLimitMax">
            {{ scope.row.ageLimitMin || 0 }} - {{ scope.row.ageLimitMax || '不限' }}岁
          </span>
          <span v-else class="text-muted">不限制</span>
        </template>
      </el-table-column>

      <el-table-column label="性别限制" align="center" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.genderLimit === 0 ? '' : 'warning'" size="small">
            {{ getGenderLimitText(scope.row.genderLimit) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="额外费用" align="center" width="100">
        <template #default="scope">
          <span v-if="scope.row.additionalFee > 0" class="fee-text">
            ￥{{ scope.row.additionalFee }}
          </span>
          <span v-else class="text-muted">免费</span>
        </template>
      </el-table-column>

      <el-table-column label="特殊要求" min-width="150" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.requirements">{{ scope.row.requirements }}</span>
          <span v-else class="text-muted">无</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="150" fixed="right">
        <template #default="scope">
          <el-button-group size="small">
            <el-tooltip content="编辑" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleEditProject(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="danger" icon="Delete" @click="handleDeleteProject(scope.row)"></el-button>
            </el-tooltip>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="mt-4">
      <pagination v-show="projectTotal > 0" :total="projectTotal" v-model:page="projectQueryParams.pageNum"
        v-model:limit="projectQueryParams.pageSize" @pagination="handleProjectPagination" />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="projectListDialog.visible = false">关 闭</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 添加项目对话框 -->
  <ProjectForm ref="projectFormRef" @success="handleProjectSuccess" />
</template>

<script setup name="ProjectManagement" lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus';
import { listEventItems, getEventItems, delEventItems } from '@/api/event/eventItems';
import { EventItemsVO, EventItemsQuery } from '@/api/event/eventItems/types';
import ProjectForm from './ProjectForm.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const emits = defineEmits<{
  (event: 'success'): void
}>()

// 项目列表对话框
const projectListDialog = reactive({
  visible: false,
  title: '',
  eventId: null as string | number | null,
  eventTitle: ''
});

// 项目相关数据
const projectsList = ref<EventItemsVO[]>([]);
const projectLoading = ref(false);
const projectQueryParams = ref<EventItemsQuery>({
  pageNum: 1,
  pageSize: 10,
  eventId: undefined,
  name: undefined
});
const projectTotal = ref(0);
const projectQueryFormRef = ref<ElFormInstance>();
const projectFormRef = ref();

/** 获取项目列表 */
const getProjectsList = async () => {
  try {
    projectLoading.value = true;
    const res = await listEventItems(projectQueryParams.value);
    projectsList.value = res.rows;
    projectTotal.value = res.total;
  } catch (error) {
    console.error('获取项目列表失败:', error);
    ElMessage.error('获取项目列表失败');
  } finally {
    projectLoading.value = false;
  }
}

/** 项目搜索 */
const handleProjectQuery = () => {
  projectQueryParams.value.pageNum = 1;
  getProjectsList();
}

/** 重置项目搜索 */
const resetProjectQuery = () => {
  projectQueryFormRef.value?.resetFields();
  handleProjectQuery();
}

/** 添加项目 */
const handleAddProject = () => {
  projectFormRef.value?.open({
    title: `为 "${projectListDialog.eventTitle}" 添加项目`,
    eventId: projectListDialog.eventId
  });
}

/** 编辑项目 */
const handleEditProject = async (row: EventItemsVO) => {
  try {
    const res = await getEventItems(row.id);
    projectFormRef.value?.open({
      title: `编辑项目 "${row.name}"`,
      eventId: projectListDialog.eventId,
      data: res.data
    });
  } catch (error) {
    console.error('获取项目详情失败:', error);
    ElMessage.error('获取项目详情失败');
  }
}

/** 删除项目 */
const handleDeleteProject = async (row: EventItemsVO) => {
  try {
    await ElMessageBox.confirm(`确认删除项目 "${row.name}" 吗？此操作不可恢复！`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    await delEventItems(row.id);
    ElMessage.success('删除成功');
    await getProjectsList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除项目失败:', error);
      ElMessage.error('删除项目失败');
    }
  }
}

/** 项目分页改变 */
const handleProjectPagination = (page: any) => {
  projectQueryParams.value.pageNum = page.page;
  projectQueryParams.value.pageSize = page.limit;
  getProjectsList();
}

/** 项目操作成功回调 */
const handleProjectSuccess = async () => {
  await getProjectsList();
  emits('success');
}

/** 获取报名进度百分比 */
const getRegistrationProgress = (current: number = 0, max: number = 0): number => {
  if (!max || max === 0) return 0;
  return Math.min((current / max) * 100, 100);
}

/** 获取进度条颜色 */
const getProgressColor = (current: number = 0, max: number = 0): string => {
  const percentage = getRegistrationProgress(current, max);
  if (percentage >= 90) return '#f56c6c';
  if (percentage >= 70) return '#e6a23c';
  return '#67c23a';
}

/** 获取性别限制文本 */
const getGenderLimitText = (genderLimit: number): string => {
  const genderMap = {
    0: '不限制',
    1: '仅男性',
    2: '仅女性'
  };
  return genderMap[genderLimit] || '不限制';
}

const open = async (option: { eventId: string | number, eventTitle: string }) => {
  projectListDialog.eventId = option.eventId;
  projectListDialog.eventTitle = option.eventTitle;
  projectListDialog.title = `"${option.eventTitle}" 的项目列表`;
  projectListDialog.visible = true;

  // 重置查询参数
  projectQueryParams.value.eventId = option.eventId;
  projectQueryParams.value.pageNum = 1;
  projectQueryParams.value.name = undefined;

  await getProjectsList();
}

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
// 项目列表对话框样式
:deep(.project-list-dialog) {
  .el-dialog__body {
    padding: 20px 30px;
    max-height: 80vh;
    overflow-y: auto;
  }
}

.project-info {
  text-align: left;

  .project-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
    line-height: 1.4;
  }

  .project-desc {
    color: #7f8c8d;
    font-size: 12px;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.participant-info {
  font-size: 12px;

  >div:first-child {
    margin-bottom: 4px;
    font-weight: 600;
    color: #2c3e50;
  }
}

.fee-text {
  color: #e74c3c;
  font-weight: 600;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}
</style>