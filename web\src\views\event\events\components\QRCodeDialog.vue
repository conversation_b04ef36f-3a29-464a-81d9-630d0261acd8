<template>
  <el-dialog :title="qrDialog.title" v-model="qrDialog.visible" width="400px" append-to-body class="qr-dialog">
    <div class="qr-content">
      <div class="qr-code-container">
        <div class="qr-placeholder" v-if="!qrDialog.url">
          <el-icon size="60">
            <Grid />
          </el-icon>
          <p>二维码生成中...</p>
        </div>
        <div v-else class="qr-content-area">
          <canvas ref="qrCanvas" width="200" height="200" style="border: 1px solid #ddd;"></canvas>
          <p class="qr-text">{{ qrDialog.url }}</p>
        </div>
      </div>
      <div class="qr-info">
        <p class="qr-url">报名链接：</p>
        <el-input v-model="qrDialog.url" readonly>
          <template #append>
            <el-button @click="copyUrl" icon="CopyDocument">复制</el-button>
          </template>
        </el-input>
        <p class="qr-tip">请扫描上方二维码或复制链接进行报名</p>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="qrDialog.visible = false">关 闭</el-button>
        <el-button type="primary" @click="downloadQR">
          <el-icon>
            <Download />
          </el-icon>
          下载二维码
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="QRCodeDialog" lang="ts">
import { ElMessage } from 'element-plus';
import { getEventsQRCodeUrl } from '@/api/event/events';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const qrCanvas = ref<HTMLCanvasElement>();

const qrDialog = reactive({
  visible: false,
  title: '',
  url: ''
});

const generateQRCode = async (text: string) => {
  if (!qrCanvas.value) return;

  try {
    // 尝试使用qrcode库生成二维码
    const QRCode = (await import('qrcode')).default;
    await QRCode.toCanvas(qrCanvas.value, text, {
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });
  } catch (error) {
    // 如果qrcode库不可用，使用简单的替代方案
    console.warn('QRCode library not available, using fallback');
    const canvas = qrCanvas.value;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清空画布
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, 200, 200);

    // 简单的二维码替代方案：显示网格图案
    ctx.fillStyle = 'black';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';

    // 绘制类似二维码的网格
    const cellSize = 8;
    const seed = text.length; // 使用文本长度作为随机种子
    for (let i = 0; i < 200; i += cellSize) {
      for (let j = 0; j < 200; j += cellSize) {
        const x = Math.floor(i / cellSize);
        const y = Math.floor(j / cellSize);
        // 使用简单的伪随机函数
        if ((x * y + seed) % 3 === 0) {
          ctx.fillRect(i, j, cellSize, cellSize);
        }
      }
    }

    // 在中心绘制白色背景和文本
    ctx.fillStyle = 'white';
    ctx.fillRect(50, 85, 100, 30);
    ctx.strokeStyle = 'black';
    ctx.strokeRect(50, 85, 100, 30);
    ctx.fillStyle = 'black';
    ctx.fillText('扫码报名', 100, 105);
  }
}

const copyUrl = async () => {
  try {
    await navigator.clipboard.writeText(qrDialog.url);
    ElMessage.success('链接复制成功');
  } catch (error) {
    // 降级方案
    const input = document.createElement('input');
    input.value = qrDialog.url;
    document.body.appendChild(input);
    input.select();
    document.execCommand('copy');
    document.body.removeChild(input);
    ElMessage.success('链接复制成功');
  }
}

const downloadQR = () => {
  try {
    if (qrCanvas.value) {
      const link = document.createElement('a');
      link.download = `报名二维码_${new Date().getTime()}.png`;
      link.href = qrCanvas.value.toDataURL();
      link.click();
      ElMessage.success('二维码下载成功');
    } else {
      ElMessage.error('二维码下载失败');
    }
  } catch (error) {
    ElMessage.error('二维码下载失败');
  }
}

const showQRCode = (url: string, title: string) => {
  qrDialog.url = url;
  qrDialog.title = `${title} - 报名二维码`;
  qrDialog.visible = true;

  // 延迟生成二维码，确保DOM已渲染
  nextTick(() => {
    generateQRCode(url);
  });
}

const generateEventQR = async (eventId: string | number, eventTitle: string) => {
  try {
    const res = await getEventsQRCodeUrl(eventId);
    const qrCodeUrl = res.msg;
    showQRCode(qrCodeUrl, eventTitle);
  } catch (error) {
    ElMessage.error('二维码生成失败');
  }
}

defineExpose({
  generateEventQR,
  showQRCode
});
</script>

<style lang="scss" scoped>
// 二维码对话框样式
:deep(.qr-dialog) {
  .el-dialog__body {
    padding: 30px;
  }

  .qr-content {
    text-align: center;

    .qr-code-container {
      margin-bottom: 20px;
      padding: 20px;
      background: #fff;
      border: 1px solid #e1e6f0;
      border-radius: 8px;
      display: inline-block;

      .qr-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #909399;

        p {
          margin: 10px 0 0 0;
        }
      }

      .qr-content-area {
        .qr-text {
          margin-top: 10px;
          font-size: 12px;
          color: #909399;
          word-break: break-all;
        }
      }
    }

    .qr-info {
      .qr-url {
        margin: 20px 0 10px 0;
        font-weight: 600;
        color: #2c3e50;
      }

      .qr-tip {
        margin: 15px 0 0 0;
        font-size: 12px;
        color: #909399;
      }

      .el-input-group__append {
        .el-button {
          border-left: none;
        }
      }
    }
  }
}
</style>