<template>
  <el-dialog :title="detailDialog.title" v-model="detailDialog.visible" width="800px" append-to-body>
    <div v-if="detailDialog.registrationData" class="registration-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4 class="section-title">基本信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="参赛者姓名">
            {{ detailDialog.registrationData.participantName }}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{ detailDialog.registrationData.phone }}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{ detailDialog.registrationData.email || '未填写' }}
          </el-descriptions-item>
          <el-descriptions-item label="报名时间">
            {{ parseTime(detailDialog.registrationData.createTime, '{y}-{m}-{d} {h}:{i}') }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 报名表单数据 -->
      <div class="detail-section" v-if="detailDialog.registrationData.formData">
        <h4 class="section-title">报名表单数据</h4>
        <RegistrationDataViewer :data="detailDialog.registrationData.formData" />
      </div>

      <!-- 支付信息 -->
      <div class="detail-section">
        <h4 class="section-title">支付信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="支付状态">
            <el-tag :type="detailDialog.registrationData.paymentStatus === 1 ? 'success' : 'warning'">
              {{ detailDialog.registrationData.paymentStatus === 1 ? '已支付' : '未支付' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="报名费用">
            ￥{{ detailDialog.registrationData.fee || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="支付时间" v-if="detailDialog.registrationData.paymentTime">
            {{ parseTime(detailDialog.registrationData.paymentTime, '{y}-{m}-{d} {h}:{i}') }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" v-if="detailDialog.registrationData.remark">
            {{ detailDialog.registrationData.remark }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="detailDialog.visible = false">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="RegistrationDetail" lang="ts">
import RegistrationDataViewer from "@/components/FormBuilder/RegistrationDataViewer.vue";
import { parseTime } from '@/utils/ruoyi';

const emits = defineEmits<{
  (event: 'success'): void
}>()

// 详情对话框
const detailDialog = reactive({
  visible: false,
  title: '',
  registrationData: null as any
});

const open = (data: any) => {
  detailDialog.registrationData = data;
  detailDialog.title = `${data.participantName} 的报名详情`;
  detailDialog.visible = true;
}

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.registration-detail {
  .detail-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
    }
  }
}
</style>