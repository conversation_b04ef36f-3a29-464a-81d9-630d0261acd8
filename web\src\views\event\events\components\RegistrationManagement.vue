<template>
  <!-- 报名管理对话框 -->
  <el-dialog :title="registrationDialog.title" v-model="registrationDialog.visible" width="1400px" append-to-body
    class="registration-dialog">
    <!-- 搜索区域 -->
    <div class="mb-4">
      <el-card shadow="hover">
        <el-form ref="registrationQueryFormRef" :model="registrationQueryParams" :inline="true">
          <el-form-item label="项目" prop="eventItemId">
            <el-select v-model="registrationQueryParams.eventItemId" placeholder="请选择项目" clearable
              style="width: 200px">
              <el-option v-for="item in projectsList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="报名信息" prop="registrationData">
            <el-input v-model="registrationQueryParams.registrationData" placeholder="请输入报名信息" clearable />
          </el-form-item>
          <el-form-item label="支付状态" prop="paymentStatus">
            <el-select v-model="registrationQueryParams.paymentStatus" placeholder="请选择支付状态" clearable
              style="width: 150px">
              <el-option label="未支付" :value="0"></el-option>
              <el-option label="已支付" :value="1"></el-option>
              <el-option label="支付失败" :value="2"></el-option>
              <el-option label="已退款" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleRegistrationQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetRegistrationQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 报名表格 -->
    <el-table v-loading="registrationLoading" :data="registrationsList" border style="width: 100%">
      <el-table-column label="报名信息" min-width="320">
        <template #default="scope">
          <div class="participant-info-enhanced">
            <div class="registration-details">
              <RegistrationDataViewer :formFieldsInfo="getFormFieldsInfo(scope.row.registrationData)"
                :formData="getFormData(scope.row.registrationData)"
                :compact="!detailedViewIds.has(scope.row.id?.toString() || '')"
                :maxFields="detailedViewIds.has(scope.row.id?.toString() || '') ? 20 : 4"
                :showEmptyFields="detailedViewIds.has(scope.row.id?.toString() || '')" />
            </div>
            <div class="view-toggle">
              <el-button link type="primary" size="small" @click="toggleDetailedView(scope.row.id?.toString() || '')"
                :icon="detailedViewIds.has(scope.row.id?.toString() || '') ? 'Fold' : 'Expand'">
                {{ detailedViewIds.has(scope.row.id?.toString() || '') ? '收起' : '详情' }}
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="报名时间" align="center" min-width="150">
        <template #default="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目名称" align="center" min-width="150">
        <template #default="scope">
          <span>{{ projectsList.find(p => p.id === scope.row.eventItemId)?.name || '未知项目' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="支付状态" align="center" width="100">
        <template #default="scope">
          <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)" size="small">
            {{ getPaymentStatusText(scope.row.paymentStatus) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="支付金额" align="center" width="100">
        <template #default="scope">
          <span class="fee-text">￥{{ scope.row.paymentAmount || 0 }}</span>
        </template>
      </el-table-column>

      <el-table-column label="支付方式" align="center" width="100">
        <template #default="scope">
          <span>{{ scope.row.paymentMethod || '未知' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="支付时间" align="center" width="160">
        <template #default="scope">
          <span v-if="scope.row.paymentTime">{{ parseTime(scope.row.paymentTime, '{y}-{m}-{d} {h}:{i}') }}</span>
          <span v-else class="text-muted">未支付</span>
        </template>
      </el-table-column>

      <el-table-column label="签到状态" align="center" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.checkInStatus === 1 ? 'success' : 'info'" size="small">
            {{ scope.row.checkInStatus === 1 ? '已签到' : '未签到' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="180" fixed="right">
        <template #default="scope">
          <el-button-group size="small">
            <el-tooltip content="查看详情" placement="top">
              <el-button link type="primary" icon="View" @click="handleViewRegistration(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="参赛结果" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleViewCompetitionResults(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="支付管理" placement="top">
              <el-button link type="warning" icon="Money" @click="handlePaymentManage(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="danger" icon="Delete" @click="handlePaymentDel(scope.row)"></el-button>
            </el-tooltip>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="mt-4">
      <pagination v-show="registrationTotal > 0" :total="registrationTotal"
        v-model:page="registrationQueryParams.pageNum" v-model:limit="registrationQueryParams.pageSize"
        @pagination="handleRegistrationPagination" />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="registrationDialog.visible = false">关 闭</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 报名详情对话框 -->
  <RegistrationDetail ref="registrationDetailRef" />

  <!-- 支付管理对话框 -->
  <PaymentManagement ref="paymentManagementRef" @success="getRegistrationsList" />

  <!-- 参赛结果设置对话框 -->
  <CompetitionResult ref="competitionResultRef" @success="getRegistrationsList" />
</template>

<script setup name="RegistrationManagement" lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus';
import { listRegistrations, delRegistrations, listEventItems } from '@/api/event/registrations';
import { RegistrationsVO, RegistrationsQuery } from '@/api/event/registrations/types';
import { EventItemsVO } from '@/api/event/eventItems/types';
import RegistrationDataViewer from '@/components/FormBuilder/RegistrationDataViewer.vue';
import RegistrationDetail from './RegistrationDetail.vue';
import PaymentManagement from './PaymentManagement.vue';
import CompetitionResult from './CompetitionResult.vue';

const emits = defineEmits<{
  (event: 'success'): void
}>()

// 报名管理对话框
const registrationDialog = reactive({
  visible: false,
  title: '',
  eventId: null as string | number | null,
  eventTitle: ''
});

// 报名管理相关数据
const registrationsList = ref<RegistrationsVO[]>([]);
const registrationLoading = ref(false);
const registrationQueryParams = ref<RegistrationsQuery>({
  pageNum: 1,
  pageSize: 10,
  eventId: undefined,
  eventItemId: undefined,
  paymentStatus: undefined,
  registrationData: undefined,
});
const registrationTotal = ref(0);
const registrationQueryFormRef = ref<ElFormInstance>();

// 项目列表（用于筛选）
const projectsList = ref<EventItemsVO[]>([]);

// 表单字段配置缓存和详情展示状态
const detailedViewIds = ref<Set<string>>(new Set());

// 组件引用
const registrationDetailRef = ref();
const paymentManagementRef = ref();
const competitionResultRef = ref();

/** 获取报名列表 */
const getRegistrationsList = async () => {
  try {
    registrationLoading.value = true;
    const res = await listRegistrations(registrationQueryParams.value);
    registrationsList.value = res.rows;
    registrationTotal.value = res.total;
  } catch (error) {
    console.error('获取报名列表失败:', error);
    ElMessage.error('获取报名列表失败');
  } finally {
    registrationLoading.value = false;
  }
}

/** 报名搜索 */
const handleRegistrationQuery = () => {
  registrationQueryParams.value.pageNum = 1;
  getRegistrationsList();
}

/** 重置报名搜索 */
const resetRegistrationQuery = () => {
  registrationQueryFormRef.value?.resetFields();
  registrationQueryParams.value.eventItemId = undefined;
  registrationQueryParams.value.paymentStatus = undefined;
  handleRegistrationQuery();
}

/** 报名分页改变 */
const handleRegistrationPagination = (page: any) => {
  registrationQueryParams.value.pageNum = page.page;
  registrationQueryParams.value.pageSize = page.limit;
  getRegistrationsList();
}

/** 查看报名详情 */
const handleViewRegistration = (row: RegistrationsVO) => {
  registrationDetailRef.value?.open(row);
}

/** 参赛结果设置 */
const handleViewCompetitionResults = (row: RegistrationsVO) => {
  competitionResultRef.value?.open(row);
}

/** 支付管理 */
const handlePaymentManage = (row: RegistrationsVO) => {
  paymentManagementRef.value?.open(row);
}

/** 删除报名记录 */
const handlePaymentDel = async (row: RegistrationsVO) => {
  try {
    await ElMessageBox.confirm(`确认删除报名记录吗？此操作不可恢复！`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    await delRegistrations(row.id);
    ElMessage.success('删除成功');
    await getRegistrationsList();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
}

// 获取表单字段配置信息
const getFormFieldsInfo = (registrationData) => {
  const data = JSON.parse(registrationData);
  return data.formFieldsInfo;
};

const getFormData = (registrationData) => {
  if (registrationData) {
    const data = JSON.parse(registrationData);
    return data.formData;
  }
}

// 切换详细视图
const toggleDetailedView = (id: string) => {
  if (detailedViewIds.value.has(id)) {
    detailedViewIds.value.delete(id);
  } else {
    detailedViewIds.value.add(id);
  }
};

/** 获取支付状态文本 */
const getPaymentStatusText = (status: number): string => {
  const statusMap = {
    0: '未支付',
    1: '已支付',
    2: '支付失败',
    3: '已退款'
  };
  return statusMap[status] || '未知';
}

/** 获取支付状态类型 */
const getPaymentStatusType = (status: number): string => {
  const typeMap = {
    0: 'warning',
    1: 'success',
    2: 'danger',
    3: 'info'
  };
  return typeMap[status] || 'info';
}

const open = async (option: { eventId: string | number, eventTitle: string }) => {
  registrationDialog.eventId = option.eventId;
  registrationDialog.eventTitle = option.eventTitle;
  registrationDialog.title = `"${option.eventTitle}" 的报名管理`;
  registrationDialog.visible = true;

  // 重置查询参数
  registrationQueryParams.value.eventId = option.eventId;
  registrationQueryParams.value.pageNum = 1;
  registrationQueryParams.value.eventItemId = undefined;
  registrationQueryParams.value.paymentStatus = undefined;

  // 获取项目列表（用于筛选）
  try {
    const projectRes = await listEventItems({
      eventId: option.eventId,
      pageNum: 1,
      pageSize: 100
    });
    projectsList.value = projectRes.rows;
  } catch (error) {
    console.error('获取项目列表失败:', error);
  }

  // 获取报名列表
  await getRegistrationsList();
}

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
@import '../styles/registration-management.scss';
</style>