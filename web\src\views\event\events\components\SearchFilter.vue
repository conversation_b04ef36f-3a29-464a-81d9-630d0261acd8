<template>
  <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
    :leave-active-class="proxy?.animate.searchAnimate.leave">
    <div v-show="showSearch" class="mb-[10px]">
      <el-card shadow="hover">
        <el-form ref="queryFormRef" label-width="130px" :model="queryParams" :inline="true">
          <el-form-item label="标题" prop="title">
            <el-input style="width:220px" v-model="queryParams.title" placeholder="请输入标题" clearable
              @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker style="width:220px" clearable v-model="queryParams.startTime" type="date"
              value-format="YYYY-MM-DD" placeholder="请选择开始时间" />
          </el-form-item>
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker style="width:220px" clearable v-model="queryParams.endTime" type="date"
              value-format="YYYY-MM-DD" placeholder="请选择结束时间" />
          </el-form-item>
          <el-form-item label="报名截止时间" prop="registrationDeadline">
            <el-date-picker style="width:220px" clearable v-model="queryParams.registrationDeadline" type="date"
              value-format="YYYY-MM-DD" placeholder="请选择报名截止时间" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select style="width:220px" v-model="queryParams.status" placeholder="请选择状态" clearable>
              <el-option v-for="dict in event_status" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref, getCurrentInstance } from 'vue';
import type { ElFormInstance } from 'element-plus';
import type { EventsQuery } from '@/api/event/events/types';

interface Props {
  showSearch: boolean;
  queryParams: EventsQuery;
}

interface Emits {
  (e: 'query'): void;
  (e: 'reset'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { event_status } = toRefs<any>(proxy?.useDict('event_status'));

const queryFormRef = ref<ElFormInstance>();

/** 搜索按钮操作 */
const handleQuery = () => {
  emit('query');
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  emit('reset');
};

// 暴露方法给父组件
defineExpose({
  resetFields: () => queryFormRef.value?.resetFields()
});
</script>
