<template>
  <div class="p-2">
    <!-- 搜索过滤组件 -->
    <SearchFilter
      :show-search="showSearch"
      :query-params="queryParams"
      @query="handleQuery"
      @reset="resetQuery"
    />

    <el-card shadow="never">
      <template #header>
        <!-- 操作工具栏组件 -->
        <OperationToolbar
          :single="single"
          :multiple="multiple"
          v-model:show-search="showSearch"
          @add="handleAdd"
          @update="handleUpdate"
          @delete="handleDelete"
          @export="handleExport"
          @refresh="getList"
        />
      </template>

      <!-- 事件表格组件 -->
      <EventTable
        :loading="loading"
        :events-list="eventsList"
        @selection-change="handleSelectionChange"
        @view="handleView"
        @update="handleUpdate"
        @registrations="handleRegistrations"
        @edit-form="handleEditForm"
        @generate-qr="handleGenerateQR"
        @view-projects="handleViewProjects"
        @customer-service="handleCustomerService"
        @certificate-manage="handleCertificateManage"
        @more-actions="handleMoreActions"
      />

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 事件表单对话框 -->
    <EventForm ref="eventFormRef" @success="getList" />

    <!-- 事件详情对话框 -->
    <EventDetailDialog
      ref="eventDetailDialogRef"
      @registrations="handleRegistrations"
      @generate-qr="handleGenerateQR"
    />

    <!-- 二维码对话框 -->
    <QRCodeDialog ref="qrCodeDialogRef" />

    <!-- 项目管理对话框 -->
    <ProjectManagement ref="projectManagementRef" @success="getList" />

    <!-- 报名管理对话框 -->
    <RegistrationManagement ref="registrationManagementRef" @success="getList" />

    <!-- 客服管理对话框 -->
    <CustomerServiceManagement ref="customerServiceManagementRef" @success="getList" />

    <!-- 证书管理对话框 -->
    <CertificateManagement ref="certificateManagementRef" @success="getList" />
    
    <!-- 表单构建器对话框 -->
    <el-dialog :title="formBuilderDialog.title" v-model="formBuilderDialog.visible" width="95%" append-to-body>
      <FormBuilder ref="formBuilderRef" />
    </el-dialog>
    
    <!-- 添加或修改列表对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="1000px" append-to-body class="event-dialog">
      <el-form ref="eventsFormRef" :model="form" :rules="rules" label-width="120px" class="event-form">

        <!-- 基础必填信息 -->
        <div class="form-section">
          <h4 class="section-title">基础信息 <span class="required-mark">*必填</span></h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="标题" prop="title" required>
                <el-input v-model="form.title" placeholder="请输入标题" maxlength="100" show-word-limit />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="主办方" prop="organizer" required>
                <el-input v-model="form.organizer" placeholder="请输入主办方" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系方式" prop="contactInfo" required>
                <el-input v-model="form.contactInfo" placeholder="请输入联系方式" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大参与人数" prop="maxParticipants" required>
                <el-input-number v-model="form.maxParticipants" :min="1" :max="10000" placeholder="请输入最大参与人数"
                  style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 描述 - 单独一行 -->
          <el-form-item label="描述" prop="description" required>
            <editor v-model="form.description" :min-height="220" />
          </el-form-item>
        </div>
        <!-- 海报上传 -->
        <div class="form-section">
          <h4 class="section-title">海报图片 <span class="required-mark">*必填</span></h4>
          <el-form-item label="海报图片" prop="poster" required>
            <ImageUpload v-model="form.poster" />
          </el-form-item>
        </div>
        <!-- 时间地点信息 -->
        <div class="form-section">
          <h4 class="section-title">时间地点</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="开始时间" prop="startTime">
                <el-date-picker v-model="form.startTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择开始时间" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束时间" prop="endTime">
                <el-date-picker v-model="form.endTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择结束时间" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="报名截止时间" prop="registrationDeadline">
                <el-date-picker v-model="form.registrationDeadline" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择报名截止时间" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="举办地点" prop="location">
                <el-input v-model="form.location" placeholder="请输入举办地点" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 费用状态信息 -->
        <div class="form-section">
          <h4 class="section-title">费用状态</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="报名费用" prop="fee">
                <el-input-number v-model="form.fee" :min="0" :precision="2" placeholder="请输入报名费用" style="width: 100%">
                  <template #append>元</template>
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                  <el-option v-for="dict in event_status" :key="dict.value" :label="dict.label"
                    :value="parseInt(dict.value)"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>



        <!-- 详细设置 -->
        <div class="form-section">
          <h4 class="section-title">详细设置</h4>

          <!-- 规则 - 单独一行 -->
          <el-form-item label="规则" prop="rules">
            <editor v-model="form.rules" :min-height="240" />
          </el-form-item>

          <!-- 奖励设置 - 单独一行 -->
          <el-form-item label="奖励设置" prop="rewards">
            <editor v-model="form.rewards" :min-height="200" />
          </el-form-item>

          <!-- 备注 - 单独一行 -->
          <el-form-item label="备注信息" prop="remark">
            <editor v-model="form.remark" :min-height="160" />
          </el-form-item>
        </div>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">
            <el-icon>
              <Check />
            </el-icon>
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 二维码显示对话框 -->
    <el-dialog :title="qrDialog.title" v-model="qrDialog.visible" width="400px" append-to-body class="qr-dialog">
      <div class="qr-content">
        <div class="qr-code-container">
          <div class="qr-placeholder" v-if="!qrDialog.url">
            <el-icon size="60">
              <Grid />
            </el-icon>
            <p>二维码生成中...</p>
          </div>
          <div v-else class="qr-content-area">
            <canvas ref="qrCanvas" width="200" height="200" style="border: 1px solid #ddd;"></canvas>
            <p class="qr-text">{{ qrDialog.url }}</p>
          </div>
        </div>
        <div class="qr-info">
          <p class="qr-url">报名链接：</p>
          <el-input v-model="qrDialog.url" readonly>
            <template #append>
              <el-button @click="copyUrl" icon="CopyDocument">复制</el-button>
            </template>
          </el-input>
          <p class="qr-tip">请扫描上方二维码或复制链接进行报名</p>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="qrDialog.visible = false">关 闭</el-button>
          <el-button type="primary" @click="downloadQR">
            <el-icon>
              <Download />
            </el-icon>
            下载二维码
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog title="详情" v-model="detailDialog.visible" width="900px" append-to-body class="event-detail-dialog">
      <div class="detail-content">
        <!-- 基础信息 -->
        <div class="detail-section">
          <h4 class="section-title">
            <el-icon>
              <InfoFilled />
            </el-icon>
            基础信息
          </h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="标题" :span="2">
              <span class="event-title">{{ detailDialog.eventDetail.title }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="主办方">
              {{ detailDialog.eventDetail.organizer || '暂无' }}
            </el-descriptions-item>
            <el-descriptions-item label="联系方式">
              {{ detailDialog.eventDetail.contactInfo || '暂无' }}
            </el-descriptions-item>
            <el-descriptions-item label="举办地点" :span="2">
              {{ detailDialog.eventDetail.location || '暂无' }}
            </el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">
              <div v-if="detailDialog.eventDetail.description" class="description-text rich-text"
                v-html="detailDialog.eventDetail.description"></div>
              <span v-else>暂无</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 时间信息 -->
        <div class="detail-section">
          <h4 class="section-title">
            <el-icon>
              <Clock />
            </el-icon>
            时间安排
          </h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="开始时间">
              <el-tag type="success" v-if="detailDialog.eventDetail.startTime">
                {{ parseTime(detailDialog.eventDetail.startTime, '{y}-{m}-{d} {h}:{i}') }}
              </el-tag>
              <span v-else>暂无</span>
            </el-descriptions-item>
            <el-descriptions-item label="结束时间">
              <el-tag type="info" v-if="detailDialog.eventDetail.endTime">
                {{ parseTime(detailDialog.eventDetail.endTime, '{y}-{m}-{d} {h}:{i}') }}
              </el-tag>
              <span v-else>暂无</span>
            </el-descriptions-item>
            <el-descriptions-item label="报名截止时间" :span="2">
              <el-tag :type="isDeadlineExpired(detailDialog.eventDetail.registrationDeadline) ? 'danger' : 'warning'"
                v-if="detailDialog.eventDetail.registrationDeadline">
                {{ parseTime(detailDialog.eventDetail.registrationDeadline, '{y}-{m}-{d} {h}:{i}') }}
                <span v-if="isDeadlineExpired(detailDialog.eventDetail.registrationDeadline)"> (已过期)</span>
              </el-tag>
              <span v-else>暂无</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 报名信息 -->
        <div class="detail-section">
          <h4 class="section-title">
            <el-icon>
              <User />
            </el-icon>
            报名信息
          </h4>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="报名费用">
              <el-tag type="danger" size="large">
                ￥{{ detailDialog.eventDetail.fee || 0 }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="最大人数">
              {{ detailDialog.eventDetail.maxParticipants || '不限' }}
            </el-descriptions-item>
            <el-descriptions-item label="当前报名">
              <el-tag
                :type="getRegistrationProgressType(detailDialog.eventDetail.currentParticipants, detailDialog.eventDetail.maxParticipants)">
                {{ detailDialog.eventDetail.currentParticipants || 0 }}人
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态" :span="3">
              <el-tag :type="getStatusType(detailDialog.eventDetail.status)" size="large" effect="dark">
                {{ getStatusText(detailDialog.eventDetail.status) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 详细设置 -->
        <div class="detail-section"
          v-if="detailDialog.eventDetail.rules || detailDialog.eventDetail.rewards || detailDialog.eventDetail.remark">
          <h4 class="section-title">
            <el-icon>
              <Document />
            </el-icon>
            详细设置
          </h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="规则" v-if="detailDialog.eventDetail.rules">
              <div class="rule-content rich-text" v-html="detailDialog.eventDetail.rules"></div>
            </el-descriptions-item>
            <el-descriptions-item label="奖励设置" v-if="detailDialog.eventDetail.rewards">
              <div class="reward-content rich-text" v-html="detailDialog.eventDetail.rewards"></div>
            </el-descriptions-item>
            <el-descriptions-item label="备注信息" v-if="detailDialog.eventDetail.remark">
              <div class="remark-content rich-text" v-html="detailDialog.eventDetail.remark"></div>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 海报预览 -->
        <div class="detail-section" v-if="detailDialog.eventDetail.posterUrl">
          <h4 class="section-title">
            <el-icon>
              <Picture />
            </el-icon>
            海报预览
          </h4>
          <div class="poster-preview">
            <el-image :src="detailDialog.eventDetail.posterUrl" :preview-src-list="[detailDialog.eventDetail.posterUrl]"
              fit="contain" style="max-width: 100px; max-height: 100px; border-radius: 8px;" />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialog.visible = false">关 闭</el-button>
          <el-button type="primary" @click="handleRegistrations(detailDialog.eventDetail)">
            <el-icon>
              <User />
            </el-icon>
            报名管理
          </el-button>
          <el-button type="success" @click="handleGenerateQR(detailDialog.eventDetail)">
            <el-icon>
              <Grid />
            </el-icon>
            生成二维码
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加项目对话框 -->
    <el-dialog :title="projectDialog.title" v-model="projectDialog.visible" width="1000px" append-to-body
      class="project-dialog">
      <el-form ref="projectFormRef" :model="projectForm" :rules="projectRules" label-width="120px" class="project-form">
        <div class="form-section">
          <h4 class="section-title">基础信息 <span class="required-mark">*必填</span></h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="项目名称" prop="name" required>
                <el-input v-model="projectForm.name" placeholder="请输入项目名称" maxlength="50" show-word-limit />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大参与人数" prop="maxParticipants" required>
                <el-input-number v-model="projectForm.maxParticipants" :min="1" :max="10000" placeholder="请输入最大参与人数"
                  style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="项目描述" prop="description" required>
            <editor v-model="projectForm.description" :min-height="220" />
          </el-form-item>
        </div>

        <div class="form-section">
          <h4 class="section-title">参与限制</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="最小年龄">
                <el-input-number v-model="projectForm.ageLimitMin" :min="0" :max="100" placeholder="最小年龄"
                  style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最大年龄">
                <el-input-number v-model="projectForm.ageLimitMax" :min="0" :max="100" placeholder="最大年龄"
                  style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="性别限制">
                <el-select v-model="projectForm.genderLimit" placeholder="请选择性别限制" style="width: 100%">
                  <el-option label="不限制" :value="0"></el-option>
                  <el-option label="仅男性" :value="1"></el-option>
                  <el-option label="仅女性" :value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-section">
          <h4 class="section-title">其他设置</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="额外费用">
                <el-input-number v-model="projectForm.additionalFee" :min="0" :precision="2" placeholder="额外费用"
                  style="width: 100%">
                  <template #append>元</template>
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序">
                <el-input-number v-model="projectForm.sortOrder" :min="0" placeholder="数字越小越靠前" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="特殊要求">
            <editor v-model="projectForm.requirements" :min-height="180" />
          </el-form-item>

          <el-form-item label="备注">
            <editor v-model="projectForm.remark" :min-height="160" />
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelProjectForm">取 消</el-button>
          <el-button :loading="projectLoading" type="primary" @click="submitProjectForm">
            <el-icon>
              <Check />
            </el-icon>
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 项目列表对话框 -->
    <el-dialog :title="projectListDialog.title" v-model="projectListDialog.visible" width="1200px" append-to-body
      class="project-list-dialog">
      <!-- 搜索区域 -->
      <div class="mb-4">
        <el-card shadow="hover">
          <el-form ref="projectQueryFormRef" :model="projectQueryParams" :inline="true">
            <el-form-item label="项目名称" prop="name">
              <el-input v-model="projectQueryParams.name" placeholder="请输入项目名称" clearable
                @keyup.enter="handleProjectQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleProjectQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetProjectQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 操作按钮 -->
      <div class="mb-4">
        <el-button type="primary" icon="Plus"
          @click="projectDialog.visible = true; resetProjectForm(); projectForm.eventId = projectListDialog.eventId; projectDialog.title = `为 '${projectListDialog.eventTitle}' 添加项目`;">
          添加项目
        </el-button>
      </div>

      <!-- 项目表格 -->
      <el-table v-loading="projectLoading" :data="projectsList" border style="width: 100%">
        <el-table-column label="项目信息" min-width="200">
          <template #default="scope">
            <div class="project-info">
              <div class="project-name">{{ scope.row.name }}</div>
              <div class="project-desc">{{ scope.row.description }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="参与人数" align="center" width="120">
          <template #default="scope">
            <div class="participant-info">
              <div>{{ scope.row.currentParticipants || 0 }} / {{ scope.row.maxParticipants || '不限' }}</div>
              <el-progress
                :percentage="getRegistrationProgress(scope.row.currentParticipants, scope.row.maxParticipants)"
                :color="getProgressColor(scope.row.currentParticipants, scope.row.maxParticipants)" :show-text="false"
                size="small" />
            </div>
          </template>
        </el-table-column>

        <el-table-column label="年龄限制" align="center" width="100">
          <template #default="scope">
            <span v-if="scope.row.ageLimitMin || scope.row.ageLimitMax">
              {{ scope.row.ageLimitMin || 0 }} - {{ scope.row.ageLimitMax || '不限' }}岁
            </span>
            <span v-else class="text-muted">不限制</span>
          </template>
        </el-table-column>

        <el-table-column label="性别限制" align="center" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.genderLimit === 0 ? '' : 'warning'" size="small">
              {{ getGenderLimitText(scope.row.genderLimit) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="额外费用" align="center" width="100">
          <template #default="scope">
            <span v-if="scope.row.additionalFee > 0" class="fee-text">
              ￥{{ scope.row.additionalFee }}
            </span>
            <span v-else class="text-muted">免费</span>
          </template>
        </el-table-column>

        <el-table-column label="特殊要求" min-width="150" show-overflow-tooltip>
          <template #default="scope">
            <span v-if="scope.row.requirements">{{ scope.row.requirements }}</span>
            <span v-else class="text-muted">无</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="150" fixed="right">
          <template #default="scope">
            <el-button-group size="small">
              <el-tooltip content="编辑" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleEditProject(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button link type="danger" icon="Delete" @click="handleDeleteProject(scope.row)"></el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4">
        <pagination v-show="projectTotal > 0" :total="projectTotal" v-model:page="projectQueryParams.pageNum"
          v-model:limit="projectQueryParams.pageSize" @pagination="handleProjectPagination" />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="projectListDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 报名管理对话框 -->
    <el-dialog :title="registrationDialog.title" v-model="registrationDialog.visible" width="1400px" append-to-body
      class="registration-dialog">
      <!-- 搜索区域 -->
      <div class="mb-4">
        <el-card shadow="hover">
          <el-form ref="registrationQueryFormRef" :model="registrationQueryParams" :inline="true">
            <el-form-item label="项目" prop="eventItemId">
              <el-select v-model="registrationQueryParams.eventItemId" placeholder="请选择项目" clearable
                style="width: 200px">
                <el-option v-for="item in projectsList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="报名信息" prop="registrationData">
              <el-input v-model="registrationQueryParams.registrationData" placeholder="请输入报名信息" clearable />
            </el-form-item>
            <el-form-item label="支付状态" prop="paymentStatus">
              <el-select v-model="registrationQueryParams.paymentStatus" placeholder="请选择支付状态" clearable
                style="width: 150px">
                <el-option label="未支付" :value="0"></el-option>
                <el-option label="已支付" :value="1"></el-option>
                <el-option label="支付失败" :value="2"></el-option>
                <el-option label="已退款" :value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleRegistrationQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetRegistrationQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 操作按钮 -->
      <!-- <div class="mb-4">
        <el-alert title="报名管理说明" type="info" :closable="false" style="margin-bottom: 16px">
          <template #default>
            <p>可以通过项目筛选查看不同项目的报名情况，支持查看详情和管理支付状态</p>
          </template>
        </el-alert>
      </div> -->

      <!-- 报名表格 -->
      <el-table v-loading="registrationLoading" :data="registrationsList" border style="width: 100%">
        <el-table-column label="报名信息" min-width="320">
          <template #default="scope">
            <div class="participant-info-enhanced">
              <div class="registration-details">
                <RegistrationDataViewer :formFieldsInfo="getFormFieldsInfo(scope.row.registrationData)"
                  :formData="getFormData(scope.row.registrationData)"
                  :compact="!detailedViewIds.has(scope.row.id?.toString() || '')"
                  :maxFields="detailedViewIds.has(scope.row.id?.toString() || '') ? 20 : 4"
                  :showEmptyFields="detailedViewIds.has(scope.row.id?.toString() || '')" />
              </div>
              <div class="view-toggle">
                <el-button link type="primary" size="small" @click="toggleDetailedView(scope.row.id?.toString() || '')"
                  :icon="detailedViewIds.has(scope.row.id?.toString() || '') ? 'Fold' : 'Expand'">
                  {{ detailedViewIds.has(scope.row.id?.toString() || '') ? '收起' : '详情' }}
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="报名时间" align="center" min-width="150">
          <template #default="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目名称" align="center" min-width="150">
          <template #default="scope">
            <span>{{ projectsList.find(p => p.id === scope.row.eventItemId)?.name || '未知项目' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="支付状态" align="center" width="100">
          <template #default="scope">
            <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)" size="small">
              {{ getPaymentStatusText(scope.row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="支付金额" align="center" width="100">
          <template #default="scope">
            <span class="fee-text">￥{{ scope.row.paymentAmount || 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="支付方式" align="center" width="100">
          <template #default="scope">
            <span>{{ scope.row.paymentMethod || '未知' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="支付时间" align="center" width="160">
          <template #default="scope">
            <span v-if="scope.row.paymentTime">{{ parseTime(scope.row.paymentTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            <span v-else class="text-muted">未支付</span>
          </template>
        </el-table-column>

        <el-table-column label="签到状态" align="center" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.checkInStatus === 1 ? 'success' : 'info'" size="small">
              {{ scope.row.checkInStatus === 1 ? '已签到' : '未签到' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="180" fixed="right">
          <template #default="scope">
            <el-button-group size="small">
              <el-tooltip content="查看详情" placement="top">
                <el-button link type="primary" icon="View" @click="handleViewRegistration(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="参赛结果" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleViewCompetitionResults(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="支付管理" placement="top">
                <el-button link type="warning" icon="Money" @click="handlePaymentManage(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button link type="danger" icon="Delete" @click="handlePaymentDel(scope.row)"></el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4">
        <pagination v-show="registrationTotal > 0" :total="registrationTotal"
          v-model:page="registrationQueryParams.pageNum" v-model:limit="registrationQueryParams.pageSize"
          @pagination="handleRegistrationPagination" />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="registrationDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 表单构建器对话框 -->
    <el-dialog :title="formBuilderDialog.title" v-model="formBuilderDialog.visible" width="95%" append-to-body
      class="form-builder-dialog" :before-close="handleFormBuilderClose">
      <FormBuilder v-if="formBuilderDialog.visible" :event-id="formBuilderDialog.eventId" @save="handleFormSave"
        @change="handleFormChange" />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="formBuilderDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 报名详情对话框 -->
    <el-dialog :title="registrationDetailDialog.title" v-model="registrationDetailDialog.visible" width="800px"
      append-to-body class="registration-detail-dialog">
      <div class="detail-content">
        <!-- 基础信息 -->
        <div class="detail-section">
          <h4 class="section-title">
            <el-icon>
              <User />
            </el-icon>
            报名信息
          </h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="姓名" label-width="150px">
              {{ parseRegistrationData(registrationDetailDialog.detail.registrationData).name || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="手机号" label-width="150px">
              {{ parseRegistrationData(registrationDetailDialog.detail.registrationData).phone || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱" label-width="150px">
              {{ parseRegistrationData(registrationDetailDialog.detail.registrationData).email || '未填写' }}
            </el-descriptions-item>
            <el-descriptions-item label="性别" label-width="150px">
              {{ parseRegistrationData(registrationDetailDialog.detail.registrationData).gender || '未填写' }}
            </el-descriptions-item>
            <el-descriptions-item label="年龄" label-width="150px">
              {{ parseRegistrationData(registrationDetailDialog.detail.registrationData).age || '未填写' }}
            </el-descriptions-item>
            <el-descriptions-item label="紧急联系人" label-width="150px">
              {{ parseRegistrationData(registrationDetailDialog.detail.registrationData).emergencyContact || '未填写' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 支付信息 -->
        <div class="detail-section">
          <h4 class="section-title">
            <el-icon>
              <Money />
            </el-icon>
            支付信息
          </h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="支付状态" label-width="150px">
              <el-tag :type="getPaymentStatusType(registrationDetailDialog.detail.paymentStatus)">
                {{ getPaymentStatusText(registrationDetailDialog.detail.paymentStatus) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="支付金额" label-width="150px">
              <span class="fee-text">￥{{ registrationDetailDialog.detail.paymentAmount || 0 }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="支付方式" label-width="150px">
              {{ registrationDetailDialog.detail.paymentMethod || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="支付时间" label-width="150px">
              <span v-if="registrationDetailDialog.detail.paymentTime">
                {{ parseTime(registrationDetailDialog.detail.paymentTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
              </span>
              <span v-else class="text-muted">未支付</span>
            </el-descriptions-item>
            <el-descriptions-item label="交易号" :span="2" label-width="150px">
              {{ registrationDetailDialog.detail.paymentTransactionId || '无' }}
            </el-descriptions-item>
          </el-descriptions>

          <!-- 支付凭证展示 -->
          <div v-if="registrationDetailDialog.detail.paymentProofList" class="payment-proof-section">
            <h5 class="proof-title">
              <el-icon>
                <Picture />
              </el-icon>
              支付凭证
            </h5>
            <el-descriptions :column="2" border>
              <el-descriptions-item :span="2" label="支付凭证" label-width="150px">
                <div class="proof-images">
                  <div v-for="image in registrationDetailDialog.detail.paymentProofList" :key="image.id"
                    class="proof-image-item">
                    <el-image style="width:50px" :src="image.url"
                      :preview-src-list="registrationDetailDialog.detail.paymentProofList.map(img => img.url)"
                      fit="cover" class="proof-image" />
                    <div class="image-name">{{ image.name }}</div>
                  </div>
                </div>
              </el-descriptions-item>
              <el-descriptions-item :span="2" label="备注" label-width="150px">
                <!-- 操作备注展示 -->
                <div v-if="parsePaymentInfo(registrationDetailDialog.detail.remark).pureRemark"
                  class="payment-remark-section">
                  <div class="remark-content">
                    {{ parsePaymentInfo(registrationDetailDialog.detail.remark).pureRemark }}
                  </div>
                </div>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>



        <!-- 参赛信息 -->
        <div class="detail-section">
          <h4 class="section-title">
            <el-icon>
              <Trophy />
            </el-icon>
            参赛信息
          </h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="签到状态" label-width="150px">
              <el-tag :type="registrationDetailDialog.detail.checkInStatus === 1 ? 'success' : 'info'">
                {{ registrationDetailDialog.detail.checkInStatus === 1 ? '已签到' : '未签到' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="签到时间" label-width="150px">
              <span v-if="registrationDetailDialog.detail.checkInTime">
                {{ parseTime(registrationDetailDialog.detail.checkInTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
              </span>
              <span v-else class="text-muted">未签到</span>
            </el-descriptions-item>
            <el-descriptions-item label="完赛状态" label-width="150px">
              <el-tag :type="registrationDetailDialog.detail.completionStatus === 1 ? 'success' : 'info'">
                {{ registrationDetailDialog.detail.completionStatus === 1 ? '已完赛' : '未完赛' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="完赛时间" label-width="150px">
              <span v-if="registrationDetailDialog.detail.completionTime">
                {{ parseTime(registrationDetailDialog.detail.completionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
              </span>
              <span v-else class="text-muted">未完赛</span>
            </el-descriptions-item>
            <el-descriptions-item label="奖状/证书" label-width="150px">
              <div class="proof-images">
                <div class="proof-image-item">
                  <el-image style="width:50px;" v-if="registrationDetailDialog.detail.certificateAwardUrl"
                    :src="registrationDetailDialog.detail.certificateAwardUrl"
                    :preview-src-list="[registrationDetailDialog.detail.certificateAwardUrl]" fit="cover"
                    class="proof-image" />
                </div>
              </div>

              <!-- <el-button v-if="registrationDetailDialog.detail.certificateAwardUrl" type="text"
                size="small">下载</el-button> -->
              <!-- <div v-else> -- </div> -->
            </el-descriptions-item>
            <el-descriptions-item label="证书/奖状上链HASH" label-width="150px">
              <span v-if="registrationDetailDialog.detail.fileChainHash">
                {{
                  registrationDetailDialog.detail.fileChainHash
                }}
              </span>
              <span v-else class="text-muted">--</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>


      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="registrationDetailDialog.visible = false">关 闭</el-button>
          <!-- <el-button type="warning" @click="handlePaymentManage(registrationDetailDialog.detail)">
            <el-icon>
              <Money />
            </el-icon>
            支付管理
          </el-button> -->
        </div>
      </template>
    </el-dialog>

    <!-- 支付管理对话框 -->
    <el-dialog :title="paymentManageDialog.title" v-model="paymentManageDialog.visible" width="650px" append-to-body
      class="payment-manage-dialog">
      <div class="payment-manage-content">
        <div class="current-status">
          <p>当前支付状态：
            <el-tag :type="getPaymentStatusType(paymentManageDialog.currentStatus)" size="large">
              {{ getPaymentStatusText(paymentManageDialog.currentStatus) }}
            </el-tag>
          </p>
        </div>

        <!-- 显示历史支付凭证和备注 -->
        <div v-if="paymentManageDialog.currentRecord.remark" class="history-payment-info">
          <h4>
            <el-icon>
              <Clock />
            </el-icon>
            历史操作记录
          </h4>

          <!-- 历史支付凭证 -->
          <div v-if="paymentManageDialog.currentRecord.paymentProofList" class="history-proof-section">
            <h5 class="proof-title">
              <el-icon>
                <Picture />
              </el-icon>
              已上传的支付凭证
            </h5>
            <div class="proof-images">
              <div v-for="image in paymentManageDialog.currentRecord.paymentProofList" :key="image.id"
                class="proof-image-item">
                <el-image :src="image.url"
                  :preview-src-list="paymentManageDialog.currentRecord.paymentProofList.map(img => img.url)" fit="cover"
                  class="proof-image" />
                <div class="image-name">{{ image.name }}</div>
              </div>
            </div>
          </div>

          <!-- 历史备注 -->
          <div v-if="parsePaymentInfo(paymentManageDialog.currentRecord.remark).pureRemark"
            class="history-remark-section">
            <h5 class="remark-title">
              <el-icon>
                <ChatDotRound />
              </el-icon>
              历史操作备注
            </h5>
            <div class="remark-content">
              {{ parsePaymentInfo(paymentManageDialog.currentRecord.remark).pureRemark }}
            </div>
          </div>
        </div>

        <el-divider />

        <!-- 确认支付区域 -->
        <div v-if="paymentManageDialog.currentStatus !== 1" class="payment-confirm-section">
          <h4>确认支付</h4>
          <div class="payment-form">
            <div class="form-item">
              <label>支付凭证截图：</label>
              <div class="upload-area">
                <ImageUpload v-model="paymentManageDialog.paymentProof" :limit="3" :file-size="5"
                  :file-type="['jpg', 'jpeg', 'png']" :is-show-tip="true" />
              </div>
              <div class="upload-tip">
                <el-icon>
                  <InfoFilled />
                </el-icon>
                <span>请上传支付凭证截图，支持jpg/png格式，最多3张，每张不超过5MB</span>
              </div>
            </div>

            <div class="form-item">
              <label>操作备注：</label>
              <el-input v-model="paymentManageDialog.remark" type="textarea" :rows="3"
                placeholder="请输入操作备注（可选），如转账方式、核实情况等" maxlength="200" show-word-limit />
            </div>

            <div class="confirm-button">
              <el-button type="success" size="large" @click="handleConfirmPayment">
                <el-icon>
                  <Check />
                </el-icon>
                确认已支付
              </el-button>
            </div>
          </div>
        </div>

        <!-- 其他操作区域 -->
        <div class="other-actions">
          <h4>其他操作</h4>
          <div class="button-group">
            <el-button v-if="paymentManageDialog.currentStatus !== 0" type="warning"
              @click="handleUpdatePaymentStatus(paymentManageDialog.registrationId, 0, '管理员取消支付确认')">
              <el-icon>
                <Close />
              </el-icon>
              标记为未支付
            </el-button>

            <el-button v-if="paymentManageDialog.currentStatus === 1" type="info"
              @click="handleUpdatePaymentStatus(paymentManageDialog.registrationId, 3, '管理员执行退款操作')">
              <el-icon>
                <RefreshLeft />
              </el-icon>
              退款
            </el-button>

            <el-button v-if="paymentManageDialog.currentStatus !== 2" type="danger"
              @click="handleUpdatePaymentStatus(paymentManageDialog.registrationId, 2, '管理员标记支付失败')">
              <el-icon>
                <CloseBold />
              </el-icon>
              标记支付失败
            </el-button>
          </div>
        </div>

        <el-alert title="操作提醒" type="warning" :closable="false" style="margin-top: 20px">
          <template #default>
            <p>请谨慎操作支付状态，上传的支付凭证将保存在系统中作为核实依据</p>
          </template>
        </el-alert>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="paymentManageDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 参赛结果设置对话框 -->
    <el-dialog :title="competitionResultDialog.title" v-model="competitionResultDialog.visible" width="800px"
      append-to-body class="competition-result-dialog">
      <el-form ref="competitionResultFormRef" :model="competitionResultForm" :rules="competitionResultRules"
        label-width="120px" class="competition-result-form">

        <!-- 参赛者信息 -->
        <div class="form-section">
          <h4 class="section-title">
            <el-icon>
              <User />
            </el-icon>
            参赛者信息
          </h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="姓名">
              {{ parseRegistrationData(competitionResultDialog.registrationData).name || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="手机号">
              {{ parseRegistrationData(competitionResultDialog.registrationData).phone || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="项目名称">
              {{ projectsList.find(p => p.id === competitionResultDialog.eventItemId)?.name || '未知项目' }}
            </el-descriptions-item>
            <el-descriptions-item label="报名时间">
              {{ parseTime(competitionResultDialog.createTime, '{y}-{m}-{d} {h}:{i}') }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 完赛状态设置 -->
        <div class="form-section">
          <h4 class="section-title">
            <el-icon>
              <Trophy />
            </el-icon>
            完赛状态设置
          </h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="完赛状态" prop="completionStatus">
                <el-select v-model="competitionResultForm.completionStatus" placeholder="请选择完赛状态" style="width: 100%">
                  <el-option label="未完赛" :value="0"></el-option>
                  <el-option label="已完赛" :value="1"></el-option>
                  <el-option label="弃权" :value="2"></el-option>
                  <el-option label="犯规" :value="3"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="完赛时间" prop="completionTime">
                <el-date-picker v-model="competitionResultForm.completionTime" type="datetime"
                  value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择完赛时间" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 参赛结果 -->
        <div class="form-section">
          <h4 class="section-title">
            <el-icon>
              <Medal />
            </el-icon>
            参赛结果
          </h4>
          <!-- <el-form-item label="比赛成绩" prop="resultData">
            <el-input v-model="competitionResultForm.resultData" type="textarea" :rows="3"
              placeholder="请输入比赛成绩，如：完赛时间、排名、得分等" maxlength="500" show-word-limit />
          </el-form-item> -->

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="排名">
                <el-input-number v-model="competitionResultForm.ranking" :min="1" placeholder="请输入排名"
                  style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="得分">
                <el-input-number v-model="competitionResultForm.score" :min="0" :precision="2" placeholder="请输入得分"
                  style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="获奖情况">
            <el-select v-model="competitionResultForm.awardType" placeholder="请选择获奖情况" style="width: 100%">
              <el-option label="未获奖" :value="0"></el-option>
              <el-option label="一等奖" :value="1"></el-option>
              <el-option label="二等奖" :value="2"></el-option>
              <el-option label="三等奖" :value="3"></el-option>
              <el-option label="优秀奖" :value="4"></el-option>
              <el-option label="参与奖" :value="5"></el-option>
            </el-select>
          </el-form-item>
        </div>

        <!-- 证书/奖状 -->
        <div class="form-section">
          <h4 class="section-title">
            <el-icon>
              <Document />
            </el-icon>
            证书/奖状
          </h4>
          <el-form-item label="证书/奖状" prop="certificateAward">
            <ImageUpload v-model="competitionResultForm.certificateAward" :limit="1" :file-size="10"
              :file-type="['jpg', 'jpeg', 'png', 'pdf']" :is-show-tip="true" />
            <div class="upload-tip">
              <el-icon>
                <InfoFilled />
              </el-icon>
              <span>支持jpg/png/pdf格式，最多1个文件，每个不超过10MB</span>
            </div>
          </el-form-item>
        </div>

        <!-- 备注信息 -->
        <div class="form-section">
          <h4 class="section-title">
            <el-icon>
              <ChatDotRound />
            </el-icon>
            备注信息
          </h4>
          <el-form-item label="备注">
            <el-input v-model="competitionResultForm.remark" type="textarea" :rows="3"
              placeholder="请输入备注信息，如特殊情况说明、裁判意见等" maxlength="300" show-word-limit />
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelCompetitionResult">取 消</el-button>
          <el-button :loading="competitionResultLoading" type="primary" @click="submitCompetitionResult">
            <el-icon>
              <Check />
            </el-icon>
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 客服管理对话框 -->
    <el-dialog :title="customerServiceDialog.title" v-model="customerServiceDialog.visible" width="900px" append-to-body
      class="customer-service-dialog">

      <!-- 当前绑定的客服列表 -->
      <div class="current-services-section" v-if="currentEventCustomerServices.length > 0">
        <div class="section-header">
          <h4 class="section-title">
            <el-icon>
              <User />
            </el-icon>
            <span>当前绑定客服</span>
            <el-badge :value="currentEventCustomerServices.length" type="primary" />
          </h4>
          <div class="primary-info">
            主要负责人：<span
              class="primary-name">{{ currentEventCustomerServices.find(s => s.isPrimary)?.customerServiceName || '未设置' }}</span>
          </div>
        </div>

        <div class="services-list">
          <div v-for="service in currentEventCustomerServices" :key="service.id" class="service-item"
            :class="{ 'is-primary': service.isPrimary }">

            <!-- 左侧：基本信息 -->
            <div class="service-info">
              <div class="info-main">
                <div class="name-line">
                  <el-tag v-if="service.isPrimary" type="danger" size="small">主要负责人</el-tag>
                </div>
              </div>
            </div>

            <!-- 中间：联系方式 -->
            <div class="service-contacts">
              <div class="contact-grid">
                <div class="contact-item" v-if="service.phone">
                  <span class="contact-label">姓名：</span>
                  <span class="contact-value">{{ service.customerServiceName }}</span>
                </div>
                <div class="contact-item" v-if="service.phone">
                  <span class="contact-label">职位：</span>
                  <span class="contact-value"><dict-tag :options="sh_ry_zw" :value="service.position" /></span>
                </div>
                <div class="contact-item" v-if="service.phone">
                  <span class="contact-label">电话：</span>
                  <span class="contact-value">{{ service.phone }}</span>
                </div>
                <div class="contact-item" v-if="service.qq">
                  <span class="contact-label">QQ：</span>
                  <span class="contact-value">{{ service.qq }}</span>
                </div>
                <div class="contact-item" v-if="service.wechat">
                  <span class="contact-label">微信：</span>
                  <span class="contact-value">{{ service.wechat }}</span>
                </div>
                <div class="contact-item" v-if="service.email">
                  <span class="contact-label">邮箱：</span>
                  <span class="contact-value">{{ service.email }}</span>
                </div>
              </div>
            </div>

            <!-- 右侧：操作按钮 -->
            <div class="service-actions" style="margin-top: 10px;">
              <el-button v-if="!service.isPrimary" type="primary" size="small" plain
                @click="handleSetPrimaryService(service)">
                设为主要负责人
              </el-button>
              <el-button type="danger" size="small" plain @click="handleRemoveEventService(service)">
                移除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <el-divider v-if="currentEventCustomerServices.length > 0" />

      <!-- 添加客服区域 -->
      <div class="add-services-section">
        <h4 class="section-title">
          <el-icon>
            <Plus />
          </el-icon>
          添加客服
        </h4>

        <!-- 客服选择 -->
        <div class="service-selection">
          <el-row :gutter="20" align="middle">
            <el-col :span="16">
              <el-select v-model="selectedCustomerServiceIds" multiple filterable placeholder="请选择要添加的客服"
                style="width: 100%" :disabled="availableCustomerServices.length === 0">
                <el-option v-for="service in availableCustomerServices" :key="service.id"
                  :label="`${service.name} (${service.position}) - ${service.phone}`" :value="service.id"
                  :disabled="service.status === 0">
                  <div class="service-option">
                    <div class="service-main">
                      <span class="service-name">{{ service.name }}</span>
                      <dict-tag :options="sh_kf_status" :value="service.status" />
                      <dict-tag :options="sh_ry_zw" :value="service.position" />
                    </div>
                    <div class="service-contact">{{ service.phone }}</div>
                  </div>
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-button type="primary" @click="handleAddEventServices"
                :disabled="selectedCustomerServiceIds.length === 0" :loading="addingServices">
                <el-icon>
                  <Plus />
                </el-icon>
                添加选中客服
              </el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 快速添加常用客服 -->
        <div class="quick-add-section" v-if="frequentServices.length > 0">
          <h5 class="quick-title">常用客服</h5>
          <div class="quick-services">
            <el-tag v-for="service in frequentServices" :key="service.id" type="primary" effect="plain"
              class="quick-service-tag" @click="handleQuickAddService(service)"
              :class="{ 'is-disabled': isServiceAlreadyAdded(service.id) }">
              <el-icon>
                <Plus />
              </el-icon>
              {{ service.name }} ({{ service.position }})
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 提示信息 -->
      <el-alert v-if="availableCustomerServices.length === 0" title="暂无可用客服" type="info" :closable="false"
        style="margin-top: 20px">
        <template #default>
          <p>当前没有可用的客服人员。请先在<router-link to="/system/customerService">客服管理</router-link>中添加客服信息。</p>
        </template>
      </el-alert>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="customerServiceDialog.visible = false">关 闭</el-button>
          <el-button type="primary" @click="refreshCustomerServices">
            <el-icon>
              <Refresh />
            </el-icon>
            刷 新
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 奖状/证书管理主对话框 -->
    <el-dialog :title="certificateManageDialog.title" v-model="certificateManageDialog.visible" width="95%"
      append-to-body class="certificate-manage-dialog">
      <el-tabs v-model="certificateManageDialog.activeTab" type="border-card">
        <!-- 模板管理标签页 -->
        <el-tab-pane label="模板管理" name="template">
          <div class="template-management">
            <!-- 搜索区域 -->
            <div class="mb-4">
              <el-card shadow="hover">
                <el-form :inline="true" :model="templatesQueryParams">
                  <el-form-item label="模板名称">
                    <el-input v-model="templatesQueryParams.templateName" placeholder="请输入模板名称" clearable
                      style="width: 200px" />
                  </el-form-item>
                  <el-form-item label="模板类型">
                    <el-select v-model="templatesQueryParams.templateType" placeholder="请选择模板类型" clearable
                      style="width: 150px">
                      <el-option label="奖状" :value="1" />
                      <el-option label="证书" :value="2" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="启用状态">
                    <el-select v-model="templatesQueryParams.isEnabled" placeholder="请选择状态" clearable
                      style="width: 120px">
                      <el-option label="启用" :value="1" />
                      <el-option label="停用" :value="0" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleTemplateQuery">搜索</el-button>
                    <el-button icon="Refresh" @click="resetTemplateQuery">重置</el-button>
                  </el-form-item>
                </el-form>
              </el-card>
            </div>

            <!-- 操作按钮 -->
            <div class="mb-4">
              <el-button type="primary" icon="Plus" @click="handleAddTemplate">新增模板</el-button>
              <el-button type="success" icon="Upload" @click="handleBatchImportTemplate">批量导入</el-button>
            </div>

            <!-- 模板列表 -->
            <el-table v-loading="templatesLoading" :data="templatesList" border style="width: 100%">
              <el-table-column label="模板信息" min-width="250">
                <template #default="scope">
                  <div class="template-info">
                    <div class="template-name">{{ scope.row.templateName }}</div>
                    <div class="template-type">
                      <el-tag :type="scope.row.templateType === 1 ? 'warning' : 'success'" size="small">
                        {{ scope.row.templateType === 1 ? '奖状' : '证书' }}
                      </el-tag>
                      <el-tag v-if="scope.row.isDefault" type="danger" size="small" style="margin-left: 5px">
                        默认
                      </el-tag>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="模板预览" width="120" align="center">
                <template #default="scope">
                  <el-image v-if="scope.row.templateImageUrl" :src="scope.row.templateImageUrl"
                    :preview-src-list="[scope.row.templateImageUrl]" fit="cover"
                    style="width: 80px; height: 60px; border-radius: 4px;" />
                  <span v-else class="text-muted">暂无</span>
                </template>
              </el-table-column>

              <el-table-column label="启用状态" width="100" align="center">
                <template #default="scope">
                  <el-switch v-model="scope.row.isEnabled" :active-value="1" :inactive-value="0"
                    @change="handleTemplateStatusChange(scope.row)" />
                </template>
              </el-table-column>

              <el-table-column label="排序" width="80" align="center" prop="sortOrder" />

              <el-table-column label="创建时间" width="160" align="center">
                <template #default="scope">
                  {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}
                </template>
              </el-table-column>

              <el-table-column label="操作" align="center" fixed="right">
                <template #default="scope">
                  <el-button-group size="small">
                    <el-tooltip content="查看" placement="top">
                      <el-button link type="primary" icon="View" @click="handleViewTemplate(scope.row)" />
                    </el-tooltip>
                    <el-tooltip content="编辑" placement="top">
                      <el-button link type="primary" icon="Edit" @click="handleEditTemplate(scope.row)" />
                    </el-tooltip>
                    <el-tooltip content="参数配置" placement="top">
                      <el-button link type="success" icon="Setting" @click="handleTemplateParams(scope.row)" />
                    </el-tooltip>
                    <!-- <el-tooltip content="设为默认" placement="top" v-if="!scope.row.isDefault">
                      <el-button link type="warning" icon="Star" @click="handleSetDefaultTemplate(scope.row)" />
                    </el-tooltip> -->
                    <!-- <el-tooltip content="复制模板" placement="top">
                      <el-button link type="info" icon="CopyDocument" @click="handleCopyTemplate(scope.row)" />
                    </el-tooltip> -->
                    <el-tooltip content="删除" placement="top">
                      <el-button link type="danger" icon="Delete" @click="handleDeleteTemplate(scope.row)" />
                    </el-tooltip>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="mt-4">
              <pagination v-show="templatesTotal > 0" :total="templatesTotal"
                v-model:page="templatesQueryParams.pageNum" v-model:limit="templatesQueryParams.pageSize"
                @pagination="getTemplatesList" />
            </div>
          </div>
        </el-tab-pane>

        <!-- 证书生成标签页 -->
        <el-tab-pane label="证书生成" name="generate">
          <div class="certificate-generation">
            <!-- 证书配置区域 -->
            <div class="config-section mb-6">
              <el-card shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span class="section-title">
                      <el-icon>
                        <Setting />
                      </el-icon>
                      证书生成配置
                    </span>
                  </div>
                </template>

                <div class="config-content">
                  <el-form :inline="true" label-width="120px">
                    <el-form-item label="选择模板:">
                      <el-select v-model="certificateConfigDialog.templateId" placeholder="请选择证书模板" style="width: 300px"
                        @change="handleTemplateChange">
                        <el-option v-for="template in templatesList.filter(t => t.isEnabled)" :key="template.id"
                          :label="template.templateName" :value="template.id">
                          <div class="template-option">
                            <span>{{ template.templateName }}</span>
                            <el-tag :type="template.templateType === 1 ? 'warning' : 'success'" size="small">
                              {{ template.templateType === 1 ? '奖状' : '证书' }}
                            </el-tag>
                          </div>
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" icon="Edit" @click="handleBatchGenerate"
                        :disabled="!certificateConfigDialog.templateId">
                        批量生成证书
                      </el-button>
                    </el-form-item>
                  </el-form>
                </div>
              </el-card>
            </div>

            <!-- 生成记录区域 -->
            <div class="records-section">
              <el-card shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span class="section-title">
                      <el-icon>
                        <Document />
                      </el-icon>
                      生成记录
                    </span>
                  </div>
                </template>

                <!-- 搜索区域 -->
                <div class="mb-4">
                  <el-form :inline="true" label-width="120px" :model="generationQueryParams">
                    <el-form-item label="参赛者姓名">
                      <el-input v-model="generationQueryParams.participantName" placeholder="请输入姓名" clearable
                        style="width: 180px" />
                    </el-form-item>
                    <el-form-item label="生成状态">
                      <el-select v-model="generationQueryParams.generationStatus" placeholder="请选择状态" clearable
                        style="width: 150px">
                        <el-option label="待生成" :value="0" />
                        <el-option label="生成中" :value="1" />
                        <el-option label="生成成功" :value="2" />
                        <el-option label="生成失败" :value="3" />
                      </el-select>
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" icon="Search" @click="handleGenerationQuery">搜索</el-button>
                      <el-button icon="Refresh" @click="resetGenerationQuery">重置</el-button>
                    </el-form-item>
                  </el-form>
                </div>

                <!-- 生成记录表格 -->
                <el-table v-loading="generationLoading" :data="generationRecords" border style="width: 100%">
                  <el-table-column label="参赛者信息" min-width="200">
                    <template #default="scope">
                      <div class="participant-info">
                        <div class="participant-name">{{ scope.row.participantName }}</div>
                        <div class="participant-phone">{{ scope.row.participantPhone || '未填写' }}</div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="证书/奖状" min-width="200">
                    <template #default="scope">
                      <el-image :src="scope.row.certificateUrlPr" :preview-src-list="[scope.row.certificateUrlPr]" fit="cover" style="width:50px"></el-image>
                    </template>
                  </el-table-column>

                  <el-table-column label="证书模板" width="150">
                    <template #default="scope">
                      <div>{{ getTemplateName(scope.row.templateId) }}</div>
                    </template>
                  </el-table-column>

                  <el-table-column label="生成状态" width="120" align="center">
                    <template #default="scope">
                      <el-tag :type="getGenerationStatusType(scope.row.generationStatus)">
                        {{ getGenerationStatusText(scope.row.generationStatus) }}
                      </el-tag>
                    </template>
                  </el-table-column>

                  <el-table-column label="文件大小" width="100" align="center">
                    <template #default="scope">
                      <span v-if="scope.row.fileSize">
                        {{ formatFileSize(scope.row.fileSize) }}
                      </span>
                      <span v-else class="text-muted">-</span>
                    </template>
                  </el-table-column>

                  <el-table-column label="下载次数" width="100" align="center" prop="downloadCount" />

                  <el-table-column label="生成时间" width="160" align="center">
                    <template #default="scope">
                      {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}
                    </template>
                  </el-table-column>

                  <el-table-column label="操作" width="200" align="center" fixed="right">
                    <template #default="scope">
                      <el-button-group size="small">
                        <!-- <el-tooltip content="预览" placement="top" v-if="scope.row.generationStatus === 2">
                          <el-button link type="primary" icon="View" @click="handlePreviewCertificate(scope.row)" />
                        </el-tooltip> -->
                        <el-tooltip content="下载" placement="top" v-if="scope.row.generationStatus === 1">
                          <el-button link type="success" icon="Download"
                            @click="handleDownloadCertificate(scope.row)" />
                        </el-tooltip>
                        <el-tooltip content="重新生成" placement="top" v-if="scope.row.generationStatus === 3">
                          <el-button link type="warning" icon="RefreshRight"
                            @click="handleRegenerateCertificate(scope.row)" />
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top">
                          <el-button link type="danger" icon="Delete" @click="handleDeleteGeneration(scope.row)" />
                        </el-tooltip>
                      </el-button-group>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="mt-4">
                  <pagination v-show="generationTotal > 0" :total="generationTotal"
                    v-model:page="generationQueryParams.pageNum" v-model:limit="generationQueryParams.pageSize"
                    @pagination="getGenerationRecords" />
                </div>
              </el-card>
            </div>

            <!-- 批量操作区域 -->
            <!-- <div class="batch-actions mt-4"> -->
              <!-- <el-card shadow="hover">
                <template #header>
                  <span class="section-title">
                    <el-icon>
                      <Operation />
                    </el-icon>
                    批量操作
                  </span>
                </template>
                <div class="batch-buttons">
                  <el-button type="success" icon="Download" @click="handleBatchDownload">
                    批量下载所有证书
                  </el-button>
                  <el-button type="warning" icon="RefreshRight" @click="handleBatchRegenerate">
                    重新生成失败的证书
                  </el-button>
                  <el-button type="danger" icon="Delete" @click="handleBatchDelete">
                    批量删除记录
                  </el-button>
                </div>
              </el-card> -->
            <!-- </div> -->
          </div>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="certificateManageDialog.visible = false">关 闭</el-button>
          <el-button type="primary" @click="refreshCertificateData">
            <el-icon>
              <Refresh />
            </el-icon>
            刷 新
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 模板编辑对话框 -->
    <el-dialog :title="templateDialog.title" v-model="templateDialog.visible" width="800px" append-to-body
      class="template-dialog">
      <el-form ref="templateFormRef" :model="templateForm" :rules="templateRules" label-width="120px">
        <div class="form-section">
          <h4 class="section-title">基本信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="模板名称" prop="templateName" required>
                <el-input v-model="templateForm.templateName" placeholder="请输入模板名称" maxlength="100" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模板类型" prop="templateType" required>
                <el-select v-model="templateForm.templateType" placeholder="请选择模板类型" style="width: 100%">
                  <el-option label="奖状" :value="1" />
                  <el-option label="证书" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="排序" prop="sortOrder">
                <el-input-number v-model="templateForm.sortOrder" :min="0" placeholder="数字越小越靠前" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="启用状态" prop="isEnabled">
                <el-switch v-model="templateForm.isEnabled" :active-value="1" :inactive-value="0" active-text="启用"
                  inactive-text="停用" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="设为默认" prop="isDefault">
            <el-switch v-model="templateForm.isDefault" :active-value="1" :inactive-value="0" active-text="是"
              inactive-text="否" />
            <div class="form-tip">每个事项只能有一个默认模板</div>
          </el-form-item>

          <el-form-item label="模板图片" prop="templateImage" required>
            <ImageUpload v-model="templateForm.templateImage" :limit="1" :file-size="10"
              :file-type="['jpg', 'jpeg', 'png']" />
            <div class="form-tip">建议尺寸：800x600像素，支持jpg/png格式，不超过10MB</div>
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input v-model="templateForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" maxlength="500"
              show-word-limit />
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelTemplateForm">取 消</el-button>
          <el-button :loading="templatesLoading" type="primary" @click="submitTemplateForm">
            <el-icon>
              <Check />
            </el-icon>
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 模板参数配置对话框 -->
    <el-dialog :title="templateParamsDialog.title" v-model="templateParamsDialog.visible" width="1000px" append-to-body
      class="template-params-dialog">
      <div class="params-config">
        <div class="toolbar mb-4">
          <el-button type="primary" icon="Plus" @click="handleAddParam">添加参数</el-button>
          <el-button type="success" icon="View" @click="handlePreviewTemplate">预览效果</el-button>
        </div>

        <el-table :data="templateParams" border style="width: 100%">
          <el-table-column label="参数键" width="150" prop="paramKey" />
          <el-table-column label="参数标签" width="150" prop="paramLabel" />
          <el-table-column label="参数类型" width="120">
            <template #default="scope">
              <el-tag :type="getParamTypeTag(scope.row.paramType)">
                {{ getParamTypeText(scope.row.paramType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="默认值" width="150" prop="defaultValue" />
          <el-table-column label="位置" width="120">
            <template #default="scope">
              ({{ scope.row.positionX }}, {{ scope.row.positionY }})
            </template>
          </el-table-column>
          <el-table-column label="字体" width="100">
            <template #default="scope">
              {{ scope.row.fontSize }}px
            </template>
          </el-table-column>
          <el-table-column label="必填" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.isRequired ? 'danger' : 'info'" size="small">
                {{ scope.row.isRequired ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="排序" width="80" prop="sortOrder" />
          <el-table-column label="操作" width="150" align="center" fixed="right">
            <template #default="scope">
              <el-button size="small" type="primary" @click="handleEditParam(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="handleDeleteParam(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="templateParamsDialog.visible = false">关 闭</el-button>
          <el-button type="primary" @click="saveTemplateParams">
            <el-icon>
              <Check />
            </el-icon>
            保存配置
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 参数编辑对话框 -->
    <el-dialog :title="paramEditDialog.title" v-model="paramEditDialog.visible" width="800px" append-to-body
      class="param-edit-dialog">
      <el-form ref="paramFormRef" :model="paramForm" :rules="paramRules" label-width="120px">
        <div class="form-section">
          <h4 class="section-title">基本信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="参数键" prop="paramKey" required>
                <el-input v-model="paramForm.paramKey" placeholder="如：studentName" maxlength="50"
                  :disabled="paramEditDialog.isEdit" />
                <div class="form-tip">参数键用于模板中的变量替换，建议使用驼峰命名</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="参数标签" prop="paramLabel" required>
                <el-input v-model="paramForm.paramLabel" placeholder="如：学员姓名" maxlength="50" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="参数类型" prop="paramType" required>
                <el-select v-model="paramForm.paramType" placeholder="请选择参数类型" style="width: 100%">
                  <el-option label="文本" value="text" />
                  <el-option label="日期" value="date" />
                  <el-option label="数字" value="number" />
                  <el-option label="选择" value="select" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否必填" prop="isRequired">
                <el-switch v-model="paramForm.isRequired" :active-value="1" :inactive-value="0" active-text="必填"
                  inactive-text="选填" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="默认值" prop="defaultValue">
            <el-input v-model="paramForm.defaultValue" placeholder="请输入默认值" maxlength="100" />
          </el-form-item>

          <el-form-item label="选项配置" prop="paramOptions" v-if="paramForm.paramType === 'select'">
            <el-input v-model="paramForm.paramOptions" type="textarea" :rows="3"
              placeholder="请输入选项配置，格式：[{&quot;label&quot;:&quot;选项1&quot;,&quot;value&quot;:&quot;value1&quot;},{&quot;label&quot;:&quot;选项2&quot;,&quot;value&quot;:&quot;value2&quot;}]" />
            <div class="form-tip">JSON格式配置选择项，包含label和value字段</div>
          </el-form-item>
        </div>

        <div class="form-section">
          <h4 class="section-title">位置与样式</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="X坐标" prop="positionX" required>
                <el-input-number v-model="paramForm.positionX" :min="0" :max="2000" placeholder="X坐标"
                  style="width: 100%" />
                <div class="form-tip">距离左边的像素距离</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="Y坐标" prop="positionY" required>
                <el-input-number v-model="paramForm.positionY" :min="0" :max="2000" placeholder="Y坐标"
                  style="width: 100%" />
                <div class="form-tip">距离顶部的像素距离</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="字体大小" prop="fontSize" required>
                <el-input-number v-model="paramForm.fontSize" :min="8" :max="100" placeholder="字体大小"
                  style="width: 100%" />
                <div class="form-tip">字体大小(px)</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="字体颜色" prop="fontColor">
                <el-color-picker v-model="paramForm.fontColor" show-alpha />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="字体" prop="fontFamily">
                <el-select v-model="paramForm.fontFamily" placeholder="请选择字体" style="width: 100%">
                  <el-option label="宋体" value="SimSun" />
                  <el-option label="黑体" value="SimHei" />
                  <el-option label="微软雅黑" value="Microsoft YaHei" />
                  <el-option label="楷体" value="KaiTi" />
                  <el-option label="仿宋" value="FangSong" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="排序" prop="sortOrder">
                <el-input-number v-model="paramForm.sortOrder" :min="0" placeholder="数字越小越靠前" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-section">
          <h4 class="section-title">备注信息</h4>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="paramForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" maxlength="500"
              show-word-limit />
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelParamForm">取 消</el-button>
          <el-button type="primary" @click="submitParamForm">
            <el-icon>
              <Check />
            </el-icon>
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量生成证书对话框 -->
    <el-dialog :title="batchGenerateDialog.title" v-model="batchGenerateDialog.visible" width="1200px" append-to-body
      class="batch-generate-dialog">
      <div class="batch-generate-content">
        <!-- 选择报名人员 -->


        <div class="selection-controls mb-4">
          <el-button type="primary" @click="selectAllRegistrations">全选</el-button>
          <el-button @click="clearAllSelections">清空选择</el-button>
          <span class="selection-info">
            已选择 {{ selectedRegistrations.length }} 人
            <el-tag v-if="excludedRegistrations.length > 0" type="warning" style="margin-left: 10px">
              排除 {{ excludedRegistrations.length }} 人
            </el-tag>
          </span>
        </div>

        <!-- 报名人员列表 -->
        <el-table :data="registrationsList" border style="width: 100%" @selection-change="handleRegistrationSelection">
          <el-table-column type="selection" width="55" />
          <el-table-column label="报名信息" prop="registrationData">
            <template #default="scope">
              <div class="participant-info-enhanced">
                <div class="registration-details">
                  <RegistrationDataViewer :formFieldsInfo="getFormFieldsInfo(scope.row.registrationData)"
                    :formData="getFormData(scope.row.registrationData)"
                    :compact="!detailedViewIds.has(scope.row.id?.toString() || '')"
                    :maxFields="detailedViewIds.has(scope.row.id?.toString() || '') ? 20 : 4"
                    :showEmptyFields="detailedViewIds.has(scope.row.id?.toString() || '')" />
                </div>
                <div class="view-toggle">
                  <el-button link type="primary" size="small"
                    @click="toggleDetailedView(scope.row.id?.toString() || '')"
                    :icon="detailedViewIds.has(scope.row.id?.toString() || '') ? 'Fold' : 'Expand'">
                    {{ detailedViewIds.has(scope.row.id?.toString() || '') ? '收起' : '详情' }}
                  </el-button>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="报名时间" width="160">
            <template #default="scope">
              {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}
            </template>
          </el-table-column>
          <el-table-column label="支付状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)">
                {{ getPaymentStatusText(scope.row.paymentStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="已生成证书" width="120" align="center">
            <template #default="scope">
              <el-tag :type="hasGeneratedCertificate(scope.row.id) ? 'success' : 'info'">
                {{ hasGeneratedCertificate(scope.row.id) ? '已生成' : '未生成' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <el-button v-if="excludedRegistrations.includes(scope.row.id)" size="small" type="success"
                @click="includeRegistration(scope.row.id)">
                取消排除
              </el-button>
              <el-button v-else size="small" type="warning" @click="excludeRegistration(scope.row.id)">
                排除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 生成参数配置 -->
        <!-- <el-card shadow="hover"> -->
        <!-- <template #header>
            <span class="section-title">
              <el-icon>
                <Setting />
              </el-icon>
              生成参数配置
            </span>
          </template> -->

        <!-- <div class="generation-params">
            <el-alert title="提示" type="info" :closable="false" style="margin-bottom: 20px">
              以下参数将应用于所有选中的证书生成，支持使用变量如 {{name}}、{{event_title}} 等
            </el-alert>

            <el-form :model="generationParams" label-width="120px">
              <el-row :gutter="20" v-for="param in currentTemplateParams" :key="param.id">
                <el-col :span="12">
                  <el-form-item :label="param.paramLabel" :required="param.isRequired">
                    <el-input v-if="param.paramType === 'text'" v-model="generationParams[param.paramKey]"
                      :placeholder="param.defaultValue || '请输入' + param.paramLabel" />
                    <el-date-picker v-else-if="param.paramType === 'date'" v-model="generationParams[param.paramKey]"
                      type="date" value-format="YYYY-MM-DD" :placeholder="'请选择' + param.paramLabel"
                      style="width: 100%" />
                    <el-input-number v-else-if="param.paramType === 'number'" v-model="generationParams[param.paramKey]"
                      :placeholder="'请输入' + param.paramLabel" style="width: 100%" />
                    <el-select v-else-if="param.paramType === 'select'" v-model="generationParams[param.paramKey]"
                      :placeholder="'请选择' + param.paramLabel" style="width: 100%">
                      <el-option v-for="option in getParamOptions(param)" :key="option.value" :label="option.label"
                        :value="option.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <div class="param-info">
                    <span class="param-key">变量: {{ '{' + '{' }} {{ param.paramKey }} {{ '}' + '}' }}</span>
                    <span class="param-position">位置: ({{ param.positionX }}, {{ param.positionY }})</span>
                  </div>
                </el-col>
              </el-row>
            </el-form>
          </div> -->
        <!-- </el-card> -->
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchGenerateDialog.visible = false">取 消</el-button>
          <el-button type="primary" @click="executeBatchGenerate" :loading="batchTasksLoading">
            <el-icon>
              <Gear />
            </el-icon>
            开始生成 ({{ selectedRegistrations.length }}人)
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Events" lang="ts">
  import { h, ref, reactive, toRefs, getCurrentInstance, onMounted, nextTick, computed } from 'vue';
  import type { ComponentInternalInstance } from 'vue';
  import { ElMessageBox, ElMessage } from 'element-plus';
  import type { FormInstance } from 'element-plus';
  import FormBuilder from '@/components/FormBuilder/FormBuilder.vue';
  import {
    listEvents,
    getEvents,
    delEvents,
    addEvents,
    updateEvents,
    updateEventStatus,
    publishEvent,
    getEventStatistics,
    exportEvents
  } from '@/api/event/events';

  // 添加其他必要的API导入 - 这些需要根据实际API文件调整
  const listEventItems = async (params: any) => ({ rows: [], total: 0 });
  const getEventItems = async (id: any) => ({ data: {} });
  const addEventItems = async (data: any) => ({ data: {} });
  const updateEventItems = async (data: any) => ({ data: {} });
  const delEventItems = async (id: any) => ({ data: {} });
  const listRegistrations = async (params: any) => ({ rows: [], total: 0 });
  const getRegistrations = async (id: any) => ({ data: {} });
  const delRegistrations = async (id: any) => ({ data: {} });
  const updatePaymentStatus = async (id: any, status: any, proof: any, remark: any) => ({ data: {} });
  const savaCompetitionResult = async (data: any) => ({ data: {} });
  const listByIds = async (ids: any) => ({ data: [] });
  const getAllCustomerService = async () => ({ data: [] });
  const getEventCustomerServices = async (eventId: any) => ({ data: [] });
  const batchAddEventCustomerService = async (eventId: any, serviceIds: any) => ({ data: {} });
  const delEventCustomerService = async (id: any) => ({ data: {} });
  const setPrimaryCustomerService = async (eventId: any, serviceId: any) => ({ data: {} });
  const getCertificateTemplate = async (id: any) => ({ data: {} });
  const getCertificateTemplateParams = async (id: any) => ({ data: {} });
  const getParamList = async (params: any) => ({ rows: [], total: 0 });
  const delCertificateTemplateParam = async (id: any) => ({ data: {} });
  import { EventsVO, EventsQuery, EventsForm } from '@/api/event/events/types';
  import { createFormConfig } from '@/api/event/form';
  import { getDefaultFormConfig } from '@/components/FormBuilder/config/defaultTemplates';
  import { parseTime } from '@/utils/ruoyi';

  // 导入组件
  import SearchFilter from './components/SearchFilter.vue';
  import OperationToolbar from './components/OperationToolbar.vue';
  import EventTable from './components/EventTable.vue';
  import EventDetailDialog from './components/EventDetailDialog.vue';
  import EventForm from './components/EventForm.vue';
  import QRCodeDialog from './components/QRCodeDialog.vue';
  import ProjectManagement from './components/ProjectManagement.vue';
  import RegistrationManagement from './components/RegistrationManagement.vue';
  import CustomerServiceManagement from './components/CustomerServiceManagement.vue';
  import CertificateManagement from './components/CertificateManagement.vue';

  // 定义类型
  interface PageData<T, U> {
    form: T;
    queryParams: U;
    rules: Record<string, any>;
  }

  // 临时类型定义 - 这些应该从相应的API类型文件中导入
  interface EventItemsVO {
    id: string | number;
    eventId: string | number;
    name: string;
    description: string;
    maxParticipants: number;
    currentParticipants: number;
    additionalFee: number;
    ageLimitMin?: number;
    ageLimitMax?: number;
    genderLimit: number;
    requirements?: string;
    sortOrder: number;
    remark?: string;
  }

  interface EventItemsQuery {
    pageNum: number;
    pageSize: number;
    eventId?: string | number;
    name?: string;
  }

  interface EventItemsForm {
    id?: string | number;
    eventId?: string | number;
    name?: string;
    description?: string;
    maxParticipants?: number;
    currentParticipants?: number;
    additionalFee?: number;
    ageLimitMin?: number;
    ageLimitMax?: number;
    genderLimit?: number;
    requirements?: string;
    sortOrder?: number;
    remark?: string;
  }

  interface RegistrationsVO {
    id: string | number;
    eventId: string | number;
    eventItemId: string | number;
    registrationData: string;
    paymentStatus: number;
    paymentAmount: number;
    paymentMethod?: string;
    paymentTime?: string;
    paymentTransactionId?: string;
    paymentProof?: string;
    paymentProofList?: any[];
    checkInStatus: number;
    checkInTime?: string;
    completionStatus: number;
    completionTime?: string;
    resultData?: string;
    certificateAward?: string;
    certificateAwardUrl?: string;
    fileChainHash?: string;
    createTime: string;
    remark?: string;
  }

  interface RegistrationsQuery {
    pageNum: number;
    pageSize: number;
    eventId?: string | number;
    eventItemId?: string | number;
    paymentStatus?: number;
    registrationData?: string;
  }

  interface CustomerServiceVO {
    id: string | number;
    name: string;
    position: string;
    phone: string;
    qq?: string;
    wechat?: string;
    email?: string;
    status: number;
  }

  interface EventCustomerServiceVO {
    id: string | number;
    eventId: string | number;
    customerServiceId: string | number;
    customerServiceName: string;
    position: string;
    phone: string;
    qq?: string;
    wechat?: string;
    email?: string;
    isPrimary: boolean;
  }

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { event_status } = toRefs<any>(proxy?.useDict('event_status'));

  const eventsList = ref<EventsVO[]>([]);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const buttonLoading = ref(false);

  const queryFormRef = ref<FormInstance>();

  // 组件引用
  const eventFormRef = ref();
  const eventDetailDialogRef = ref();
  const qrCodeDialogRef = ref();
  const projectManagementRef = ref();
  const registrationManagementRef = ref();
  const customerServiceManagementRef = ref();
  const certificateManagementRef = ref();

  // 对话框状态
  const dialog = reactive({
    visible: false,
    title: ''
  });

  // 添加缺失的变量
  const qrCanvas = ref<HTMLCanvasElement>();
  const formBuilderRef = ref();
  const formBuilderDialog = reactive({
    visible: false,
    title: '',
    eventId: null as string | number | null,
    eventTitle: ''
  });

  // 详情对话框
  const detailDialog = reactive({
    visible: false,
    eventDetail: {} as EventsVO
  });

  // 二维码对话框
  const qrDialog = reactive({
    visible: false,
    title: '',
    url: ''
  });


  const initFormData : EventsForm = {
    id: undefined,
    title: undefined,
    description: undefined,
    startTime: undefined,
    endTime: undefined,
    location: undefined,
    poster: undefined,
    posterUrl: undefined,
    rules: undefined,
    rewards: undefined,
    registrationDeadline: undefined,
    fee: undefined,
    status: undefined,
    organizer: undefined,
    contactInfo: undefined,
    maxParticipants: undefined,
    currentParticipants: undefined,
    remark: undefined,
    createTime: undefined,
    updateTime: undefined,
    createBy: undefined,
    updateBy: undefined,
  }
  const data = reactive<PageData<EventsForm, EventsQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      title: undefined,
      description: undefined,
      startTime: undefined,
      endTime: undefined,
      location: undefined,
      poster: undefined,
      rules: undefined,
      rewards: undefined,
      registrationDeadline: undefined,
      fee: undefined,
      status: undefined,
      organizer: undefined,
      contactInfo: undefined,
      maxParticipants: undefined,
      currentParticipants: undefined,
      params: {
      }
    },
    rules: {
      title: [
        { required: true, message: "标题不能为空", trigger: "blur" }
      ],
      description: [
        { required: true, message: "描述不能为空", trigger: "blur" }
      ],
      poster: [
        { required: true, message: "请上传海报图片", trigger: "blur" }
      ],

      organizer: [
        { required: true, message: "主办方不能为空", trigger: "blur" }
      ],
      contactInfo: [
        { required: true, message: "联系方式不能为空", trigger: "blur" }
      ],
      maxParticipants: [
        { required: true, message: "最大参与人数限制不能为空", trigger: "blur" }
      ]
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询列表列表 */
  const getList = async () => {
    loading.value = true;
    const res = await listEvents(queryParams.value);
    eventsList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  }

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  }

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    // 表单重置由EventForm组件内部处理
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  }

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : EventsVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  const handleAdd = () => {
    eventFormRef.value?.open({ title: '添加信息' });
  }

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: EventsVO) => {
    if (row) {
      eventFormRef.value?.open({ title: '修改信息', data: row });
    } else {
      const _id = ids.value[0];
      const res = await getEvents(_id);
      eventFormRef.value?.open({ title: '修改信息', data: res.data });
    }
  }

  /** 提交按钮 - 现在由EventForm组件处理 */
  const submitForm = () => {
    // 这个方法现在由EventForm组件内部处理
    // 保留这里是为了兼容性，实际逻辑已移到EventForm组件中
  }

  /** 删除按钮操作 */
  const handleDelete = async (row ?: EventsVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除列表编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
    await delEvents(_ids);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('event/events/export', {
      ...queryParams.value
    }, `列表_${new Date().getTime()}.xlsx`)
  }

  /** 批量操作 */
  const handleBatchOperation = async (command : string) => {
    if (ids.value.length === 0) {
      proxy?.$modal.msgWarning('请先选择要操作的');
      return;
    }

    const operations = {
      enable: '启用',
      disable: '禁用',
      close: '关闭报名',
      delete: '删除'
    };

    const operation = operations[command];
    await proxy?.$modal.confirm(`确认要批量${operation}选中的${ids.value.length}个吗？`);

    try {
      loading.value = true;
      // 根据不同操作调用相应API
      switch (command) {
        case 'enable':
          // await batchUpdateEventStatus(ids.value, 'active');
          break;
        case 'disable':
          // await batchUpdateEventStatus(ids.value, 'inactive');
          break;
        case 'close':
          // await batchUpdateEventStatus(ids.value, 'closed');
          break;
        case 'delete':
          await delEvents(ids.value);
          break;
      }
      proxy?.$modal.msgSuccess(`批量${operation}成功`);
      await getList();
    } catch (error) {
      proxy?.$modal.msgError(`批量${operation}失败`);
    } finally {
      loading.value = false;
    }
  }

  // 项目对话框
  const projectDialog = reactive({
    visible: false,
    title: '',
    eventId: null as string | number | null,
    eventTitle: ''
  });

  // 项目列表对话框
  const projectListDialog = reactive({
    visible: false,
    title: '',
    eventId: null as string | number | null,
    eventTitle: ''
  });

  // 报名管理对话框
  const registrationDialog = reactive({
    visible: false,
    title: '',
    eventId: null as string | number | null,
    eventTitle: ''
  });

  // 项目相关数据
  const projectsList = ref<EventItemsVO[]>([]);
  const projectLoading = ref(false);
  const projectQueryParams = ref<EventItemsQuery>({
    pageNum: 1,
    pageSize: 10,
    eventId: undefined,
    name: undefined
  });
  const projectTotal = ref(0);
  const projectFormRef = ref<FormInstance>();
  const projectQueryFormRef = ref<FormInstance>();

  // 项目表单初始数据
  const initProjectFormData : EventItemsForm = {
    id: undefined,
    eventId: undefined,
    name: undefined,
    description: undefined,
    maxParticipants: undefined,
    currentParticipants: 0,
    additionalFee: 0,
    ageLimitMin: undefined,
    ageLimitMax: undefined,
    genderLimit: 0, // 0:不限制 1:男性 2:女性
    requirements: undefined,
    sortOrder: 0,
    remark: undefined
  };

  const projectForm = ref<EventItemsForm>({ ...initProjectFormData });

  // 项目表单验证规则
  const projectRules = {
    name: [
      { required: true, message: "项目名称不能为空", trigger: "blur" }
    ],
    description: [
      { required: true, message: "项目描述不能为空", trigger: "blur" }
    ],
    maxParticipants: [
      { required: true, message: "最大参与人数不能为空", trigger: "blur" }
    ]
  };

  // 报名管理相关数据
  const registrationsList = ref<RegistrationsVO[]>([]);
  const registrationLoading = ref(false);
  const registrationQueryParams = ref<RegistrationsQuery>({
    pageNum: 1,
    pageSize: 10,
    eventId: undefined,
    eventItemId: undefined,
    paymentStatus: undefined,
    registrationData: undefined,
  });
  const registrationTotal = ref(0);
  const registrationQueryFormRef = ref<FormInstance>();

  // 报名详情对话框
  const registrationDetailDialog = reactive({
    visible: false,
    title: '',
    detail: {} as RegistrationsVO
  });

  // 支付管理对话框
  const paymentManageDialog = reactive({
    visible: false,
    title: '',
    registrationId: null as string | number | null,
    currentStatus: 0,
    paymentProof: '', // 支付凭证图片
    remark: '', // 操作备注
    currentRecord: {} as RegistrationsVO // 当前记录的完整信息
  });

  // 参赛结果设置对话框
  const competitionResultDialog = reactive({
    visible: false,
    title: '',
    registrationId: null as string | number | null,
    eventItemId: null as string | number | null,
    registrationData: '', // 报名数据JSON字符串
    createTime: '' // 报名时间
  });

  // 参赛结果表单
  const competitionResultForm = ref({
    id: undefined,
    completionStatus: 0, // 完赛状态
    completionTime: '', // 完赛时间
    resultData: '', // 比赛结果数据
    ranking: undefined, // 排名
    score: undefined, // 得分
    awardType: 0, // 获奖情况
    certificateAward: '', // 证书/奖状文件
    remark: '' // 备注
  });

  // 参赛结果表单验证规则
  const competitionResultRules = {
    completionStatus: [
      { required: true, message: "请选择完赛状态", trigger: "change" }
    ],
    completionTime: [
      { required: true, message: "请选择完赛时间", trigger: "change" }
    ]
  };

  const competitionResultFormRef = ref<FormInstance>();
  const competitionResultLoading = ref(false);

  // 表单字段配置缓存和详情展示状态
  const formFieldsCache = ref<Record<string, any[]>>({});
  const detailedViewIds = ref<Set<string>>(new Set());

  // ========== 客服管理相关数据 ==========
  // 客服管理对话框
  const customerServiceDialog = reactive({
    visible: false,
    title: '',
    eventId: null as string | number | null,
    eventTitle: ''
  });

  // ========== 奖状/证书管理相关数据 ==========
  // 奖状/证书管理主对话框
  const certificateManageDialog = reactive({
    visible: false,
    title: '',
    eventId: null as string | number | null,
    eventTitle: '',
    activeTab: 'template' // template-模板管理, generate-证书生成
  });

  // 模板管理对话框
  const templateDialog = reactive({
    visible: false,
    title: '',
    isEdit: false
  });

  // 证书生成配置对话框
  const certificateConfigDialog = reactive({
    visible: false,
    title: '',
    eventId: null as string | number | null
  });

  // 证书批量生成对话框
  const batchGenerateDialog = reactive({
    visible: false,
    title: '',
    eventId: null as string | number | null,
    templateId: null as string | number | null
  });

  // 模板列表数据
  const templatesList = ref([]);
  const templatesLoading = ref(false);
  const templatesTotal = ref(0);
  const templatesQueryParams = ref({
    pageNum: 1,
    pageSize: 10,
    eventId: undefined,
    templateName: '',
    isEnabled: undefined,
    templateType: undefined
  });

  // 模板表单数据
  const initTemplateForm = {
    id: undefined,
    templateName: '',
    eventId: undefined,
    templateImage: '',
    templateConfig: '{}',
    isEnabled: 1,
    isDefault: 0,
    templateType: 1,
    sortOrder: 0,
    remark: ''
  };
  const templateForm = ref({ ...initTemplateForm });
  const templateFormRef = ref<FormInstance>();

  // 模板参数配置列表
  const templateParams = ref([]);
  const templateParamsDialog = reactive({
    visible: false,
    title: '',
    templateId: null as string | number | null
  });

  // 参数编辑对话框
  const paramEditDialog = reactive({
    visible: false,
    title: '',
    isEdit: false
  });

  // 参数表单初始数据
  const initParamForm = {
    id: undefined,
    templateId: undefined,
    paramKey: '',
    paramLabel: '',
    paramType: 'text',
    paramOptions: '',
    defaultValue: '',
    isRequired: 1,
    positionX: 300,
    positionY: 200,
    fontSize: 24,
    fontColor: '#000000',
    fontFamily: 'SimSun',
    sortOrder: 0,
    remark: ''
  };

  const paramForm = ref({ ...initParamForm });
  const paramFormRef = ref<FormInstance>();

  // 参数表单验证规则
  const paramRules = {
    paramKey: [
      { required: true, message: "参数键不能为空", trigger: "blur" },
      { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: "参数键必须以字母开头，只能包含字母、数字和下划线", trigger: "blur" }
    ],
    paramLabel: [
      { required: true, message: "参数标签不能为空", trigger: "blur" }
    ],
    paramType: [
      { required: true, message: "请选择参数类型", trigger: "change" }
    ],
    positionX: [
      { required: true, message: "X坐标不能为空", trigger: "blur" },
      { type: 'number', min: 0, message: "X坐标必须大于等于0", trigger: "blur" }
    ],
    positionY: [
      { required: true, message: "Y坐标不能为空", trigger: "blur" },
      { type: 'number', min: 0, message: "Y坐标必须大于等于0", trigger: "blur" }
    ],
    fontSize: [
      { required: true, message: "字体大小不能为空", trigger: "blur" },
      { type: 'number', min: 1, max: 100, message: "字体大小必须在1-100之间", trigger: "blur" }
    ]
  };

  // 证书生成记录
  const generationRecords = ref([]);
  const generationLoading = ref(false);
  const generationTotal = ref(0);
  const generationQueryParams = ref({
    pageNum: 1,
    pageSize: 10,
    eventId: undefined,
    templateId: undefined,
    generationStatus: undefined,
    participantName: ''
  });

  // 批量生成任务列表
  const batchTasks = ref([]);
  const batchTasksLoading = ref(false);

  // 选中的报名记录（用于批量生成）
  const selectedRegistrations = ref([]);
  const excludedRegistrations = ref([]);

  // 生成参数数据
  const generationParams = ref({});
  const currentTemplateParams = ref([]);

  // 模板表单验证规则
  const templateRules = {
    templateName: [
      { required: true, message: "模板名称不能为空", trigger: "blur" }
    ],
    templateType: [
      { required: true, message: "请选择模板类型", trigger: "change" }
    ],
    templateImage: [
      { required: true, message: "请上传模板图片", trigger: "blur" }
    ]
  };

  // 客服管理相关数据
  const allCustomerServices = ref<CustomerServiceVO[]>([]);
  const currentEventCustomerServices = ref<EventCustomerServiceVO[]>([]);
  const selectedCustomerServiceIds = ref<Array<string | number>>([]);
  const addingServices = ref(false);

  // 计算属性：可用客服列表（排除已绑定的）
  const availableCustomerServices = computed(() => {
    const boundServiceIds = currentEventCustomerServices.value.map(s => s.customerServiceId);
    return allCustomerServices.value.filter(service =>
      !boundServiceIds.includes(service.id) && service.status === 1
    );
  });

  // 计算属性：常用客服（前3个在职客服）
  const frequentServices = computed(() => {
    return availableCustomerServices.value.slice(0, 3);
  });

  // 获取默认字段配置
  const getDefaultFields = () => [
    { fieldKey: 'name', fieldLabel: '姓名', fieldType: 'text', sortOrder: 1 },
    { fieldKey: 'phone', fieldLabel: '手机号', fieldType: 'phone', sortOrder: 2 },
    { fieldKey: 'idCard', fieldLabel: '身份证号', fieldType: 'text', sortOrder: 3 },
    { fieldKey: 'gender', fieldLabel: '性别', fieldType: 'radio', fieldOptions: { options: [{ label: '男', value: '男' }, { label: '女', value: '女' }] }, sortOrder: 4 },
    { fieldKey: 'age', fieldLabel: '年龄', fieldType: 'number', sortOrder: 5 },
    { fieldKey: 'email', fieldLabel: '邮箱', fieldType: 'email', sortOrder: 6 },
    { fieldKey: 'emergencyContact', fieldLabel: '紧急联系人', fieldType: 'text', sortOrder: 7 },
    { fieldKey: 'emergencyPhone', fieldLabel: '紧急联系电话', fieldType: 'phone', sortOrder: 8 },
    { fieldKey: 'address', fieldLabel: '地址', fieldType: 'textarea', sortOrder: 9 },
    { fieldKey: 'birthday', fieldLabel: '出生日期', fieldType: 'date', sortOrder: 10 }
  ];

  // 获取表单字段配置信息
  const getFormFieldsInfo = (registrationData) => {
    const data = JSON.parse(registrationData);
    return data.formFieldsInfo;
  };
  const getFormData = (registrationData) => {
    if (registrationData) {
      const data = JSON.parse(registrationData);
      return data.formData;
    }
  }
  // 切换详细视图
  const toggleDetailedView = (id : string) => {
    if (detailedViewIds.value.has(id)) {
      detailedViewIds.value.delete(id);
    } else {
      detailedViewIds.value.add(id);
    }
  };

  // 从报名数据中获取参赛者姓名
  const getParticipantName = (registrationData : string) : string => {
    try {
      const data = JSON.parse(registrationData || '{}');
      return data.name || data.userName || data.participantName || '未知';
    } catch {
      return '未知';
    }
  };

  // 从报名数据中获取参赛者手机号
  const getParticipantPhone = (registrationData : string) : string => {
    try {
      const data = JSON.parse(registrationData || '{}');
      return data.phone || data.mobile || data.phoneNumber || '未填写';
    } catch {
      return '未填写';
    }
  };

  /** 查看详情 */
  const handleView = async (row : EventsVO) => {
    try {
      const res = await getEvents(row.id);
      detailDialog.eventDetail = res.data;
      detailDialog.visible = true;
    } catch (error) {
      console.log(error)
      proxy?.$modal.msgError('获取详情失败');
    }
  }

  /** 报名管理 */
  const handleRegistrations = async (row : EventsVO) => {
    registrationManagementRef.value?.open({ eventId: row.id, eventTitle: row.title });
  }

  /** 编辑表单 */
  const handleEditForm = async (row : EventsVO) => {
    formBuilderDialog.eventId = row.id;
    formBuilderDialog.eventTitle = row.title;
    formBuilderDialog.title = `编辑"${row.title}"的报名表单`;
    formBuilderDialog.visible = true;

    // 这里可以加载现有的表单配置
    // await loadFormConfig(row.id);
  }

  /** 表单构建器关闭前确认 */
  const handleFormBuilderClose = (done : () => void) => {
    ElMessageBox.confirm('确认关闭表单编辑器吗？未保存的更改将会丢失。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      done();
    }).catch(() => {
      // 用户取消关闭
    });
  }

  /** 表单保存 */
  const handleFormSave = async (data : any) => {
    try {
      // 调用API保存表单配置
      const formData = {
        id: data.formConfig.id,
        eventId: formBuilderDialog.eventId,
        formName: `${formBuilderDialog.eventTitle} - 报名表单`,
        formDescription: '报名表单',
        formConfig: data.formConfig,
        fields: data.fields
      }

      await createFormConfig(formData)
      proxy?.$modal.msgSuccess('表单配置保存成功')
      formBuilderDialog.visible = false
    } catch (error) {
      console.error('保存表单配置失败:', error)
      proxy?.$modal.msgError('保存表单配置失败')
    }
  }

  /** 表单变化 */
  const handleFormChange = (data : any) => {
    // 表单配置发生变化时的处理
    console.log('表单配置变化:', data);
  }

  /** 生成二维码 */
  const handleGenerateQR = async (row : EventsVO) => {
    qrCodeDialogRef.value?.open({ eventId: row.id, eventTitle: row.title });
  }

  /** 更多操作 */
  const handleMoreActions = async (command : string, row : EventsVO) => {
    switch (command) {
      case 'statistics':
        await handleShowStatistics(row);
        break;
      case 'publish':
        await handlePublishEvent(row, true);
        break;
      case 'unpublish':
        await handlePublishEvent(row, false);
        break;
      case 'status-0':
      case 'status-1':
      case 'status-2':
      case 'status-3':
      case 'status-4':
        await handleUpdateStatus(row, parseInt(command.split('-')[1]));
        break;
      case 'copy':
        await handleCopyEvent(row);
        break;
      case 'export':
        await handleExportRegistrations(row);
        break;
      case 'delete':
        await handleDelete(row);
        break;
      case 'addProject':
        await handleAddProject(row);
        break;
    }
  }

  /** 复制 */
  const handleCopyEvent = async (row : EventsVO) => {
    try {
      const copyData = { ...row };
      delete copyData.id;
      copyData.title = `${row.title}(副本)`;
      copyData.status = 0; // 设置为未开始状态
      copyData.currentParticipants = 0; // 重置当前报名人数

      // 清除一些不应该复制的字段
      delete copyData.createTime;
      delete copyData.updateTime;
      delete copyData.createBy;
      delete copyData.updateBy;

      await addEvents(copyData);
      proxy?.$modal.msgSuccess('复制成功');
      await getList();
    } catch (error) {
      console.error('复制失败:', error);
      proxy?.$modal.msgError('复制失败');
    }
  }

  /** 导出报名表 */
  const handleExportRegistrations = (row : EventsVO) => {
    proxy?.download('event/registrations/export', {
      eventId: row.id
    }, `${row.title}_报名表_${new Date().getTime()}.xlsx`);
  }

  /** 显示统计信息 */
  const handleShowStatistics = async (row : EventsVO) => {
    // try {
    const res = await getEventStatistics(row.id);
    const stats = res.data;

    // 使用alert方法显示HTML内容
    await ElMessageBox.alert(
      h('div', { style: 'text-align: left; line-height: 1.6;' }, [
        h('h4', { style: 'margin: 0 0 15px 0; color: #409eff; font-size: 16px;' }, `${row.title} - 统计信息`),
        h('div', { style: 'background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 10px 0;' }, [
          h('p', { style: 'margin: 8px 0; display: flex; justify-content: space-between;' }, [
            h('span', '总报名人数：'),
            h('strong', { style: 'color: #409eff;' }, `${stats.totalRegistrations || 0}人`)
          ]),
          h('p', { style: 'margin: 8px 0; display: flex; justify-content: space-between;' }, [
            h('span', '已支付人数：'),
            h('strong', { style: 'color: #67c23a;' }, `${stats.paidRegistrations || 0}人`)
          ]),
          h('p', { style: 'margin: 8px 0; display: flex; justify-content: space-between;' }, [
            h('span', '未支付人数：'),
            h('strong', { style: 'color: #e6a23c;' }, `${stats.unpaidRegistrations || 0}人`)
          ]),
          h('p', { style: 'margin: 8px 0; display: flex; justify-content: space-between;' }, [
            h('span', '支付率：'),
            h('strong', { style: 'color: #409eff;' }, `${(stats.paymentRate || 0).toFixed(1)}%`)
          ]),
          h('p', { style: 'margin: 8px 0; display: flex; justify-content: space-between;' }, [
            h('span', '报名费收入：'),
            h('strong', { style: 'color: #f56c6c; font-size: 16px;' }, `￥${(stats.totalRevenue || 0).toFixed(2)}`)
          ])
        ])
      ]),
      '统计信息',
      {
        confirmButtonText: '确定',
        type: 'info',
        center: false,
        customClass: 'statistics-dialog'
      }
    );
    // } catch (error) {
    //   console.error('获取统计信息失败:', error);
    //   proxy?.$modal.msgError('获取统计信息失败');
    // }
  }

  /** 发布/取消发布 */
  const handlePublishEvent = async (row : EventsVO, publish : boolean) => {
    const action = publish ? '发布' : '取消发布';

    await proxy?.$modal.confirm(`确认要${action}"${row.title}"吗？`);

    try {
      await publishEvent(row.id, publish);
      proxy?.$modal.msgSuccess(`${action}成功`);
      await getList();
    } catch (error) {
      proxy?.$modal.msgError(`${action}失败`);
    }
  }

  /** 更新状态 */
  const handleUpdateStatus = async (row : EventsVO, status : number) => {
    const statusMap = {
      0: '未开始',
      1: '报名中',
      2: '进行中',
      3: '已结束',
      4: '已取消'
    };

    const statusText = statusMap[status];

    await proxy?.$modal.confirm(`确认要将"${row.title}"设置为"${statusText}"状态吗？`);

    try {
      await updateEventStatus(row.id, status);
      proxy?.$modal.msgSuccess(`状态更新成功`);
      await getList();
    } catch (error) {
      proxy?.$modal.msgError(`状态更新失败`);
    }
  }

  /** 显示二维码 */
  const showQRCode = (url : string, title : string) => {
    qrDialog.url = url;
    qrDialog.title = `${title} - 报名二维码`;
    qrDialog.visible = true;

    // 延迟生成二维码，确保DOM已渲染
    nextTick(() => {
      generateQRCode(url);
    });
  }

  /** 生成二维码 */
  const generateQRCode = async (text : string) => {
    if (!qrCanvas.value) return;

    try {
      // 尝试使用qrcode库生成二维码
      const QRCode = (await import('qrcode')).default;
      await QRCode.toCanvas(qrCanvas.value, text, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
    } catch (error) {
      // 如果qrcode库不可用，使用简单的替代方案
      console.warn('QRCode library not available, using fallback');
      const canvas = qrCanvas.value;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      // 清空画布
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, 200, 200);

      // 简单的二维码替代方案：显示网格图案
      ctx.fillStyle = 'black';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';

      // 绘制类似二维码的网格
      const cellSize = 8;
      const seed = text.length; // 使用文本长度作为随机种子
      for (let i = 0; i < 200; i += cellSize) {
        for (let j = 0; j < 200; j += cellSize) {
          const x = Math.floor(i / cellSize);
          const y = Math.floor(j / cellSize);
          // 使用简单的伪随机函数
          if ((x * y + seed) % 3 === 0) {
            ctx.fillRect(i, j, cellSize, cellSize);
          }
        }
      }

      // 在中心绘制白色背景和文本
      ctx.fillStyle = 'white';
      ctx.fillRect(50, 85, 100, 30);
      ctx.strokeStyle = 'black';
      ctx.strokeRect(50, 85, 100, 30);
      ctx.fillStyle = 'black';
      ctx.fillText('扫码报名', 100, 105);
    }
  }

  /** 复制链接 */
  const copyUrl = async () => {
    try {
      await navigator.clipboard.writeText(qrDialog.url);
      proxy?.$modal.msgSuccess('链接复制成功');
    } catch (error) {
      // 降级方案
      const input = document.createElement('input');
      input.value = qrDialog.url;
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      proxy?.$modal.msgSuccess('链接复制成功');
    }
  }

  /** 下载二维码 */
  const downloadQR = () => {
    try {
      if (qrCanvas.value) {
        const link = document.createElement('a');
        link.download = `报名二维码_${new Date().getTime()}.png`;
        link.href = qrCanvas.value.toDataURL();
        link.click();
        proxy?.$modal.msgSuccess('二维码下载成功');
      } else {
        proxy?.$modal.msgError('二维码下载失败');
      }
    } catch (error) {
      proxy?.$modal.msgError('二维码下载失败');
    }
  }

  /** 获取报名进度百分比 */
  const getRegistrationProgress = (current : number = 0, max : number = 0) : number => {
    if (!max || max === 0) return 0;
    return Math.min((current / max) * 100, 100);
  }

  /** 获取进度条颜色 */
  const getProgressColor = (current : number = 0, max : number = 0) : string => {
    const percentage = getRegistrationProgress(current, max);
    if (percentage >= 90) return '#f56c6c';
    if (percentage >= 70) return '#e6a23c';
    return '#67c23a';
  }

  /** 获取报名进度标签类型 */
  const getRegistrationProgressType = (current : number = 0, max : number = 0) : string => {
    const percentage = getRegistrationProgress(current, max);
    if (percentage >= 90) return 'danger';
    if (percentage >= 70) return 'warning';
    if (percentage >= 50) return 'primary';
    return 'success';
  }

  /** 获取状态类型 */
  const getStatusType = (status : number) : string => {
    const statusMap = {
      0: 'info',      // 未开始
      1: 'success',   // 报名中
      2: 'warning',   // 进行中
      3: 'info',      // 已结束
      4: 'danger'     // 已取消
    };
    return statusMap[status] || 'info';
  }

  /** 获取状态文本 */
  const getStatusText = (status : number) : string => {
    const statusMap = {
      0: '未开始',
      1: '报名中',
      2: '进行中',
      3: '已结束',
      4: '已取消'
    };
    return statusMap[status] || '未知';
  }

  /** 检查报名截止时间是否已过期 */
  const isDeadlineExpired = (deadline : string) : boolean => {
    if (!deadline) return false;
    return new Date(deadline) < new Date();
  }

  // ========== 项目管理相关方法 ==========

  /** 添加项目 */
  const handleAddProject = async (row : EventsVO) => {
    resetProjectForm();
    projectForm.value.eventId = row.id;
    projectDialog.eventId = row.id;
    projectDialog.eventTitle = row.title;
    projectDialog.title = `为 "${row.title}" 添加项目`;
    projectDialog.visible = true;
  }

  /** 查看项目列表 */
  const handleViewProjects = async (row : EventsVO) => {
    projectManagementRef.value?.open({ eventId: row.id, eventTitle: row.title });
  }

  /** 获取项目列表 */
  const getProjectsList = async () => {
    try {
      projectLoading.value = true;
      const res = await listEventItems(projectQueryParams.value);
      projectsList.value = res.rows;
      projectTotal.value = res.total;
    } catch (error) {
      console.error('获取项目列表失败:', error);
      proxy?.$modal.msgError('获取项目列表失败');
    } finally {
      projectLoading.value = false;
    }
  }

  /** 重置项目表单 */
  const resetProjectForm = () => {
    projectForm.value = { ...initProjectFormData };
    projectFormRef.value?.resetFields();
  }

  /** 取消项目表单 */
  const cancelProjectForm = () => {
    resetProjectForm();
    projectDialog.visible = false;
  }

  /** 提交项目表单 */
  const submitProjectForm = () => {
    projectFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        try {
          projectLoading.value = true;
          if (projectForm.value.id) {
            await updateEventItems(projectForm.value);
            proxy?.$modal.msgSuccess("项目修改成功");
          } else {
            await addEventItems(projectForm.value);
            proxy?.$modal.msgSuccess("项目添加成功");
          }
          projectDialog.visible = false;

          // 如果项目列表对话框也是打开的，刷新列表
          if (projectListDialog.visible) {
            await getProjectsList();
          }
        } catch (error) {
          console.error('保存项目失败:', error);
          proxy?.$modal.msgError('保存项目失败');
        } finally {
          projectLoading.value = false;
        }
      }
    });
  }

  /** 项目搜索 */
  const handleProjectQuery = () => {
    projectQueryParams.value.pageNum = 1;
    getProjectsList();
  }

  /** 重置项目搜索 */
  const resetProjectQuery = () => {
    projectQueryFormRef.value?.resetFields();
    handleProjectQuery();
  }

  /** 编辑项目 */
  const handleEditProject = async (row : EventItemsVO) => {
    try {
      const res = await getEventItems(row.id);
      projectForm.value = res.data;
      projectDialog.title = `编辑项目 "${row.name}"`;
      projectDialog.visible = true;
    } catch (error) {
      console.error('获取项目详情失败:', error);
      proxy?.$modal.msgError('获取项目详情失败');
    }
  }

  /** 删除项目 */
  const handleDeleteProject = async (row : EventItemsVO) => {
    try {
      await proxy?.$modal.confirm(`确认删除项目 "${row.name}" 吗？此操作不可恢复！`);
      await delEventItems(row.id);
      proxy?.$modal.msgSuccess('删除成功');
      await getProjectsList();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除项目失败:', error);
        proxy?.$modal.msgError('删除项目失败');
      }
    }
  }

  /** 项目分页改变 */
  const handleProjectPagination = (page : any) => {
    projectQueryParams.value.pageNum = page.page;
    projectQueryParams.value.pageSize = page.limit;
    getProjectsList();
  }

  /** 获取性别限制文本 */
  const getGenderLimitText = (genderLimit : number) : string => {
    const genderMap = {
      0: '不限制',
      1: '仅男性',
      2: '仅女性'
    };
    return genderMap[genderLimit] || '不限制';
  }

  // ========== 报名管理相关方法 ==========

  /** 获取报名列表 */
  const getRegistrationsList = async () => {
    try {
      registrationLoading.value = true;
      const res = await listRegistrations(registrationQueryParams.value);
      registrationsList.value = res.rows;
      registrationTotal.value = res.total;
    } catch (error) {
      console.error('获取报名列表失败:', error);
      proxy?.$modal.msgError('获取报名列表失败');
    } finally {
      registrationLoading.value = false;
    }
  }

  /** 报名搜索 */
  const handleRegistrationQuery = () => {
    registrationQueryParams.value.pageNum = 1;
    getRegistrationsList();
  }

  /** 重置报名搜索 */
  const resetRegistrationQuery = () => {
    registrationQueryFormRef.value?.resetFields();
    registrationQueryParams.value.eventItemId = undefined;
    registrationQueryParams.value.paymentStatus = undefined;
    handleRegistrationQuery();
  }

  /** 报名分页改变 */
  const handleRegistrationPagination = (page : any) => {
    registrationQueryParams.value.pageNum = page.page;
    registrationQueryParams.value.pageSize = page.limit;
    getRegistrationsList();
  }

  /** 查看报名详情 */
  const handleViewRegistration = async (row : RegistrationsVO) => {
    try {
      const res = await getRegistrations(row.id);
      registrationDetailDialog.detail = res.data;
      registrationDetailDialog.title = `报名详情 - ${row.registrationData ? JSON.parse(row.registrationData).name || '未知' : '未知'}`;
      registrationDetailDialog.visible = true;
      if (registrationDetailDialog.detail.paymentProof) {
        listByIds(registrationDetailDialog.detail.paymentProof).then(res => {
          registrationDetailDialog.detail.paymentProofList = res.data
        })
      }


    } catch (error) {
      console.error('获取报名详情失败:', error);
      proxy?.$modal.msgError('获取报名详情失败');
    }
  }
  /** 设置参赛结果 */
  const handleViewCompetitionResults = async (row : RegistrationsVO) => {
    try {
      // 获取报名详情
      const res = await getRegistrations(row.id);
      const registrationDetail = res.data;

      // 设置对话框数据
      competitionResultDialog.registrationId = row.id;
      competitionResultDialog.eventItemId = row.eventItemId;
      competitionResultDialog.registrationData = row.registrationData;
      competitionResultDialog.createTime = row.createTime;
      competitionResultDialog.title = `设置参赛结果 - ${parseRegistrationData(row.registrationData).name || '未知'}`;

      // 设置表单数据
      competitionResultForm.value.id = row.id;
      competitionResultForm.value.completionStatus = registrationDetail.completionStatus || 0;
      competitionResultForm.value.completionTime = registrationDetail.completionTime || '';
      competitionResultForm.value.resultData = registrationDetail.resultData || '';
      competitionResultForm.value.remark = registrationDetail.remark || '';
      competitionResultForm.value.certificateAward = registrationDetail.certificateAward;

      // 解析结果数据中的排名、得分、获奖情况等
      if (registrationDetail.resultData) {
        try {
          const resultData = JSON.parse(registrationDetail.resultData);
          competitionResultForm.value.ranking = resultData.ranking;
          competitionResultForm.value.score = resultData.score;
          competitionResultForm.value.awardType = resultData.awardType || 0;
        } catch (e) {
          console.warn('解析结果数据失败:', e);
        }
      }

      // 显示对话框
      competitionResultDialog.visible = true;
    } catch (error) {
      console.error('获取报名详情失败:', error);
      proxy?.$modal.msgError('获取报名详情失败');
    }
  }
  /** 支付管理 */
  const handlePaymentManage = (row : RegistrationsVO) => {
    paymentManageDialog.registrationId = row.id;
    paymentManageDialog.currentStatus = row.paymentStatus;
    paymentManageDialog.title = `支付管理 - ${row.registrationData ? JSON.parse(row.registrationData).name || '未知' : '未知'}`;
    paymentManageDialog.paymentProof = ''; // 重置支付凭证
    paymentManageDialog.remark = ''; // 重置备注

    // 保存当前记录的完整信息，用于显示历史支付凭证
    paymentManageDialog.currentRecord = row;
    if (paymentManageDialog.currentRecord.paymentProof) {
      listByIds(paymentManageDialog.currentRecord.paymentProof).then(res => {
        paymentManageDialog.currentRecord.paymentProofList = res.data
      })
    }


    paymentManageDialog.visible = true;
  }
  //删除报名记录
  const handlePaymentDel = async (row : RegistrationsVO) => {
    try {
      await proxy?.$modal.confirm(`确认删除报名记录吗？此操作不可恢复！`);
      await delRegistrations(row.id);
      proxy?.$modal.msgSuccess('删除成功');
      await getRegistrationsList();
    } catch (error) {
      if (error !== 'cancel') {
        proxy?.$modal.msgError('删除失败');
      }
    }
  }

  /** 更新支付状态 */
  const handleUpdatePaymentStatus = async (registrationId : string | number, status : number, remark ?: string, paymentProof ?: string) => {
    try {
      // 构建完整的备注信息，包含支付凭证
      let fullRemark = remark || '';
      /*if (paymentProof && status === 1) {
        fullRemark += (fullRemark ? ' | ' : '') + `支付凭证: ${paymentProof}`;
      }*/

      await updatePaymentStatus(registrationId, status, paymentProof, fullRemark);
      proxy?.$modal.msgSuccess('支付状态更新成功');

      // 重置对话框状态
      paymentManageDialog.visible = false;
      paymentManageDialog.paymentProof = '';
      paymentManageDialog.remark = '';

      await getRegistrationsList();
    } catch (error) {
      console.error('更新支付状态失败:', error);
      proxy?.$modal.msgError('更新支付状态失败');
    }
  }

  /** 确认支付（带支付凭证） */
  const handleConfirmPayment = async () => {
    if (!paymentManageDialog.paymentProof && !paymentManageDialog.remark) {
      proxy?.$modal.msgWarning('请上传支付凭证或填写备注信息');
      return;
    }

    const remark = paymentManageDialog.remark || '管理员确认支付';
    await handleUpdatePaymentStatus(
      paymentManageDialog.registrationId,
      1,
      remark,
      paymentManageDialog.paymentProof
    );
  }

  /** 获取支付状态文本 */
  const getPaymentStatusText = (status : number) : string => {
    const statusMap = {
      0: '未支付',
      1: '已支付',
      2: '支付失败',
      3: '已退款'
    };
    return statusMap[status] || '未知';
  }

  /** 获取支付状态类型 */
  const getPaymentStatusType = (status : number) : string => {
    const typeMap = {
      0: 'warning',
      1: 'success',
      2: 'danger',
      3: 'info'
    };
    return typeMap[status] || 'info';
  }

  /** 解析报名数据 */
  const parseRegistrationData = (data : string) => {
    try {
      return JSON.parse(data || '{}');
    } catch {
      return {};
    }
  }

  /** 解析支付凭证和备注 */
  const parsePaymentInfo = (remark : string) => {
    if (!remark) return { paymentProof: '', pureRemark: '' };

    // 查找支付凭证信息
    const proofMatch = remark.match(/支付凭证:\s*([^|]+)/);
    const paymentProof = proofMatch ? proofMatch[1].trim() : '';

    // 移除支付凭证信息，获取纯备注
    const pureRemark = remark.replace(/\|\s*支付凭证:\s*[^|]*/, '').replace(/支付凭证:\s*[^|]*\s*\|?/, '').trim();

    return { paymentProof, pureRemark };
  }

  /** 获取支付凭证图片列表 */
  const getPaymentProofImages = (paymentProof : string) => {
    console.log("paymentProof", paymentProof);
    if (!paymentProof) return [];

    // 支付凭证可能是逗号分隔的ossId字符串
    //const ossIds = paymentProof.split(',').filter(id => id.trim());

    // 这里需要根据ossId获取图片URL，暂时返回ossId作为占位
    // 在实际项目中，可能需要调用API根据ossId获取完整的图片URL
    /*return ossIds.map(ossId => ({
      id: ossId,
      url: ossId.includes('http') ? ossId : `/api/file/${ossId}`, // 简单的URL构造
      name: `支付凭证_${ossId}`
    }));*/
  }

  // ========== 客服管理相关方法 ==========

  /** 客服管理 */
  const handleCustomerService = async (row : EventsVO) => {
    customerServiceManagementRef.value?.open({ eventId: row.id, eventTitle: row.title });
  }

  /** 加载客服管理数据 */
  const loadCustomerServiceData = async () => {
    try {
      // 同时加载所有客服和当前事项绑定的客服
      const [allServicesRes, eventServicesRes] = await Promise.all([
        getAllCustomerService(),
        getEventCustomerServices(customerServiceDialog.eventId!)
      ]);

      allCustomerServices.value = allServicesRes.data;
      currentEventCustomerServices.value = eventServicesRes.data;
    } catch (error) {
      console.error('加载客服数据失败:', error);
      proxy?.$modal.msgError('加载客服数据失败');
    }
  }

  /** 添加选中的客服 */
  const handleAddEventServices = async () => {
    if (selectedCustomerServiceIds.value.length === 0) {
      proxy?.$modal.msgWarning('请先选择要添加的客服');
      return;
    }

    try {
      addingServices.value = true;
      await batchAddEventCustomerService(customerServiceDialog.eventId!, selectedCustomerServiceIds.value);
      proxy?.$modal.msgSuccess(`成功添加${selectedCustomerServiceIds.value.length}个客服`);

      // 清空选择并刷新数据
      selectedCustomerServiceIds.value = [];
      await loadCustomerServiceData();
    } catch (error) {
      console.error('添加客服失败:', error);
      proxy?.$modal.msgError('添加客服失败');
    } finally {
      addingServices.value = false;
    }
  }

  /** 快速添加单个客服 */
  const handleQuickAddService = async (service : CustomerServiceVO) => {
    if (isServiceAlreadyAdded(service.id)) {
      proxy?.$modal.msgWarning('该客服已绑定');
      return;
    }

    try {
      await batchAddEventCustomerService(customerServiceDialog.eventId!, [service.id]);
      proxy?.$modal.msgSuccess(`成功添加客服：${service.name}`);
      await loadCustomerServiceData();
    } catch (error) {
      console.error('添加客服失败:', error);
      proxy?.$modal.msgError('添加客服失败');
    }
  }

  /** 移除事项客服绑定 */
  const handleRemoveEventService = async (service : EventCustomerServiceVO) => {
    try {
      await proxy?.$modal.confirm(`确认移除客服"${service.customerServiceName}"吗？`);
      await delEventCustomerService(service.id);
      proxy?.$modal.msgSuccess('移除成功');
      await loadCustomerServiceData();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('移除客服失败:', error);
        proxy?.$modal.msgError('移除客服失败');
      }
    }
  }

  /** 设置主要负责人 */
  const handleSetPrimaryService = async (service : EventCustomerServiceVO) => {
    try {
      await setPrimaryCustomerService(customerServiceDialog.eventId!, service.customerServiceId);
      proxy?.$modal.msgSuccess(`已设置"${service.customerServiceName}"为主要负责人`);
      await loadCustomerServiceData();
    } catch (error) {
      console.error('设置主要负责人失败:', error);
      proxy?.$modal.msgError('设置主要负责人失败');
    }
  }

  /** 刷新客服数据 */
  const refreshCustomerServices = async () => {
    await loadCustomerServiceData();
    proxy?.$modal.msgSuccess('刷新成功');
  }

  /** 检查客服是否已添加 */
  const isServiceAlreadyAdded = (serviceId : string | number) : boolean => {
    return currentEventCustomerServices.value.some(s => s.customerServiceId === serviceId);
  }

  /** 获取职位标签类型 */
  const getPositionTagType = (position : string) : string => {
    const typeMap : Record<string, string> = {
      '客服': 'primary',
      '运营': 'success',
      '技术': 'warning'
    };
    return typeMap[position] || 'info';
  }

  /** 复制到剪贴板 */
  const copyToClipboard = async (text : string) => {
    try {
      await navigator.clipboard.writeText(text);
      proxy?.$modal.msgSuccess('已复制到剪贴板');
    } catch (error) {
      // 降级处理：使用旧版API
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        proxy?.$modal.msgSuccess('已复制到剪贴板');
      } catch (err) {
        proxy?.$modal.msgError('复制失败');
      }
      document.body.removeChild(textArea);
    }
  }

  onMounted(() => {
    getList();
  });

  /** 取消参赛结果设置 */
  const cancelCompetitionResult = () => {
    competitionResultForm.value = {
      id: undefined,
      completionStatus: 0,
      completionTime: '',
      resultData: '',
      ranking: undefined,
      score: undefined,
      awardType: 0,
      certificateAward: '',
      remark: ''
    };
    competitionResultFormRef.value?.resetFields();
    competitionResultDialog.visible = false;
  }

  /** 提交参赛结果 */
  const submitCompetitionResult = () => {
    competitionResultFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        try {
          competitionResultLoading.value = true;

          // 构建结果数据
          const resultData = {
            ranking: competitionResultForm.value.ranking,
            score: competitionResultForm.value.score,
            awardType: competitionResultForm.value.awardType,
            completionTime: competitionResultForm.value.completionTime,
            resultText: competitionResultForm.value.resultData
          };

          // 构建更新数据
          const updateData = {
            id: competitionResultForm.value.id,
            completionStatus: competitionResultForm.value.completionStatus,
            completionTime: competitionResultForm.value.completionTime,
            resultData: JSON.stringify(resultData),
            certificateAward: competitionResultForm.value.certificateAward,
            remark: competitionResultForm.value.remark
          };

          // 调用更新API
          await savaCompetitionResult(updateData);

          proxy?.$modal.msgSuccess('参赛结果设置成功');
          competitionResultDialog.visible = false;

          // 刷新报名列表
          await getRegistrationsList();
        } catch (error) {
          console.error('保存参赛结果失败:', error);
          proxy?.$modal.msgError('保存参赛结果失败');
        } finally {
          competitionResultLoading.value = false;
        }
      }
    });
  }

  // ========== 奖状/证书管理相关方法 ==========

  // 添加模板参数
  const handleAddParam = () => {
    console.log(templateParamsDialog.templateId)
    // 重置表单
    paramForm.value = {
      templateId: templateParamsDialog.templateId,
      paramKey: '',
      paramLabel: '',
      paramType: 'text',
      isRequired: 1,
      defaultValue: '',
      paramOptions: '',
      positionX: 100,
      positionY: 100,
      fontSize: 16,
      fontColor: '#000000',
      fontFamily: 'Microsoft YaHei',
      sortOrder: 0,
      remark: ''
    };

    paramEditDialog.title = '添加模板参数';
    paramEditDialog.visible = true;

    // 重置表单验证
    nextTick(() => {
      paramFormRef.value?.resetFields();
    });
  };

  // 编辑模板参数
  const handleEditParam = async (row) => {
    try {
      // 获取参数详情
      const res = await getCertificateTemplateParams(row.id);
      paramForm.value = { ...res.data };

      paramEditDialog.title = '编辑模板参数';
      paramEditDialog.visible = true;

      // 重置表单验证
      nextTick(() => {
        paramFormRef.value?.resetFields();
      });
    } catch (error) {
      console.error('获取参数详情失败:', error);
      ElMessage.error('获取参数详情失败');
    }
  };

  // 删除模板参数
  const handleDeleteParam = async (row) => {
    try {
      await ElMessageBox.confirm('确认删除该模板参数吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });

      await delCertificateTemplateParam(row.id);
      ElMessage.success('删除成功');

      // 重新获取参数列表
      await getCertificateTemplateParamsList(templateParamsDialog.templateId);
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除参数失败:', error);
        ElMessage.error('删除参数失败');
      }
    }
  };

  // 预览模板（含参数）
  const handlePreviewTemplate = async () => {
    try {
      // 获取模板详情
      const templateRes = await getCertificateTemplate(templateParamsDialog.templateId);
      const template = templateRes.data;

      // 获取模板参数
      const paramsRes = await getParamList({ templateId: templateParamsDialog.templateId, pageNum: 1, pageSize: 100 });
      const params = Array.isArray(paramsRes.rows) ? paramsRes.rows : [];
      console.log(params)
      // 创建预览对话框内容
      const previewContent = h('div', { class: 'template-preview-container' }, [
        // 模板图片预览
        h('div', { class: 'template-image-preview', style: { position: 'relative', display: 'inline-block' } }, [
          h('img', {
            src: template.templateImageUrl,
            alt: '模板预览',
            style: { maxWidth: '600px', maxHeight: '400px', border: '1px solid #ddd' }
          }),
          // 参数位置标记
          ...params.map(param => h('div', {
            style: {
              position: 'absolute',
              left: `${param.positionX}px`,
              top: `${param.positionY}px`,
              fontSize: `${param.fontSize}px`,
              color: param.fontColor,
              fontFamily: param.fontFamily,
              background: 'rgba(255, 255, 0, 0.3)',
              padding: '2px 4px',
              border: '1px dashed #ff6600',
              borderRadius: '2px',
              whiteSpace: 'nowrap'
            }
          }, `{{ ${param.paramKey} }}`))
        ]),

        // 参数信息列表
        h('div', { class: 'template-params-info', style: { marginTop: '20px' } }, [
          h('h4', { style: { marginBottom: '10px' } }, '模板参数信息：'),
          h('el-table', {
            data: params,
            size: 'small',
            border: true
          }, {
            default: () => [
              h('el-table-column', { prop: 'paramKey', label: '参数标识', width: 120 }),
              h('el-table-column', { prop: 'paramLabel', label: '参数名称', width: 120 }),
              h('el-table-column', {

                prop: 'paramType', label: '参数类型', width: 80, formatter: (row) => {
                  const typeMap = { text: '文本', date: '日期', number: '数字', select: '选择' };
                  return typeMap[row.paramType] || row.paramType;
                }

              }),
              h('el-table-column', { prop: 'isRequired', label: '是否必填', width: 80, formatter: (row) => row.isRequired ? '是' : '否' }),
              h('el-table-column', { prop: 'defaultValue', label: '默认值', showOverflowTooltip: true }),
              h('el-table-column', { prop: 'positionX', label: 'X坐标', width: 70 }),
              h('el-table-column', { prop: 'positionY', label: 'Y坐标', width: 70 }),
              h('el-table-column', { prop: 'fontSize', label: '字体大小', width: 80 }),
              h('el-table-column', { prop: 'remark', label: '备注', showOverflowTooltip: true })
            ]
          })
        ])
      ]);

      // 显示预览对话框
      ElMessageBox({
        title: `模板预览 - ${template.templateName}`,
        message: previewContent,
        showCancelButton: false,
        confirmButtonText: '关闭',
        customStyle: {
          width: '80%',
          maxWidth: '1000px'
        }
      });

    } catch (error) {
      console.error('预览模板失败:', error);
      ElMessage.error('预览模板失败');
    }
  };

  // 获取证书模板参数列表
  const getCertificateTemplateParamsList = async (templateId) => {
    try {
      const res = await getParamList({ templateId, pageNum: 1, pageSize: 100 });
      // 这里可以更新相关的参数列表数据
      templateParams.value = res?.rows || [];
    } catch (error) {
      ElMessage.error('获取模板参数列表失败');
      return [];
    }
  };

  // 取消参数表单
  const cancelParamForm = () => {
    paramEditDialog.visible = false;
    paramFormRef.value?.resetFields();
  };

  // 提交参数表单
  const submitParamForm = async () => {
    try {
      await paramFormRef.value?.validate();
      console.log(templateParamsDialog.templateId)
      console.log(paramForm.value)
      if (paramForm.value.id) {
        await updateCertificateTemplateParams(paramForm.value);
        ElMessage.success('编辑参数成功');
      } else {
        await saveCertificateTemplateParams(templateParamsDialog.templateId, paramForm.value);
        ElMessage.success('添加参数成功');
      }

      paramEditDialog.visible = false;

      // 重新获取参数列表
      await getCertificateTemplateParamsList(templateParamsDialog.templateId);
    } catch (error) {
      console.error('保存参数失败:', error);
      ElMessage.error('保存参数失败');
    }
  };

  /** 奖状/证书管理 */
  const handleCertificateManage = async (row : EventsVO) => {
    certificateManagementRef.value?.open({ eventId: row.id, eventTitle: row.title });
  }

  /** 加载奖状/证书数据 */
  const loadCertificateData = async () => {
    try {
      // 同时加载模板列表和生成记录
      await Promise.all([
        getTemplatesList(),
        getGenerationRecords()
      ]);
    } catch (error) {
      console.error('加载奖状证书数据失败:', error);
      proxy?.$modal.msgError('加载数据失败');
    }
  }

  /** 获取模板列表 */
  const getTemplatesList = async () => {
    try {
      templatesLoading.value = true;
      // TODO: 调用模板列表API
      const res = await listCertificateTemplates(templatesQueryParams.value);
      templatesList.value = res.rows;
      templatesTotal.value = res.total;

    } catch (error) {
      console.error('获取模板列表失败:', error);
      proxy?.$modal.msgError('获取模板列表失败');
    } finally {
      templatesLoading.value = false;
    }
  }

  /** 获取生成记录列表 */
  const getGenerationRecords = async () => {
    try {
      generationLoading.value = true;
      // TODO: 调用生成记录列表API
      const res = await listCertificateGenerations(generationQueryParams.value);
      generationRecords.value = res.rows;
      generationTotal.value = res.total;
    } catch (error) {
      console.error('获取生成记录失败:', error);
      proxy?.$modal.msgError('获取生成记录失败');
    } finally {
      generationLoading.value = false;
    }
  }

  /** 模板搜索 */
  const handleTemplateQuery = () => {
    templatesQueryParams.value.pageNum = 1;
    getTemplatesList();
  }

  /** 重置模板搜索 */
  const resetTemplateQuery = () => {
    templatesQueryParams.value.templateName = '';
    templatesQueryParams.value.templateType = undefined;
    templatesQueryParams.value.isEnabled = undefined;
    handleTemplateQuery();
  }

  /** 新增模板 */
  const handleAddTemplate = () => {
    templateForm.value = { ...initTemplateForm };
    templateForm.value.eventId = certificateManageDialog.eventId;
    templateDialog.title = '新增模板';
    templateDialog.isEdit = false;
    templateDialog.visible = true;
  }

  /** 编辑模板 */
  const handleEditTemplate = async (row : any) => {
    try {
      // TODO: 获取模板详情
      const res = await getCertificateTemplate(row.id);
      templateForm.value = res.data;
      templateDialog.title = `编辑模板 - ${row.templateName}`;
      templateDialog.isEdit = true;
      templateDialog.visible = true;
    } catch (error) {
      console.error('获取模板详情失败:', error);
      proxy?.$modal.msgError('获取模板详情失败');
    }
  }

  /** 查看模板 */
  const handleViewTemplate = async (row : any) => {
    try {
      if (!row.templateImageUrl && !row.templateImage) {
        proxy?.$modal.msgWarning('该模板没有预览图片');
        return;
      }

      // 创建预览对话框
      const imageUrl = row.templateImageUrl || row.templateImage;

      // 使用 ElMessageBox 创建图片预览对话框
      await ElMessageBox.alert(
        h('div', { style: 'text-align: center; padding: 20px;' }, [
          h('h4', { style: 'margin: 0 0 20px 0; color: #409eff; font-size: 16px;' }, `${row.templateName} - 模板预览`),
          h('div', { style: 'margin-bottom: 15px;' }, [
            h('img', {
              src: imageUrl,
              style: 'max-width: 100%; max-height: 500px; border-radius: 8px; box-shadow: 0 2px 12px rgba(0,0,0,0.1);',
              alt: row.templateName,
              onError: (e : Event) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.parentNode?.appendChild(
                  document.createTextNode('图片加载失败')
                );
              }
            })
          ]),
          h('div', { style: 'font-size: 14px; color: #606266; line-height: 1.5;' }, [
            h('p', { style: 'margin: 8px 0;' }, [
              h('strong', '模板类型：'),
              h('span', row.templateType === 1 ? '奖状' : '证书')
            ]),
            h('p', { style: 'margin: 8px 0;' }, [
              h('strong', '状态：'),
              h('span', {
                style: `color: ${row.isEnabled ? '#67c23a' : '#f56c6c'};`
              }, row.isEnabled ? '已启用' : '已停用')
            ]),
            row.isDefault ? h('p', { style: 'margin: 8px 0; color: #e6a23c;' }, [
              h('strong', '默认模板')
            ]) : null,
            row.remark ? h('p', { style: 'margin: 8px 0;' }, [
              h('strong', '备注：'),
              h('span', row.remark)
            ]) : null
          ])
        ]),
        '模板预览',
        {
          confirmButtonText: '关闭',
          type: 'info',
          center: false,
          customClass: 'template-preview-dialog',
          showClose: true,
          closeOnClickModal: true,
          closeOnPressEscape: true
        }
      );
    } catch (error) {
      // 用户点击关闭按钮或按ESC键会触发这里，是正常行为
      if (error !== 'cancel' && error !== 'close') {
        console.error('预览模板失败:', error);
        proxy?.$modal.msgError('预览模板失败');
      }
    }
  }

  /** 模板参数配置 */
  const handleTemplateParams = async (row : any) => {
    templateParamsDialog.templateId = row.id;
    templateParamsDialog.title = `${row.templateName} - 参数配置`;


    try {
      // TODO: 获取模板参数列表
      const res = await getParamList({ templateId: row.id, pageNum: 1, pageSize: 100 });
      if (res.code == 200) {
        templateParams.value = res.rows;
      }
      templateParamsDialog.visible = true;
    } catch (error) {
      console.error('获取模板参数失败:', error);
      proxy?.$modal.msgError('获取模板参数失败');
    }
  }

  /** 设为默认模板 */
  const handleSetDefaultTemplate = async (row : any) => {
    try {
      await proxy?.$modal.confirm(`确认将"${row.templateName}"设为默认模板吗？`);
      // TODO: 调用设为默认模板API
      await setDefaultCertificateTemplate(row.id);
      proxy?.$modal.msgSuccess('设置成功');
      await getTemplatesList();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('设置默认模板失败:', error);
        proxy?.$modal.msgError('设置默认模板失败');
      }
    }
  }

  /** 复制模板 */
  const handleCopyTemplate = async (row : any) => {
    try {
      const copyData = { ...row };
      delete copyData.id;
      copyData.templateName = `${row.templateName}(副本)`;
      copyData.isDefault = 0;

      // TODO: 调用复制模板API
      await addCertificateTemplate(copyData);
      proxy?.$modal.msgSuccess('复制成功');
      await getTemplatesList();
    } catch (error) {
      console.error('复制模板失败:', error);
      proxy?.$modal.msgError('复制模板失败');
    }
  }

  /** 删除模板 */
  const handleDeleteTemplate = async (row : any) => {
    try {
      await proxy?.$modal.confirm(`确认删除模板"${row.templateName}"吗？此操作不可恢复！`);
      // TODO: 调用删除模板API
      await delCertificateTemplate(row.id);
      proxy?.$modal.msgSuccess('删除成功');
      await getTemplatesList();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除模板失败:', error);
        proxy?.$modal.msgError('删除模板失败');
      }
    }
  }

  /** 模板状态改变 */
  const handleTemplateStatusChange = async (row : any) => {
    try {
      // TODO: 调用更新模板状态API
      await updateCertificateTemplateStatus(row.id, row.isEnabled);
      const statusText = row.isEnabled ? '启用' : '停用';
      proxy?.$modal.msgSuccess(`${statusText}成功`);
    } catch (error) {
      // 回滚状态
      row.isEnabled = row.isEnabled ? 0 : 1;
      console.error('更新模板状态失败:', error);
      proxy?.$modal.msgError('更新状态失败');
    }
  }

  /** 取消模板表单 */
  const cancelTemplateForm = () => {
    templateForm.value = { ...initTemplateForm };
    templateFormRef.value?.resetFields();
    templateDialog.visible = false;
  }

  /** 提交模板表单 */
  const submitTemplateForm = () => {
    templateFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        try {
          templatesLoading.value = true;
          if (templateDialog.isEdit) {
            // TODO: 调用更新模板API
            await updateCertificateTemplate(templateForm.value);
            proxy?.$modal.msgSuccess("模板更新成功");
          } else {
            // TODO: 调用新增模板API
            await addCertificateTemplate(templateForm.value);
            proxy?.$modal.msgSuccess("模板创建成功");
          }
          templateDialog.visible = false;
          await getTemplatesList();
        } catch (error) {
          console.error('保存模板失败:', error);
          proxy?.$modal.msgError('保存模板失败');
        } finally {
          templatesLoading.value = false;
        }
      }
    });
  }

  /** 批量生成证书 */
  const handleBatchGenerate = async () => {
    if (!certificateConfigDialog.templateId) {
      proxy?.$modal.msgWarning('请先选择证书模板');
      return;
    }

    batchGenerateDialog.eventId = certificateManageDialog.eventId;
    batchGenerateDialog.templateId = certificateConfigDialog.templateId;
    batchGenerateDialog.title = '批量生成证书';


    // 获取报名列表
    registrationQueryParams.value.eventId = certificateManageDialog.eventId;
    await getRegistrationsList();

    // 获取模板参数
    try {
      const res = await getParamList({ templateId: certificateConfigDialog.templateId, pageNum: 1, pageSize: 100 });
      currentTemplateParams.value = res.rows || [];
      batchGenerateDialog.visible = true;
    } catch (error) {
      console.error('获取模板参数失败:', error);
    }

  }

  /** 执行批量生成 */
  const executeBatchGenerate = async () => {
    if (selectedRegistrations.value.length === 0) {
      proxy?.$modal.msgWarning('请选择要生成证书的人员');
      return;
    }

    try {
      batchTasksLoading.value = true;

      // TODO: 调用批量生成API
      const batchData = {
        eventId: batchGenerateDialog.eventId,
        templateId: batchGenerateDialog.templateId,
        selectedRegistrations: selectedRegistrations.value,
        excludedRegistrations: excludedRegistrations.value,
        generationParams: JSON.stringify(generationParams.value)
      };
      await batchGenerateCertificates(batchData);

      proxy?.$modal.msgSuccess(`成功提交批量生成任务，将为${selectedRegistrations.value.length}人生成证书`);
      batchGenerateDialog.visible = false;

      // 刷新生成记录
      await getGenerationRecords();
    } catch (error) {
      console.error('批量生成失败:', error);
      proxy?.$modal.msgError('批量生成失败');
    } finally {
      batchTasksLoading.value = false;
    }
  }

  /** 生成记录搜索 */
  const handleGenerationQuery = () => {
    generationQueryParams.value.pageNum = 1;
    getGenerationRecords();
  }

  /** 重置生成记录搜索 */
  const resetGenerationQuery = () => {
    generationQueryParams.value.participantName = '';
    generationQueryParams.value.generationStatus = undefined;
    handleGenerationQuery();
  }

  /** 预览证书 */
  const handlePreviewCertificate = (row : any) => {
    // 预览 row.certificateUrlPr
    // TODO: 实现证书预览
    ElMessage.info('证书预览功能开发中');
  }

  /** 下载证书 */
  const handleDownloadCertificate = async (row : any) => {
    try {
      // TODO: 调用下载证书API
      await downloadCertificate(row.id);
      proxy?.$modal.msgSuccess('证书下载中...');
    } catch (error) {
      console.error('下载证书失败:', error);
      proxy?.$modal.msgError('下载证书失败');
    }
  }

  /** 重新生成证书 */
  const handleRegenerateCertificate = async (row : any) => {
    try {
      await proxy?.$modal.confirm(`确认重新生成${row.participantName}的证书吗？`);
      // TODO: 调用重新生成API
      await regenerateCertificate(row.id);
      proxy?.$modal.msgSuccess('重新生成任务已提交');
      await getGenerationRecords();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('重新生成失败:', error);
        proxy?.$modal.msgError('重新生成失败');
      }
    }
  }

  /** 删除生成记录 */
  const handleDeleteGeneration = async (row : any) => {
    try {
      await proxy?.$modal.confirm(`确认删除${row.participantName}的证书生成记录吗？`);
      console.log(111)
      // TODO: 调用删除生成记录API
      await delCertificateGeneration(row.id);
      proxy?.$modal.msgSuccess('删除成功');
      await getGenerationRecords();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除生成记录失败:', error);
        proxy?.$modal.msgError('删除失败');
      }
    }
  }

  /** 批量下载证书 */
  const handleBatchDownload = async () => {
    try {
      // TODO: 调用批量下载API
      await batchDownloadCertificates(certificateManageDialog.eventId);
      proxy?.$modal.msgSuccess('批量下载已开始，请稍候...');
    } catch (error) {
      console.error('批量下载失败:', error);
      proxy?.$modal.msgError('批量下载失败');
    }
  }

  /** 报名人员选择改变 */
  const handleRegistrationSelection = (selection : any[]) => {
    selectedRegistrations.value = selection.map(item => item.id);
  }

  /** 全选报名人员 */
  const selectAllRegistrations = () => {
    selectedRegistrations.value = registrationsList.value.map(item => item.id);
    excludedRegistrations.value = [];
  }

  /** 清空选择 */
  const clearAllSelections = () => {
    selectedRegistrations.value = [];
    excludedRegistrations.value = [];
  }

  /** 排除报名人员 */
  const excludeRegistration = (registrationId : string | number) => {
    if (!excludedRegistrations.value.includes(registrationId)) {
      excludedRegistrations.value.push(registrationId);
    }
    // 从选中列表中移除
    const index = selectedRegistrations.value.indexOf(registrationId);
    if (index > -1) {
      selectedRegistrations.value.splice(index, 1);
    }
  }

  /** 取消排除 */
  const includeRegistration = (registrationId : string | number) => {
    const index = excludedRegistrations.value.indexOf(registrationId);
    if (index > -1) {
      excludedRegistrations.value.splice(index, 1);
    }
  }

  /** 检查是否已生成证书 */
  const hasGeneratedCertificate = (registrationId : string | number) => {
    // TODO: 实现检查逻辑
    return false;
  }

  /** 获取模板名称 */
  const getTemplateName = (templateId : string | number) => {
    const template = templatesList.value.find(t => t.id === templateId);
    return template?.templateName || '未知模板';
  }

  /** 获取生成状态文本 */
  const getGenerationStatusText = (status : number) => {
    const statusMap = {
      0: '待生成',
      2: '生成中',
      1: '生成成功',
      3: '生成失败'
    };
    return statusMap[status] || '未知';
  }

  /** 获取生成状态类型 */
  const getGenerationStatusType = (status : number) => {
    const typeMap = {
      0: 'info',
      2: 'warning',
      1: 'success',
      3: 'danger'
    };
    return typeMap[status] || 'info';
  }

  /** 格式化文件大小 */
  const formatFileSize = (bytes : number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /** 获取参数类型文本 */
  const getParamTypeText = (type : string) => {
    const typeMap = {
      text: '文本',
      date: '日期',
      number: '数字',
      select: '选择'
    };
    return typeMap[type] || '未知';
  }

  /** 获取参数类型标签 */
  const getParamTypeTag = (type : string) => {
    const tagMap = {
      text: '',
      date: 'success',
      number: 'warning',
      select: 'danger'
    };
    return tagMap[type] || 'info';
  }

  /** 获取参数选项 */
  const getParamOptions = (param : any) => {
    try {
      return JSON.parse(param.paramOptions || '[]');
    } catch {
      return [];
    }
  }

  /** 刷新奖状证书数据 */
  const refreshCertificateData = async () => {
    await loadCertificateData();
  }

  /** 模板改变 */
  const handleTemplateChange = async (templateId : string | number) => {
    // 获取选中模板的参数配置
    try {
      // TODO: 获取模板参数
      const res = await getCertificateTemplateParams(templateId);
      currentTemplateParams.value = res.data;
    } catch (error) {
      console.error('获取模板参数失败:', error);
    }
  }

  /** 批量导入模板 */
  const handleBatchImportTemplate = () => {
    ElMessage.info('批量导入功能开发中');
  }

  /** 条件筛选 */
  const selectByConditions = () => {
    ElMessage.info('条件筛选功能开发中');
  }

  /** 批量重新生成 */
  const handleBatchRegenerate = async () => {
    try {
      await proxy?.$modal.confirm('确认重新生成所有失败的证书吗？');
      // TODO: 调用批量重新生成API
      proxy?.$modal.msgSuccess('批量重新生成任务已提交');
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量重新生成失败:', error);
        proxy?.$modal.msgError('批量重新生成失败');
      }
    }
  }

  /** 批量删除记录 */
  const handleBatchDelete = async () => {
    try {
      await proxy?.$modal.confirm('确认批量删除生成记录吗？此操作不可恢复！');
      // TODO: 调用批量删除API
      proxy?.$modal.msgSuccess('批量删除成功');
      await getGenerationRecords();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量删除失败:', error);
        proxy?.$modal.msgError('批量删除失败');
      }
    }
  }

  /** 保存模板参数 */
  const saveTemplateParams = async () => {
    try {
      // TODO: 调用保存参数API
      proxy?.$modal.msgSuccess('参数配置保存成功');
      templateParamsDialog.visible = false;
    } catch (error) {
      console.error('保存参数配置失败:', error);
      proxy?.$modal.msgError('保存参数配置失败');
    }
  }
</script>

<style lang="scss" scoped>
  .event-info {
    text-align: left;

    .event-title {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 4px;
      line-height: 1.4;
    }

    .event-desc {
      color: #7f8c8d;
      font-size: 12px;
      margin-bottom: 8px;
      line-height: 1.3;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .event-meta {
      display: flex;
      align-items: center;
      gap: 8px;

      .fee {
        font-weight: 600;
        color: #e74c3c;
        font-size: 14px;
      }
    }
  }

  .time-info {
    font-size: 12px;
    line-height: 1.4;
    text-align: left;

    div {
      margin-bottom: 2px;

      strong {
        color: #34495e;
      }
    }

    .deadline {
      color: #e67e22;

      &.expired {
        color: #e74c3c;
        font-weight: 600;
      }
    }
  }

  .registration-progress {
    .numbers {
      font-size: 13px;
      font-weight: 600;
      margin-bottom: 4px;
      color: #2c3e50;
    }
  }

  .no-poster {
    color: #bdc3c7;
    font-size: 12px;
  }

  :deep(.el-table) {
    .el-table__row {
      &:hover {
        background-color: #f8f9fa;
      }
    }

    .el-table__cell {
      padding: 12px 0;
    }
  }

  :deep(.el-button-group) {
    .el-button+.el-button {
      margin-left: 0;
    }
  }

  .el-dropdown {
    margin-left: 4px;
  }

  // 对话框样式优化
  :deep(.event-dialog) {
    .el-dialog__body {
      padding: 20px 30px;
      max-height: 70vh;
      overflow-y: auto;
    }

    .el-dialog__header {
      padding: 20px 30px 10px;
      border-bottom: 1px solid #ebeef5;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }
    }

    .el-dialog__footer {
      padding: 15px 30px 20px;
      border-top: 1px solid #ebeef5;
      text-align: right;
    }
  }

  .event-form {
    .form-section {
      margin-bottom: 30px;
      padding: 20px;
      background: #fafafa;
      border-radius: 8px;
      border: 1px solid #e1e6f0;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 20px 0;
        padding-bottom: 10px;
        border-bottom: 2px solid #409eff;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .required-mark {
          font-size: 12px;
          color: #f56c6c;
          font-weight: 400;
        }
      }

      .el-form-item {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .el-form-item__label {
        color: #606266;
        font-weight: 500;
      }
    }

    // 必填字段样式
    .el-form-item.is-required .el-form-item__label::before {
      content: '*';
      color: #f56c6c;
      margin-right: 4px;
    }

    // textarea 样式优化
    .el-textarea__inner {
      font-family: inherit;
      resize: vertical;
    }

    // 日期选择器样式
    .el-date-editor {
      width: 100%;
    }

    // 数字输入框样式
    .el-input-number {
      width: 100%;
    }
  }

  // 底部按钮样式
  .dialog-footer {
    .el-button {
      min-width: 100px;
      margin-left: 10px;

      &:first-child {
        margin-left: 0;
      }
    }

    .el-button--primary {
      background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
      border: none;
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);

      &:hover {
        background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%);
        box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    :deep(.event-dialog) {
      .el-dialog {
        width: 95% !important;
        margin: 20px auto;
      }

      .el-dialog__body {
        padding: 15px 20px;
      }
    }

    .event-form {
      .form-section {
        padding: 15px;
        margin-bottom: 20px;

        .section-title {
          font-size: 14px;
          margin-bottom: 15px;
        }
      }

      .el-row .el-col {
        margin-bottom: 10px;
      }
    }
  }

  // 二维码对话框样式
  :deep(.qr-dialog) {
    .el-dialog__body {
      padding: 30px;
    }

    .qr-content {
      text-align: center;

      .qr-code-container {
        margin-bottom: 20px;
        padding: 20px;
        background: #fff;
        border: 1px solid #e1e6f0;
        border-radius: 8px;
        display: inline-block;

        .qr-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #909399;

          p {
            margin: 10px 0 0 0;
          }
        }

        .qr-content-area {
          .qr-text {
            margin-top: 10px;
            font-size: 12px;
            color: #909399;
            word-break: break-all;
          }
        }
      }

      .qr-info {
        .qr-url {
          margin: 20px 0 10px 0;
          font-weight: 600;
          color: #2c3e50;
        }

        .qr-tip {
          margin: 15px 0 0 0;
          font-size: 12px;
          color: #909399;
        }

        .el-input-group__append {
          .el-button {
            border-left: none;
          }
        }
      }
    }
  }

  // 项目对话框样式
  :deep(.project-dialog) {
    .el-dialog__body {
      padding: 20px 30px;
      max-height: 70vh;
      overflow-y: auto;
    }
  }

  .project-form {
    .form-section {
      margin-bottom: 25px;
      padding: 15px;
      background: #fafafa;
      border-radius: 6px;
      border: 1px solid #e1e6f0;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 14px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 15px 0;
        padding-bottom: 8px;
        border-bottom: 2px solid #409eff;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .required-mark {
          font-size: 12px;
          color: #f56c6c;
          font-weight: 400;
        }
      }

      .el-form-item {
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  // 项目列表对话框样式
  :deep(.project-list-dialog) {
    .el-dialog__body {
      padding: 20px 30px;
      max-height: 80vh;
      overflow-y: auto;
    }
  }

  .project-info {
    text-align: left;

    .project-name {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 4px;
      line-height: 1.4;
    }

    .project-desc {
      color: #7f8c8d;
      font-size: 12px;
      line-height: 1.3;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .participant-info {
    font-size: 12px;

    >div:first-child {
      margin-bottom: 4px;
      font-weight: 600;
      color: #2c3e50;
    }
  }

  .fee-text {
    color: #e74c3c;
    font-weight: 600;
  }

  .text-muted {
    color: #909399;
    font-size: 12px;
  }

  .mb-4 {
    margin-bottom: 16px;
  }

  .mt-4 {
    margin-top: 16px;
  }

  // 统计信息对话框样式
  :deep(.statistics-dialog) {
    .el-message-box {
      width: 450px;
    }

    .el-message-box__content {
      padding: 20px 15px;
    }

    .el-message-box__message {
      margin: 0;
      line-height: 1.6;
    }

    .el-message-box__header {
      .el-message-box__title {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
      }
    }

    .el-message-box__btns {
      .el-button--primary {
        background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
        border: none;
        min-width: 80px;
      }
    }
  }

  // 报名管理对话框样式
  :deep(.registration-dialog) {
    .el-dialog__body {
      padding: 20px 30px;
      max-height: 80vh;
      overflow-y: auto;
    }

    .registration-info {
      text-align: left;

      .name {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 4px;
        font-size: 14px;
      }

      .phone {
        color: #606266;
        font-size: 12px;
        margin-bottom: 2px;
      }

      .email {
        color: #909399;
        font-size: 12px;
      }
    }

    // 增强版参赛者信息样式
    .participant-info-enhanced {
      .participant-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f2f5;

        .header-info {
          flex: 1;
          min-width: 0;

          .participant-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 2px;
            font-size: 14px;
          }

          .participant-contact {
            font-size: 12px;
            color: #409eff;
            font-family: 'Monaco', 'Consolas', monospace;
          }
        }
      }

      .registration-details {
        font-size: 12px;
        margin-bottom: 8px;

        :deep(.registration-data-viewer) {
          .compact-info-section {
            .compact-row {
              gap: 8px;
            }

            .compact-item {
              font-size: 11px;

              strong {
                font-weight: 500;
              }
            }
          }
        }
      }

      .view-toggle {
        text-align: right;
        padding-top: 4px;
        border-top: 1px solid #f0f2f5;

        .el-button {
          font-size: 11px;
          padding: 2px 8px;
        }
      }
    }
  }

  // 报名详情对话框样式
  :deep(.registration-detail-dialog) {
    .el-dialog__body {
      padding: 20px 30px;
      max-height: 80vh;
      overflow-y: auto;
    }

    .detail-section {
      margin-bottom: 25px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 15px 0;
        padding-bottom: 8px;
        border-bottom: 2px solid #409eff;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .remark-content {
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e1e6f0;
        color: #606266;
        line-height: 1.6;
      }
    }
  }

  /* 富文本展示的基础样式（详情使用） */
  .rich-text {
    padding: 10px 12px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    color: #303133;
    line-height: 1.6;
    word-break: break-word;

    :deep(img) {
      max-width: 100%;
      height: auto;
      display: inline-block;
    }

    :deep(p) {
      margin: 0 0 10px 0;
    }

    :deep(ul),
    :deep(ol) {
      padding-left: 1.2em;
      margin: 0 0 10px 0;
    }
  }

  // 支付管理对话框样式
  :deep(.payment-manage-dialog) {
    .el-dialog__body {
      padding: 20px 30px;
    }

    .payment-manage-content {
      .current-status {
        text-align: center;
        margin-bottom: 20px;

        p {
          font-size: 16px;
          margin: 0;
          color: #606266;
        }
      }

      .payment-confirm-section {
        margin-bottom: 30px;

        h4 {
          margin: 0 0 20px 0;
          color: #2c3e50;
          font-size: 16px;
          border-bottom: 2px solid #67c23a;
          padding-bottom: 8px;
        }

        .payment-form {
          .form-item {
            margin-bottom: 20px;

            label {
              display: block;
              margin-bottom: 8px;
              font-weight: 600;
              color: #606266;
              font-size: 14px;
            }

            .upload-area {
              margin-bottom: 10px;
            }

            .upload-tip {
              display: flex;
              align-items: center;
              gap: 6px;
              color: #909399;
              font-size: 12px;
              line-height: 1.4;

              .el-icon {
                color: #409eff;
                font-size: 14px;
              }
            }
          }

          .confirm-button {
            text-align: center;
            margin-top: 25px;

            .el-button {
              padding: 12px 30px;
              font-size: 16px;
            }
          }
        }
      }

      .history-payment-info {
        margin-bottom: 25px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e1e6f0;

        h4 {
          margin: 0 0 15px 0;
          color: #2c3e50;
          font-size: 16px;
          display: flex;
          align-items: center;
          gap: 8px;
          border-bottom: 2px solid #409eff;
          padding-bottom: 8px;
        }

        .history-proof-section,
        .history-remark-section {
          margin-bottom: 15px;

          &:last-child {
            margin-bottom: 0;
          }

          .proof-title,
          .remark-title {
            margin: 0 0 10px 0;
            color: #606266;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
          }

          .proof-images {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;

            .proof-image-item {
              text-align: center;

              .proof-image {
                width: 80px;
                height: 80px;
                border-radius: 6px;
                border: 1px solid #dcdfe6;
                cursor: pointer;
                transition: all 0.3s;

                &:hover {
                  border-color: #409eff;
                  transform: scale(1.05);
                }
              }

              .image-name {
                margin-top: 4px;
                font-size: 12px;
                color: #909399;
                max-width: 80px;
                word-break: break-all;
                line-height: 1.2;
              }
            }
          }

          .remark-content {
            padding: 10px 12px;
            background: #fff;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            color: #606266;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }

      .other-actions {
        h4 {
          margin: 0 0 15px 0;
          color: #2c3e50;
          font-size: 16px;
          border-bottom: 2px solid #909399;
          padding-bottom: 8px;
        }

        .button-group {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;

          .el-button {
            flex: 1;
            min-width: 120px;
            justify-content: center;
            padding: 8px 16px;
          }
        }
      }
    }
  }

  // 报名详情对话框中的支付凭证样式
  :deep(.registration-detail-dialog) {

    .payment-proof-section,
    .payment-remark-section {
      margin-top: 20px;

      .proof-title,
      .remark-title {
        margin: 0 0 12px 0;
        color: #2c3e50;
        font-size: 14px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 6px;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 6px;
      }

      .proof-images {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .proof-image-item {
          text-align: center;

          .proof-image {
            width: 100px;
            height: 100px;
            border-radius: 6px;
            border: 1px solid #dcdfe6;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              border-color: #409eff;
              transform: scale(1.05);
            }
          }

          .image-name {
            margin-top: 6px;
            font-size: 12px;
            color: #909399;
            max-width: 100px;
            word-break: break-all;
            line-height: 1.2;
          }
        }
      }

      .remark-content {
        padding: 12px 15px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e1e6f0;
        color: #606266;
        line-height: 1.6;
      }
    }
  }

  // 参赛结果设置对话框样式
  :deep(.competition-result-dialog) {
    .el-dialog__body {
      padding: 20px 30px;
      max-height: 80vh;
      overflow-y: auto;
    }

    .competition-result-form {
      .form-section {
        margin-bottom: 25px;
        padding: 15px;
        background: #fafafa;
        border-radius: 6px;
        border: 1px solid #e1e6f0;

        &:last-child {
          margin-bottom: 0;
        }

        .section-title {
          font-size: 14px;
          font-weight: 600;
          color: #2c3e50;
          margin: 0 0 15px 0;
          padding-bottom: 8px;
          border-bottom: 2px solid #409eff;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .el-form-item {
          margin-bottom: 15px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .upload-tip {
          display: flex;
          align-items: center;
          gap: 6px;
          color: #909399;
          font-size: 12px;
          line-height: 1.4;
          margin-top: 8px;

          .el-icon {
            color: #409eff;
            font-size: 14px;
          }
        }
      }

      // 参赛者信息展示样式
      .el-descriptions {
        .el-descriptions__label {
          font-weight: 600;
          color: #606266;
        }

        .el-descriptions__content {
          color: #303133;
        }
      }

      // 完赛状态选择器样式
      .el-select {
        width: 100%;
      }

      // 日期时间选择器样式
      .el-date-editor {
        width: 100%;
      }

      // 数字输入框样式
      .el-input-number {
        width: 100%;
      }

      // 文本域样式
      .el-textarea__inner {
        font-family: inherit;
        resize: vertical;
      }
    }

    .el-dialog__footer {
      padding: 15px 30px 20px;
      border-top: 1px solid #ebeef5;
      text-align: right;
    }
  }

  // 表单构建器对话框样式
  :deep(.form-builder-dialog) {
    .el-dialog {
      max-width: 1800px;
      height: 90vh;
      display: flex;
      flex-direction: column;
    }

    .el-dialog__body {
      flex: 1;
      padding: 0;
      overflow: hidden;
    }

    .el-dialog__footer {
      padding: 15px 30px 20px;
      border-top: 1px solid #ebeef5;
      text-align: right;
    }
  }

  // ========== 客服管理对话框样式 ==========
  // :deep(.customer-service-dialog) {
  .el-dialog__body {
    padding: 20px 30px;
    max-height: 80vh;
    overflow-y: auto;
  }

  // 通用标题样式
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  // 当前客服区域
  .current-services-section {
    margin-bottom: 20px;

    .section-header {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 6px;
      margin-bottom: 15px;

      .section-title {
        margin-bottom: 8px;
        border-bottom: none;
        padding-bottom: 0;
      }

      .primary-info {
        font-size: 14px;
        color: #666;

        .primary-name {
          color: #e6a23c;
          font-weight: 500;
        }
      }
    }

    .services-list {
      .service-item {

        display: flex;
        align-items: center;
        padding: 15px;
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        margin-bottom: 10px;
        background: #fff;

        &:last-child {
          margin-bottom: 0;
        }

        &.is-primary {
          border-color: #f56c6c;
          background: #fef2f2;
        }

        // 左侧基本信息 - 固定宽度
        .service-info {
          width: 200px;
          flex-shrink: 0;

          .info-main {
            .name-line {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 6px;

              .service-name {
                font-size: 16px;
                font-weight: 600;
                color: #2c3e50;
              }
            }

            .tags-line {
              display: flex;
              gap: 6px;
              flex-wrap: wrap;
            }
          }
        }

        // 中间联系方式 - 弹性宽度
        .service-contacts {
          flex: 1;
          padding: 0 20px;

          .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px 20px;

            .contact-item {
              display: flex;
              align-items: center;
              gap: 4px;

              .contact-label {
                font-size: 12px;
                color: #909399;
                line-height: 1;
                flex-shrink: 0;
              }

              .contact-value {
                font-size: 14px;
                color: #303133;
                font-weight: 500;
                display: flex;
                align-items: center;
              }
            }
          }
        }

        // 右侧操作按钮 - 固定宽度
        .service-actions {
          width: 180px;
          flex-shrink: 0;
          display: flex;
          gap: 8px;
          justify-content: flex-end;

          .el-button {
            flex: 1;
            max-width: 85px;
            font-size: 12px;
          }
        }
      }
    }
  }

  // 添加客服区域样式
  .add-services-section {
    .service-selection {
      margin-bottom: 20px;

      .service-option {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .service-main {
          display: flex;
          align-items: center;
          gap: 8px;

          .service-name {
            font-weight: 500;
            color: #2c3e50;
          }
        }

        .service-contact {
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .quick-add-section {
      margin-top: 20px;
      padding-top: 15px;
      border-top: 1px solid #e4e7ed;

      .quick-title {
        font-size: 14px;
        font-weight: 600;
        color: #606266;
        margin: 0 0 12px 0;
      }

      .quick-services {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .quick-service-tag {
          cursor: pointer;
          transition: all 0.2s;
          display: flex;
          align-items: center;
          gap: 4px;

          &:hover:not(.is-disabled) {
            background-color: #409eff;
            color: white;
            border-color: #409eff;
          }

          &.is-disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          .el-icon {
            font-size: 12px;
          }
        }
      }
    }
  }

  .el-alert a {
    color: #409eff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  // }

  // 移动端适配
  @media (max-width: 768px) {
    :deep(.customer-service-dialog) {
      .el-dialog {
        width: 95% !important;
        margin: 20px auto;
      }

      .current-services-section {
        .section-header {
          padding: 12px;
        }

        .services-list {
          .service-item {
            border: 1px solid #9c9a9a8a;
            margin: 5px;
            padding: 10px;
            flex-direction: column;
            align-items: stretch;
            padding: 12px;

            .service-info {
              width: 100%;
              margin-bottom: 12px;
            }

            .service-contacts {

              padding: 0;
              margin-bottom: 12px;

              .contact-grid {
                grid-template-columns: 1fr;
                gap: 6px;
              }
            }

            .service-actions {
              width: 100%;
              justify-content: stretch;

              .el-button {
                max-width: none;
              }
            }
          }
        }
      }

      .add-services-section {
        .service-selection {
          .el-row {
            flex-direction: column;
            gap: 12px;

            .el-col {
              width: 100% !important;
            }
          }
        }
      }
    }

    :deep(.form-builder-dialog) {
      .el-dialog {
        width: 100% !important;
        height: 100vh !important;
        margin: 0;
        border-radius: 0;
      }
    }
  }

  // ========== 奖状/证书管理对话框样式 ==========
  :deep(.certificate-manage-dialog) {
    .el-dialog {
      max-width: 1800px;
      height: 90vh;
      display: flex;
      flex-direction: column;
    }

    .el-dialog__body {
      flex: 1;
      padding: 20px;
      overflow: hidden;
    }

    .el-tabs__content {
      height: calc(100vh - 200px);
      overflow-y: auto;
    }

    // 模板管理样式
    .template-management {
      .template-info {
        text-align: left;

        .template-name {
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 4px;
          font-size: 14px;
        }

        .template-type {
          display: flex;
          align-items: center;
          gap: 5px;
        }
      }

      .el-table {
        .template-preview-img {
          border-radius: 4px;
          border: 1px solid #dcdfe6;
        }
      }
    }

    // 证书生成样式
    .certificate-generation {
      .config-section {
        margin-bottom: 20px;

        .config-content {
          .template-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
          }
        }
      }

      .records-section {
        .participant-info {
          text-align: left;

          .participant-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 2px;
          }

          .participant-phone {
            color: #909399;
            font-size: 12px;
          }
        }
      }

      .batch-actions {
        .batch-buttons {
          display: flex;
          gap: 12px;
          flex-wrap: wrap;

          .el-button {
            flex: 1;
            min-width: 150px;
          }
        }
      }
    }

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  // 模板编辑对话框样式
  :deep(.template-dialog) {
    .el-dialog__body {
      padding: 20px 30px;
      max-height: 70vh;
      overflow-y: auto;
    }

    .form-section {
      margin-bottom: 25px;
      padding: 15px;
      background: #fafafa;
      border-radius: 6px;
      border: 1px solid #e1e6f0;

      .section-title {
        font-size: 14px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 15px 0;
        padding-bottom: 8px;
        border-bottom: 2px solid #409eff;
      }

      .form-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 5px;
        line-height: 1.4;
      }
    }
  }

  // 模板参数配置对话框样式
  :deep(.template-params-dialog) {
    .el-dialog__body {
      padding: 20px 30px;
      max-height: 80vh;
      overflow-y: auto;
    }

    .params-config {
      .toolbar {
        display: flex;
        gap: 12px;
        margin-bottom: 20px;
      }

      .el-table {
        .el-tag {
          font-size: 12px;
        }
      }
    }
  }

  // 批量生成对话框样式
  :deep(.batch-generate-dialog) {
    .el-dialog {
      max-width: 1400px;
    }

    .el-dialog__body {
      padding: 20px 30px;
      max-height: 80vh;
      overflow-y: auto;
    }

    .batch-generate-content {
      .selection-controls {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;

        .selection-info {
          margin-left: auto;
          font-size: 14px;
          font-weight: 500;
          color: #606266;
        }
      }

      .generation-params {
        .param-info {
          display: flex;
          flex-direction: column;
          gap: 4px;
          font-size: 12px;
          color: #909399;
          padding: 8px 0;

          .param-key {
            color: #409eff;
            font-family: monospace;
          }

          .param-position {
            color: #e6a23c;
          }
        }
      }
    }
  }

  // 模板预览对话框样式
  :deep(.template-preview-dialog) {
    .el-message-box {
      width: auto;
      max-width: 90vw;
      max-height: 90vh;
    }

    .el-message-box__content {
      padding: 10px 15px;
    }

    .el-message-box__message {
      margin: 0;
      line-height: 1.6;

      img {
        transition: transform 0.3s ease;
        cursor: zoom-in;

        &:hover {
          transform: scale(1.02);
        }
      }
    }

    .el-message-box__header {
      .el-message-box__title {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
      }
    }

    .el-message-box__btns {
      .el-button--primary {
        background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
        border: none;
        min-width: 80px;
      }
    }
  }
</style>
