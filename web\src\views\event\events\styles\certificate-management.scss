// 奖状/证书管理对话框样式
:deep(.certificate-manage-dialog) {
  .el-dialog {
    max-width: 1800px;
    height: 90vh;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body {
    flex: 1;
    padding: 20px;
    overflow: hidden;
  }

  .el-tabs__content {
    height: calc(100vh - 200px);
    overflow-y: auto;
  }

  // 模板管理样式
  .template-management {
    .template-info {
      text-align: left;

      .template-name {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 4px;
        font-size: 14px;
      }

      .template-type {
        display: flex;
        align-items: center;
        gap: 5px;
      }
    }
  }

  // 证书生成样式
  .certificate-generation {
    .config-section {
      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .section-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;
        }
      }

      .config-content {
        padding: 10px 0;

        .template-option {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
        }
      }
    }

    .records-section {
      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .section-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;
        }
      }
    }
  }

  // 通用样式
  .mb-4 {
    margin-bottom: 16px;
  }

  .mb-6 {
    margin-bottom: 24px;
  }

  .mt-4 {
    margin-top: 16px;
  }

  .text-muted {
    color: #909399;
    font-size: 14px;
    text-align: center;
    padding: 40px 0;
  }
}