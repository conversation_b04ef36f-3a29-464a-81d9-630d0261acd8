// 客服管理对话框样式
:deep(.customer-service-dialog) {
  .el-dialog__body {
    padding: 20px;
    max-height: 80vh;
    overflow-y: auto;
  }

  .section-header {
    margin-bottom: 15px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
    }

    .primary-info {
      font-size: 14px;
      color: #606266;

      .primary-name {
        color: #e74c3c;
        font-weight: 600;
      }
    }
  }

  .current-services-section {
    margin-bottom: 20px;

    .services-list {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .service-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e1e6f0;
        transition: all 0.3s;

        &.is-primary {
          background: linear-gradient(135deg, #fef5e7 0%, #fef0e0 100%);
          border-color: #f39c12;
        }

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        .service-info {
          flex: 1;
          min-width: 0;

          .info-main {
            .name-line {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 6px;

              .service-name {
                font-size: 16px;
                font-weight: 600;
                color: #2c3e50;
              }
            }

            .contact-line {
              display: flex;
              gap: 15px;
              margin-bottom: 4px;
              font-size: 14px;
              color: #606266;

              .phone {
                color: #409eff;
                font-family: 'Monaco', 'Consolas', monospace;
              }

              .email {
                color: #909399;
              }
            }

            .time-line {
              font-size: 12px;
              color: #909399;
            }
          }
        }

        .service-actions {
          flex-shrink: 0;
        }
      }
    }
  }

  .available-services-section {
    .search-section {
      margin-bottom: 15px;
    }

    .available-services-list {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      padding: 10px;

      .services-selection {
        .available-service-item {
          margin-bottom: 10px;
          padding: 8px;
          border-radius: 4px;
          transition: background-color 0.3s;

          &:hover {
            background-color: #f5f7fa;
          }

          &:last-child {
            margin-bottom: 0;
          }

          .service-checkbox {
            width: 100%;

            :deep(.el-checkbox__label) {
              width: 100%;
              padding-left: 8px;
            }
          }

          .service-content {
            .service-basic {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 4px;

              .service-name {
                font-weight: 600;
                color: #2c3e50;
                font-size: 14px;
              }
            }

            .service-contact {
              display: flex;
              gap: 12px;
              margin-bottom: 2px;
              font-size: 12px;
              color: #606266;

              .phone {
                color: #409eff;
                font-family: 'Monaco', 'Consolas', monospace;
              }

              .email {
                color: #909399;
              }
            }

            .service-hours {
              font-size: 11px;
              color: #909399;
            }
          }
        }
      }
    }

    .batch-actions {
      margin-top: 15px;
      text-align: center;
      padding-top: 15px;
      border-top: 1px solid #ebeef5;

      .el-button {
        margin: 0 5px;
      }
    }
  }
}