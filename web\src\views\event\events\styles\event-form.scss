// 事项表单样式
.event-form {
  .form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #e1e6f0;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 20px 0;
      padding-bottom: 10px;
      border-bottom: 2px solid #409eff;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .required-mark {
        font-size: 12px;
        color: #f56c6c;
        font-weight: 400;
      }
    }

    .el-form-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .el-form-item__label {
      color: #606266;
      font-weight: 500;
    }
  }

  // 必填字段样式
  .el-form-item.is-required .el-form-item__label::before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }

  // textarea 样式优化
  .el-textarea__inner {
    font-family: inherit;
    resize: vertical;
  }

  // 日期选择器样式
  .el-date-editor {
    width: 100%;
  }

  // 数字输入框样式
  .el-input-number {
    width: 100%;
  }
}

// 底部按钮样式
.dialog-footer {
  .el-button {
    min-width: 100px;
    margin-left: 10px;

    &:first-child {
      margin-left: 0;
    }
  }

  .el-button--primary {
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    border: none;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);

    &:hover {
      background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%);
      box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
    }
  }
}

// 对话框样式优化
:deep(.event-dialog) {
  .el-dialog__body {
    padding: 20px 30px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .el-dialog__header {
    padding: 20px 30px 10px;
    border-bottom: 1px solid #ebeef5;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
    }
  }

  .el-dialog__footer {
    padding: 15px 30px 20px;
    border-top: 1px solid #ebeef5;
    text-align: right;
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.event-dialog) {
    .el-dialog {
      width: 95% !important;
      margin: 20px auto;
    }

    .el-dialog__body {
      padding: 15px 20px;
    }
  }

  .event-form {
    .form-section {
      padding: 15px;
      margin-bottom: 20px;

      .section-title {
        font-size: 14px;
        margin-bottom: 15px;
      }
    }

    .el-row .el-col {
      margin-bottom: 10px;
    }
  }
}