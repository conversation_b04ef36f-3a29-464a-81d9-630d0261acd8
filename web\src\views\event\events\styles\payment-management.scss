// 支付管理对话框样式
:deep(.payment-manage-dialog) {
  .el-dialog__body {
    padding: 20px 30px;
  }

  .payment-manage-content {
    .current-status {
      text-align: center;
      margin-bottom: 20px;

      p {
        font-size: 16px;
        margin: 0;
        color: #606266;
      }
    }

    .payment-confirm-section {
      margin-bottom: 30px;

      h4 {
        margin: 0 0 20px 0;
        color: #2c3e50;
        font-size: 16px;
        border-bottom: 2px solid #67c23a;
        padding-bottom: 8px;
      }

      .payment-form {
        .form-item {
          margin-bottom: 20px;

          label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #606266;
            font-size: 14px;
          }

          .upload-area {
            margin-bottom: 10px;
          }

          .upload-tip {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #909399;
            font-size: 12px;
            line-height: 1.4;

            .el-icon {
              color: #409eff;
              font-size: 14px;
            }
          }
        }

        .confirm-button {
          text-align: center;
          margin-top: 25px;

          .el-button {
            padding: 12px 30px;
            font-size: 16px;
          }
        }
      }
    }

    .history-payment-info {
      margin-bottom: 25px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e1e6f0;

      h4 {
        margin: 0 0 15px 0;
        color: #2c3e50;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
        border-bottom: 2px solid #409eff;
        padding-bottom: 8px;
      }

      .history-proof-section,
      .history-remark-section {
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        .proof-title,
        .remark-title {
          margin: 0 0 10px 0;
          color: #606266;
          font-size: 14px;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 6px;
        }

        .proof-images {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;

          .proof-image-item {
            text-align: center;

            .proof-image {
              width: 80px;
              height: 80px;
              border-radius: 6px;
              border: 1px solid #dcdfe6;
              cursor: pointer;
              transition: all 0.3s;

              &:hover {
                border-color: #409eff;
                transform: scale(1.05);
              }
            }

            .image-name {
              margin-top: 4px;
              font-size: 12px;
              color: #909399;
              max-width: 80px;
              word-break: break-all;
              line-height: 1.2;
            }
          }
        }

        .remark-content {
          padding: 10px 12px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #dcdfe6;
          color: #606266;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }

    .other-actions {
      h4 {
        margin: 0 0 15px 0;
        color: #2c3e50;
        font-size: 16px;
        border-bottom: 2px solid #909399;
        padding-bottom: 8px;
      }

      .button-group {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .el-button {
          flex: 1;
          min-width: 120px;
          justify-content: center;
          padding: 8px 16px;
        }
      }
    }
  }
}