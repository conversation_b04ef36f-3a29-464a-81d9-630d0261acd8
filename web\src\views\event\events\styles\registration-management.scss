// 报名管理对话框样式
:deep(.registration-dialog) {
  .el-dialog__body {
    padding: 20px 30px;
    max-height: 80vh;
    overflow-y: auto;
  }

  .registration-info {
    text-align: left;

    .name {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 4px;
      font-size: 14px;
    }

    .phone {
      color: #606266;
      font-size: 12px;
      margin-bottom: 2px;
    }

    .email {
      color: #909399;
      font-size: 12px;
    }
  }

  // 增强版参赛者信息样式
  .participant-info-enhanced {
    .participant-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f2f5;

      .header-info {
        flex: 1;
        min-width: 0;

        .participant-name {
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 2px;
          font-size: 14px;
        }

        .participant-contact {
          font-size: 12px;
          color: #409eff;
          font-family: 'Monaco', 'Consolas', monospace;
        }
      }
    }

    .registration-details {
      font-size: 12px;
      margin-bottom: 8px;

      :deep(.registration-data-viewer) {
        .compact-info-section {
          .compact-row {
            gap: 8px;
          }

          .compact-item {
            font-size: 11px;

            strong {
              font-weight: 500;
            }
          }
        }
      }
    }

    .view-toggle {
      text-align: right;
      padding-top: 4px;
      border-top: 1px solid #f0f2f5;

      .el-button {
        font-size: 11px;
        padding: 2px 8px;
      }
    }
  }
}

.fee-text {
  color: #e74c3c;
  font-weight: 600;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}