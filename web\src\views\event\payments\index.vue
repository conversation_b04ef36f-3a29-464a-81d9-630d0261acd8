<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="报名ID" prop="registrationId">
              <el-input v-model="queryParams.registrationId" placeholder="请输入报名ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="支付金额" prop="amount">
              <el-input v-model="queryParams.amount" placeholder="请输入支付金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="支付方式" prop="paymentMethod">
              <el-input v-model="queryParams.paymentMethod" placeholder="请输入支付方式" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="支付渠道" prop="paymentChannel">
              <el-input v-model="queryParams.paymentChannel" placeholder="请输入支付渠道" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="第三方交易号" prop="transactionId">
              <el-input v-model="queryParams.transactionId" placeholder="请输入第三方交易号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商户订单号" prop="outTradeNo">
              <el-input v-model="queryParams.outTradeNo" placeholder="请输入商户订单号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="支付状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择支付状态" clearable >
                <el-option v-for="dict in payment_status" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="支付成功时间" prop="paidAt">
              <el-date-picker clearable
                v-model="queryParams.paidAt"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择支付成功时间"
              />
            </el-form-item>
            <el-form-item label="退款金额" prop="refundAmount">
              <el-input v-model="queryParams.refundAmount" placeholder="请输入退款金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="退款原因" prop="refundReason">
              <el-input v-model="queryParams.refundReason" placeholder="请输入退款原因" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="退款时间" prop="refundedAt">
              <el-date-picker clearable
                v-model="queryParams.refundedAt"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择退款时间"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['event:payments:add']">新增支付记录</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['event:payments:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['event:payments:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['event:payments:export']">导出财务报表</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-dropdown @command="handlePaymentManagement" v-if="multiple">
              <el-button type="info" plain>
                支付管理<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="confirm">批量确认支付</el-dropdown-item>
                  <el-dropdown-item command="reject">批量拒绝支付</el-dropdown-item>
                  <el-dropdown-item command="refund">批量申请退款</el-dropdown-item>
                  <el-dropdown-item command="verify" divided>批量验证支付</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Refresh" @click="handleSyncPayments">同步支付状态</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="paymentsList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="支付ID" align="center" prop="id" width="80" v-if="false" />
        <el-table-column label="用户信息" align="left" min-width="180">
          <template #default="scope">
            <div class="user-info">
              <div class="user-basic">
                <el-avatar :size="30" :src="getUserAvatar(scope.row.userId)" />
                <div class="user-details">
                  <div class="user-name">{{ getUserName(scope.row.userId) }}</div>
                  <div class="user-id">ID: {{ scope.row.userId }}</div>
                </div>
              </div>
              <div class="registration-id">报名: {{ scope.row.registrationId }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="支付金额" align="center" width="120">
          <template #default="scope">
            <div class="payment-amount">
              <div class="amount">¥{{ scope.row.amount }}</div>
              <div class="method">{{ scope.row.paymentMethod || '未知' }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="支付状态" align="center" width="120">
          <template #default="scope">
            <div class="payment-status-info">
              <el-tag 
                :type="getPaymentStatusType(scope.row.status)" 
                size="small"
                effect="dark"
              >
                {{ getPaymentStatusText(scope.row.status) }}
              </el-tag>
              <div class="status-note" v-if="scope.row.status === 'pending'">
                <el-button 
                  link 
                  type="primary" 
                  size="small" 
                  @click="handleManualConfirm(scope.row)"
                >
                  手动确认
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="交易信息" align="center" width="160">
          <template #default="scope">
            <div class="transaction-info">
              <div class="channel">{{ scope.row.paymentChannel || '未知渠道' }}</div>
              <div class="transaction-id" v-if="scope.row.transactionId">
                <el-tooltip :content="scope.row.transactionId" placement="top">
                  <span>{{ scope.row.transactionId.substring(0, 12) }}...</span>
                </el-tooltip>
              </div>
              <div class="order-no" v-if="scope.row.outTradeNo">
                <el-tooltip :content="scope.row.outTradeNo" placement="top">
                  <span>订单: {{ scope.row.outTradeNo.substring(0, 10) }}...</span>
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="支付时间" align="center" width="140">
          <template #default="scope">
            <div class="time-info">
              <div class="paid-time" v-if="scope.row.paidAt">
                {{ parseTime(scope.row.paidAt, '{m}-{d} {h}:{i}') }}
              </div>
              <div class="create-time">
                创建: {{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="退款信息" align="center" width="120">
          <template #default="scope">
            <div class="refund-info" v-if="scope.row.refundAmount || scope.row.refundedAt">
              <div class="refund-amount">¥{{ scope.row.refundAmount || 0 }}</div>
              <div class="refund-time" v-if="scope.row.refundedAt">
                {{ parseTime(scope.row.refundedAt, '{m}-{d}') }}
              </div>
              <div class="refund-reason" v-if="scope.row.refundReason">
                <el-tooltip :content="scope.row.refundReason" placement="top">
                  <span>{{ scope.row.refundReason.substring(0, 10) }}...</span>
                </el-tooltip>
              </div>
            </div>
            <span v-else class="no-refund">-</span>
          </template>
        </el-table-column>
        <el-table-column label="支付凭证" align="center" width="100">
          <template #default="scope">
            <div class="payment-proof">
              <el-button 
                v-if="scope.row.notifyData" 
                link 
                type="primary" 
                icon="Document" 
                @click="showPaymentProof(scope.row)"
              >
                查看凭证
              </el-button>
              <el-upload
                v-else
                :show-file-list="false"
                :on-success="(response) => handleProofUpload(response, scope.row)"
                action="/api/upload"
                class="proof-upload"
              >
                <el-button link type="info" icon="Upload" size="small">上传凭证</el-button>
              </el-upload>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" fixed="right">
          <template #default="scope">
            <el-button-group size="small">
              <el-tooltip content="查看详情" placement="top">
                <el-button link type="primary" icon="View" @click="handleViewPayment(scope.row)"></el-button>
              </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['event:payments:edit']"></el-button>
            </el-tooltip>
              <el-tooltip 
                :content="scope.row.status === 'pending' ? '确认支付' : '取消支付'" 
                placement="top"
              >
                <el-button 
                  link 
                  :type="scope.row.status === 'pending' ? 'success' : 'warning'" 
                  :icon="scope.row.status === 'pending' ? 'CircleCheck' : 'CircleClose'" 
                  @click="handleTogglePaymentStatus(scope.row)"
                ></el-button>
              </el-tooltip>
              <el-tooltip content="申请退款" placement="top" v-if="scope.row.status === 'paid'">
                <el-button link type="danger" icon="RefreshLeft" @click="handleRefundRequest(scope.row)"></el-button>
            </el-tooltip>
              <el-dropdown @command="(command) => handlePaymentAction(command, scope.row)" trigger="click">
                <el-button link type="info" icon="MoreFilled"></el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="verify">验证支付状态</el-dropdown-item>
                    <el-dropdown-item command="resend">重发支付通知</el-dropdown-item>
                    <el-dropdown-item command="export">导出支付凭证</el-dropdown-item>
                    <el-dropdown-item command="note">添加备注</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除记录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改支付记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="paymentsFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="报名ID" prop="registrationId">
          <el-input v-model="form.registrationId" placeholder="请输入报名ID" />
        </el-form-item>
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="支付金额" prop="amount">
          <el-input v-model="form.amount" placeholder="请输入支付金额" />
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-input v-model="form.paymentMethod" placeholder="请输入支付方式" />
        </el-form-item>
        <el-form-item label="支付渠道" prop="paymentChannel">
          <el-input v-model="form.paymentChannel" placeholder="请输入支付渠道" />
        </el-form-item>
        <el-form-item label="第三方交易号" prop="transactionId">
          <el-input v-model="form.transactionId" placeholder="请输入第三方交易号" />
        </el-form-item>
        <el-form-item label="商户订单号" prop="outTradeNo">
          <el-input v-model="form.outTradeNo" placeholder="请输入商户订单号" />
        </el-form-item>
        <el-form-item label="支付状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择支付状态">
            <el-option
                v-for="dict in payment_status"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="支付成功时间" prop="paidAt">
          <el-date-picker clearable
            v-model="form.paidAt"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择支付成功时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="退款金额" prop="refundAmount">
          <el-input v-model="form.refundAmount" placeholder="请输入退款金额" />
        </el-form-item>
        <el-form-item label="退款原因" prop="refundReason">
          <el-input v-model="form.refundReason" placeholder="请输入退款原因" />
        </el-form-item>
        <el-form-item label="退款时间" prop="refundedAt">
          <el-date-picker clearable
            v-model="form.refundedAt"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择退款时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Payments" lang="ts">
import { listPayments, getPayments, delPayments, addPayments, updatePayments } from '@/api/event/payments';
import { PaymentsVO, PaymentsQuery, PaymentsForm } from '@/api/event/payments/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { payment_status } = toRefs<any>(proxy?.useDict('payment_status'));

const paymentsList = ref<PaymentsVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const paymentsFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: PaymentsForm = {
  id: undefined,
  registrationId: undefined,
  userId: undefined,
  amount: undefined,
  paymentMethod: undefined,
  paymentChannel: undefined,
  transactionId: undefined,
  outTradeNo: undefined,
  status: undefined,
  paidAt: undefined,
  notifyData: undefined,
  refundAmount: undefined,
  refundReason: undefined,
  refundedAt: undefined,
  remark: undefined,
}
const data = reactive<PageData<PaymentsForm, PaymentsQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    registrationId: undefined,
    userId: undefined,
    amount: undefined,
    paymentMethod: undefined,
    paymentChannel: undefined,
    transactionId: undefined,
    outTradeNo: undefined,
    status: undefined,
    paidAt: undefined,
    notifyData: undefined,
    refundAmount: undefined,
    refundReason: undefined,
    refundedAt: undefined,
    params: {
    }
  },
  rules: {
    paymentChannel: [
      { required: true, message: "支付渠道不能为空", trigger: "blur" }
    ],
    transactionId: [
      { required: true, message: "第三方交易号不能为空", trigger: "blur" }
    ],
    paidAt: [
      { required: true, message: "支付成功时间不能为空", trigger: "blur" }
    ],
    notifyData: [
      { required: true, message: "支付通知数据不能为空", trigger: "blur" }
    ],
    refundAmount: [
      { required: true, message: "退款金额不能为空", trigger: "blur" }
    ],
    refundReason: [
      { required: true, message: "退款原因不能为空", trigger: "blur" }
    ],
    refundedAt: [
      { required: true, message: "退款时间不能为空", trigger: "blur" }
    ],
    remark: [
      { required: true, message: "备注不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询支付记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPayments(queryParams.value);
  paymentsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  paymentsFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: PaymentsVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加支付记录";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: PaymentsVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getPayments(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改支付记录";
}

/** 提交按钮 */
const submitForm = () => {
  paymentsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updatePayments(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addPayments(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: PaymentsVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除支付记录编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delPayments(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('event/payments/export', {
    ...queryParams.value
  }, `payments_${new Date().getTime()}.xlsx`)
}

/** 批量支付管理操作 */
const handlePaymentManagement = async (command: string) => {
  if (ids.value.length === 0) {
    proxy?.$modal.msgWarning('请先选择要操作的支付记录');
    return;
  }
  
  const operations = {
    confirm: '确认支付',
    reject: '拒绝支付',
    refund: '申请退款',
    verify: '验证支付'
  };
  
  const operation = operations[command];
  await proxy?.$modal.confirm(`确认要批量${operation}选中的${ids.value.length}条记录吗？`);
  
  try {
    loading.value = true;
    switch (command) {
      case 'confirm':
        // await batchConfirmPayments(ids.value);
        break;
      case 'reject':
        // await batchRejectPayments(ids.value);
        break;
      case 'refund':
        // await batchRefundPayments(ids.value);
        break;
      case 'verify':
        // await batchVerifyPayments(ids.value);
        break;
    }
    proxy?.$modal.msgSuccess(`批量${operation}成功`);
    await getList();
  } catch (error) {
    proxy?.$modal.msgError(`批量${operation}失败`);
  } finally {
    loading.value = false;
  }
}

/** 同步支付状态 */
const handleSyncPayments = async () => {
  try {
    loading.value = true;
    // await syncPaymentStatus();
    proxy?.$modal.msgSuccess('支付状态同步成功');
    await getList();
  } catch (error) {
    proxy?.$modal.msgError('支付状态同步失败');
  } finally {
    loading.value = false;
  }
}

/** 查看支付详情 */
const handleViewPayment = (row: PaymentsVO) => {
  // 打开支付详情对话框
  showPaymentDetailDialog(row);
}

/** 手动确认支付 */
const handleManualConfirm = async (row: PaymentsVO) => {
  const { value: payerName } = await proxy?.$modal.prompt(
    '请输入支付人姓名（用于核对支付信息）',
    '手动确认支付',
    {
      confirmButtonText: '确认支付',
      cancelButtonText: '取消',
      inputPattern: /^.{1,20}$/,
      inputErrorMessage: '请输入有效的支付人姓名'
    }
  );
  
  if (payerName) {
    try {
      // await manualConfirmPayment(row.id, payerName);
      proxy?.$modal.msgSuccess(`支付确认成功，支付人：${payerName}`);
      await getList();
    } catch (error) {
      proxy?.$modal.msgError('支付确认失败');
    }
  }
}

/** 切换支付状态 */
const handleTogglePaymentStatus = async (row: PaymentsVO) => {
  const isPaid = row.status === 'paid';
  const action = isPaid ? '取消支付' : '确认支付';
  
  await proxy?.$modal.confirm(`确认要${action}这笔金额为¥${row.amount}的支付记录吗？`);
  
  try {
    const newStatus = isPaid ? 'pending' : 'paid';
    // await updatePaymentStatus(row.id, newStatus);
    proxy?.$modal.msgSuccess(`${action}成功`);
    await getList();
  } catch (error) {
    proxy?.$modal.msgError(`${action}失败`);
  }
}

/** 申请退款 */
const handleRefundRequest = async (row: PaymentsVO) => {
  const { value: refundData } = await proxy?.$modal.prompt(
    '请输入退款原因',
    '申请退款',
    {
      confirmButtonText: '申请退款',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputPlaceholder: '请详细说明退款原因...'
    }
  );
  
  if (refundData) {
    try {
      // await requestRefund(row.id, refundData, row.amount);
      proxy?.$modal.msgSuccess('退款申请提交成功');
      await getList();
    } catch (error) {
      proxy?.$modal.msgError('退款申请失败');
    }
  }
}

/** 支付操作 */
const handlePaymentAction = async (command: string, row: PaymentsVO) => {
  switch (command) {
    case 'verify':
      await handleVerifyPayment(row);
      break;
    case 'resend':
      await handleResendNotification(row);
      break;
    case 'export':
      handleExportProof(row);
      break;
    case 'note':
      await handleAddNote(row);
      break;
    case 'delete':
      await handleDelete(row);
      break;
  }
}

/** 验证支付状态 */
const handleVerifyPayment = async (row: PaymentsVO) => {
  try {
    loading.value = true;
    // await verifyPaymentStatus(row.id);
    proxy?.$modal.msgSuccess('支付状态验证完成');
    await getList();
  } catch (error) {
    proxy?.$modal.msgError('支付状态验证失败');
  } finally {
    loading.value = false;
  }
}

/** 重发支付通知 */
const handleResendNotification = async (row: PaymentsVO) => {
  try {
    // await resendPaymentNotification(row.id);
    proxy?.$modal.msgSuccess('支付通知重发成功');
  } catch (error) {
    proxy?.$modal.msgError('支付通知重发失败');
  }
}

/** 导出支付凭证 */
const handleExportProof = (row: PaymentsVO) => {
  if (row.notifyData) {
    const blob = new Blob([row.notifyData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `payment_proof_${row.id}.json`;
    a.click();
    URL.revokeObjectURL(url);
  } else {
    proxy?.$modal.msgWarning('该支付记录暂无凭证数据');
  }
}

/** 添加备注 */
const handleAddNote = async (row: PaymentsVO) => {
  const { value: note } = await proxy?.$modal.prompt(
    '请输入备注信息',
    '添加备注',
    {
      confirmButtonText: '保存',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputValue: row.remark || '',
      inputPlaceholder: '请输入备注信息...'
    }
  );
  
  if (note !== null) {
    try {
      // await updatePaymentRemark(row.id, note);
      proxy?.$modal.msgSuccess('备注保存成功');
      await getList();
    } catch (error) {
      proxy?.$modal.msgError('备注保存失败');
    }
  }
}

/** 显示支付详情对话框 */
const showPaymentDetailDialog = (row: PaymentsVO) => {
  // 实现支付详情对话框
  console.log('显示支付详情', row);
}

/** 显示支付凭证 */
const showPaymentProof = (row: PaymentsVO) => {
  // 实现支付凭证显示对话框
  console.log('显示支付凭证', row);
}

/** 处理凭证上传 */
const handleProofUpload = (response: any, row: PaymentsVO) => {
  // 处理凭证上传成功
  console.log('凭证上传成功', response, row);
  getList();
}

/** 获取用户头像 */
const getUserAvatar = (userId: string): string => {
  return `https://api.dicebear.com/6.x/initials/svg?seed=${userId}`;
}

/** 获取用户名称 */
const getUserName = (userId: string): string => {
  // 这里应该通过用户ID查询用户名称，暂时返回ID
  return `用户-${userId}`;
}

/** 获取支付状态类型 */
const getPaymentStatusType = (status: string): string => {
  const statusMap = {
    'paid': 'success',
    'pending': 'warning',
    'failed': 'danger',
    'refunded': 'info',
    'cancelled': 'info'
  };
  return statusMap[status] || 'info';
}

/** 获取支付状态文本 */
const getPaymentStatusText = (status: string): string => {
  const statusMap = {
    'paid': '已支付',
    'pending': '待支付',
    'failed': '支付失败',
    'refunded': '已退款',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
}

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.user-info {
  .user-basic {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .el-avatar {
      margin-right: 10px;
    }
    
    .user-details {
      .user-name {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 2px;
      }
      
      .user-id {
        font-size: 12px;
        color: #7f8c8d;
      }
    }
  }
  
  .registration-id {
    font-size: 12px;
    color: #3498db;
    font-weight: 500;
  }
}

.payment-amount {
  text-align: center;
  
  .amount {
    font-weight: 700;
    color: #e74c3c;
    font-size: 16px;
    margin-bottom: 4px;
  }
  
  .method {
    font-size: 12px;
    color: #7f8c8d;
  }
}

.payment-status-info {
  .status-note {
    margin-top: 6px;
  }
}

.transaction-info {
  font-size: 12px;
  line-height: 1.4;
  
  .channel {
    color: #2c3e50;
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .transaction-id, .order-no {
    color: #3498db;
    font-family: monospace;
    margin-bottom: 2px;
    cursor: pointer;
  }
}

.time-info {
  font-size: 12px;
  line-height: 1.4;
  
  .paid-time {
    color: #27ae60;
    font-weight: 600;
    margin-bottom: 4px;
  }
  
  .create-time {
    color: #7f8c8d;
  }
}

.refund-info {
  font-size: 12px;
  text-align: center;
  
  .refund-amount {
    color: #e67e22;
    font-weight: 600;
    margin-bottom: 4px;
  }
  
  .refund-time {
    color: #7f8c8d;
    margin-bottom: 2px;
  }
  
  .refund-reason {
    color: #95a5a6;
    cursor: pointer;
  }
}

.no-refund {
  color: #bdc3c7;
  font-size: 12px;
}

.payment-proof {
  .proof-upload {
    :deep(.el-upload) {
      display: inline-block;
    }
  }
}

:deep(.el-table) {
  .el-table__row {
    &:hover {
      background-color: #f8f9fa;
    }
  }
  
  .el-table__cell {
    padding: 12px 0;
  }
}

:deep(.el-button-group) {
  .el-button + .el-button {
    margin-left: 0;
  }
}

.el-dropdown {
  margin-left: 4px;
}

// 支付状态特殊样式
:deep(.el-tag) {
  &.el-tag--success {
    background-color: #f0f9ff;
    border-color: #67c23a;
    color: #67c23a;
  }
  
  &.el-tag--warning {
    background-color: #fdf6ec;
    border-color: #e6a23c;
    color: #e6a23c;
  }
  
  &.el-tag--danger {
    background-color: #fef0f0;
    border-color: #f56c6c;
    color: #f56c6c;
  }
  
  &.el-tag--info {
    background-color: #f4f4f5;
    border-color: #909399;
    color: #909399;
  }
}

// 手动确认按钮样式
.status-note {
  .el-button--primary {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
    font-size: 12px;
    padding: 2px 8px;
    height: auto;
  }
}
</style>
