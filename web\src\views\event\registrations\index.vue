<template>
  <!-- 
    报名记录管理页面 - 集成了RegistrationDataViewer组件
    
    功能特性：
    1. 增强版参赛者信息展示
       - 头像 + 基本信息（姓名、ID、联系方式）
       - 根据表单配置自动展示报名详情
       - 支持紧凑模式和详细模式切换
    
    2. 智能字段解析
       - 自动根据fieldType格式化显示值
       - 支持选择框、日期、文件等特殊类型
       - 字段按类型分组展示（个人信息、参赛信息、健康信息等）
    
    3. 性能优化
       - 表单配置缓存机制
       - 按赛事ID批量加载配置
       - 兜底默认字段配置
    
    使用说明：
    - 点击"详情"按钮展开完整信息
    - 支持响应式设计，移动端友好
    - TODO: 将getFormConfig API集成到loadFormConfigs函数中
  -->
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="90px">
            <el-form-item label="参赛者信息" prop="userId">
              <el-input v-model="queryParams.userId" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="支付状态" prop="paymentStatus">
              <el-select v-model="queryParams.paymentStatus" placeholder="请选择支付状态" clearable >
                <el-option v-for="dict in payment_status" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="签到状态" prop="checkInStatus">
              <el-select v-model="queryParams.checkInStatus" placeholder="请选择签到状态" clearable >
                <el-option v-for="dict in event_check_in_status" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="参赛状态" prop="completionStatus">
              <el-select v-model="queryParams.completionStatus" placeholder="请选择参赛状态" clearable >
                <el-option v-for="dict in event_completion_status" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['event:registrations:add']">新增报名</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['event:registrations:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['event:registrations:remove']">删除</el-button>
          </el-col>
          <!-- <el-col :span="2">
            <el-input
              v-model="quickSearchKeyword"
              placeholder="快速搜索(姓名/手机号)"
              prefix-icon="Search"
              @keyup.enter="handleQuickSearch"
              @clear="handleQuickSearchClear"
              clearable
              style="width: 200px"
            />
          </el-col> -->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['event:registrations:export']">导出报名表</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="User" @click="handleUpdatePaymentByName">根据支付人确认</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-dropdown @command="handlePaymentAction" v-if="multiple">
              <el-button type="info" plain>
                支付管理<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="confirm">批量确认支付</el-dropdown-item>
                  <el-dropdown-item command="reject">批量拒绝支付</el-dropdown-item>
                  <el-dropdown-item command="refund">批量退款</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-col>
          <el-col :span="1.5">
            <el-dropdown @command="handleBulkAction" v-if="multiple">
              <el-button type="info" plain>
                批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="checkin">批量签到</el-dropdown-item>
                  <el-dropdown-item command="finish">批量完赛</el-dropdown-item>
                  <el-dropdown-item command="cancel" divided>批量取消报名</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="registrationsList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="报名ID" align="center" prop="id" width="80" v-if="false" />
        <el-table-column label="参赛者信息" align="left" min-width="320">
          <template #default="scope">
            <div class="participant-info-enhanced">
              <div class="participant-header">
                <el-avatar :size="35" :src="getUserAvatar(scope.row.userId)" />
                <div class="header-info">
                  <div class="participant-name">{{ getParticipantName(scope.row.registrationData) }}</div>
                  <div class="participant-id">ID: {{ scope.row.userId }}</div>
                  <div class="participant-contact">{{ getParticipantPhone(scope.row.registrationData) }}</div>
                </div>
              </div>
              <div class="registration-details">
                <RegistrationDataViewer 
                  :formFieldsInfo="getFormFieldsInfo(scope.row.eventId)"
                  :formData="scope.row.registrationData"
                  :compact="!detailedViewIds.has(scope.row.id.toString())"
                  :maxFields="detailedViewIds.has(scope.row.id.toString()) ? 20 : 4"
                  :showEmptyFields="detailedViewIds.has(scope.row.id.toString())"
                />
              </div>
              <div class="view-toggle">
                <el-button
                  link
                  type="primary"
                  size="small"
                  @click="toggleDetailedView(scope.row.id.toString())"
                  :icon="detailedViewIds.has(scope.row.id.toString()) ? 'Fold' : 'Expand'"
                >
                  {{ detailedViewIds.has(scope.row.id.toString()) ? '收起' : '详情' }}
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="赛事信息" align="left" width="180">
          <template #default="scope">
            <div class="event-info">
              <div class="event-name">{{ getEventName(scope.row.eventId) }}</div>
              <div class="event-item">{{ getEventItemName(scope.row.eventItemId) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="支付状态" align="center" width="120">
          <template #default="scope">
            <div class="payment-status">
              <el-tag
                :type="getPaymentStatusType(scope.row.paymentStatus)"
                size="small"
                effect="dark"
              >
                {{ getPaymentStatusText(scope.row.paymentStatus) }}
              </el-tag>
              <div class="payment-amount" v-if="scope.row.paymentAmount">
                ¥{{ scope.row.paymentAmount }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="支付信息" align="center" width="140">
          <template #default="scope">
            <div class="payment-details" v-if="scope.row.paymentTime">
              <div class="payment-method">{{ scope.row.paymentMethod || '未知' }}</div>
              <div class="payment-time">{{ parseTime(scope.row.paymentTime, '{m}-{d} {h}:{i}') }}</div>
              <div class="transaction-id" v-if="scope.row.paymentTransactionId">
                <el-tooltip :content="scope.row.paymentTransactionId" placement="top">
                  <span>{{ scope.row.paymentTransactionId.substring(0, 10) }}...</span>
                </el-tooltip>
              </div>
            </div>
            <span v-else class="no-payment">未支付</span>
          </template>
        </el-table-column>
        <el-table-column label="签到状态" align="center" width="100">
          <template #default="scope">
            <el-tag
              :type="getCheckInStatusType(scope.row.checkInStatus)"
              size="small"
            >
              {{ getCheckInStatusText(scope.row.checkInStatus) }}
            </el-tag>
            <div class="checkin-time" v-if="scope.row.checkInTime">
              {{ parseTime(scope.row.checkInTime, '{m}-{d} {h}:{i}') }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="参赛状态" align="center" width="100">
          <template #default="scope">
            <el-tag
              :type="getCompletionStatusType(scope.row.completionStatus)"
              size="small"
            >
              {{ getCompletionStatusText(scope.row.completionStatus) }}
            </el-tag>
            <div class="completion-time" v-if="scope.row.completionTime">
              {{ parseTime(scope.row.completionTime, '{m}-{d} {h}:{i}') }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="二维码" align="center" width="80">
          <template #default="scope">
            <el-button
              v-if="scope.row.qrCodeUrl"
              link
              type="primary"
              icon="QrCode"
              @click="showQRCode(scope.row.qrCodeUrl, '报名二维码')"
            />
            <span v-else class="no-qr">-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="220" fixed="right">
          <template #default="scope">
            <el-button-group size="small">
              <el-tooltip content="查看详情" placement="top">
                <el-button link type="primary" icon="View" @click="handleViewDetail(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="修改" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['event:registrations:edit']"></el-button>
              </el-tooltip>
              <el-tooltip content="支付管理" placement="top">
                <el-button link type="warning" icon="Money" @click="handlePaymentManage(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="签到操作" placement="top">
                <el-button
                  link
                  :type="scope.row.checkInStatus === 'checked_in' ? 'success' : 'info'"
                  icon="CircleCheck"
                  @click="handleCheckIn(scope.row)"
                ></el-button>
              </el-tooltip>
              <el-dropdown @command="(command) => handleRowAction(command, scope.row)" trigger="click">
                <el-button link type="info" icon="MoreFilled"></el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="finish" v-if="scope.row.checkInStatus === 'checked_in'">标记完赛</el-dropdown-item>
                    <el-dropdown-item command="result">录入成绩</el-dropdown-item>
                    <el-dropdown-item command="refund" v-if="scope.row.paymentStatus === 'paid'">申请退款</el-dropdown-item>
                    <el-dropdown-item command="cancel" divided>取消报名</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改报名记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="registrationsFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="赛事ID" prop="eventId">
          <el-input v-model="form.eventId" placeholder="请输入赛事ID" />
        </el-form-item>
        <el-form-item label="报名项目ID" prop="eventItemId">
          <el-input v-model="form.eventItemId" placeholder="请输入报名项目ID" />
        </el-form-item>
        <el-form-item label="支付状态" prop="paymentStatus">
          <el-select v-model="form.paymentStatus" placeholder="请选择支付状态">
            <el-option
                v-for="dict in payment_status"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="支付金额" prop="paymentAmount">
          <el-input v-model="form.paymentAmount" placeholder="请输入支付金额" />
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-input v-model="form.paymentMethod" placeholder="请输入支付方式" />
        </el-form-item>
        <el-form-item label="支付时间" prop="paymentTime">
          <el-date-picker clearable
            v-model="form.paymentTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择支付时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="支付交易号" prop="paymentTransactionId">
          <el-input v-model="form.paymentTransactionId" placeholder="请输入支付交易号" />
        </el-form-item>
        <el-form-item label="支付二维码URL" prop="qrCodeUrl">
            <el-input v-model="form.qrCodeUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="签到状态" prop="checkInStatus">
          <el-select v-model="form.checkInStatus" placeholder="请选择签到状态">
            <el-option
                v-for="dict in event_check_in_status"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="签到时间" prop="checkInTime">
          <el-date-picker clearable
            v-model="form.checkInTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择签到时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="参赛状态" prop="completionStatus">
          <el-select v-model="form.completionStatus" placeholder="请选择参赛状态">
            <el-option
                v-for="dict in event_completion_status"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="完赛时间" prop="completionTime">
          <el-date-picker clearable
            v-model="form.completionTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择完赛时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Registrations" lang="ts">
import {
  listRegistrations,
  getRegistrations,
  delRegistrations,
  addRegistrations,
  updateRegistrations,
  updatePaymentStatus,
  updatePaymentStatusByPayerName,
  batchUpdatePaymentStatus,
  searchRegistrations,
  listByPaymentStatus,
  listByEvent,
  getRegistrationStatistics,
  generatePaymentQrCode,
  exportRegistrations,
  exportByEvent
} from '@/api/event/registrations';
import { RegistrationsVO, RegistrationsQuery, RegistrationsForm } from '@/api/event/registrations/types';
import RegistrationDataViewer from '@/components/FormBuilder/RegistrationDataViewer.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { event_check_in_status, event_completion_status, payment_status } = toRefs<any>(proxy?.useDict('event_check_in_status', 'event_completion_status', 'payment_status'));

const registrationsList = ref<RegistrationsVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const quickSearchKeyword = ref('');

// 表单字段配置缓存
const formFieldsCache = ref<Record<string, any[]>>({});
// 详情展示状态
const detailedViewIds = ref<Set<string>>(new Set());

// 异步加载表单配置（用于组件初始化）
const loadFormConfigs = async () => {
  // 批量获取当前页面所有赛事的表单配置
  const eventIds = [...new Set(registrationsList.value.map(item => item.eventId))];
  for (const eventId of eventIds) {
    if (!formFieldsCache.value[eventId]) {
      // 这里调用实际的API
      // try {
      //   const response = await getFormConfig(eventId);
      //   formFieldsCache.value[eventId] = response.data.formFields || getDefaultFields();
      // } catch (error) {
      //   formFieldsCache.value[eventId] = getDefaultFields();
      // }
      
      // 暂时使用默认配置
      formFieldsCache.value[eventId] = getDefaultFields();
    }
  }
};

// 获取默认字段配置
const getDefaultFields = () => [
  { fieldKey: 'name', fieldLabel: '姓名', fieldType: 'text', sortOrder: 1 },
  { fieldKey: 'phone', fieldLabel: '手机号', fieldType: 'phone', sortOrder: 2 },
  { fieldKey: 'idCard', fieldLabel: '身份证号', fieldType: 'text', sortOrder: 3 },
  { fieldKey: 'gender', fieldLabel: '性别', fieldType: 'radio', fieldOptions: { options: [{ label: '男', value: '男' }, { label: '女', value: '女' }] }, sortOrder: 4 },
  { fieldKey: 'age', fieldLabel: '年龄', fieldType: 'number', sortOrder: 5 },
  { fieldKey: 'email', fieldLabel: '邮箱', fieldType: 'email', sortOrder: 6 },
  { fieldKey: 'emergencyContact', fieldLabel: '紧急联系人', fieldType: 'text', sortOrder: 7 },
  { fieldKey: 'emergencyPhone', fieldLabel: '紧急联系电话', fieldType: 'phone', sortOrder: 8 },
  { fieldKey: 'address', fieldLabel: '地址', fieldType: 'textarea', sortOrder: 9 },
  { fieldKey: 'birthday', fieldLabel: '出生日期', fieldType: 'date', sortOrder: 10 },
  { fieldKey: 'category', fieldLabel: '参赛类别', fieldType: 'select', fieldOptions: { options: [{ label: '业余组', value: 'amateur' }, { label: '专业组', value: 'professional' }] }, sortOrder: 11 },
  { fieldKey: 'tshirtSize', fieldLabel: 'T恤尺码', fieldType: 'select', fieldOptions: { options: [{ label: 'S', value: 'S' }, { label: 'M', value: 'M' }, { label: 'L', value: 'L' }, { label: 'XL', value: 'XL' }] }, sortOrder: 12 }
];

// 获取表单字段配置信息
const getFormFieldsInfo = (eventId: string): any[] => {
  return formFieldsCache.value[eventId] || getDefaultFields();
};

// 切换详细视图
const toggleDetailedView = (id: string) => {
  if (detailedViewIds.value.has(id)) {
    detailedViewIds.value.delete(id);
  } else {
    detailedViewIds.value.add(id);
  }
};

const queryFormRef = ref<ElFormInstance>();
const registrationsFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: RegistrationsForm = {
  id: undefined,
  userId: undefined,
  eventId: undefined,
  eventItemId: undefined,
  registrationData: undefined,
  paymentStatus: undefined,
  paymentAmount: undefined,
  paymentMethod: undefined,
  paymentTime: undefined,
  paymentTransactionId: undefined,
  qrCodeUrl: undefined,
  checkInStatus: undefined,
  checkInTime: undefined,
  completionStatus: undefined,
  completionTime: undefined,
  resultData: undefined,
  remark: undefined,
}
const data = reactive<PageData<RegistrationsForm, RegistrationsQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: undefined,
    eventId: undefined,
    eventItemId: undefined,
    registrationData: undefined,
    paymentStatus: undefined,
    paymentAmount: undefined,
    paymentMethod: undefined,
    paymentTime: undefined,
    paymentTransactionId: undefined,
    qrCodeUrl: undefined,
    checkInStatus: undefined,
    checkInTime: undefined,
    completionStatus: undefined,
    completionTime: undefined,
    resultData: undefined,
    params: {
    }
  },
  rules: {
    paymentMethod: [
      { required: true, message: "支付方式不能为空", trigger: "blur" }
    ],
    paymentTime: [
      { required: true, message: "支付时间不能为空", trigger: "blur" }
    ],
    paymentTransactionId: [
      { required: true, message: "支付交易号不能为空", trigger: "blur" }
    ],
    qrCodeUrl: [
      { required: true, message: "支付二维码URL不能为空", trigger: "blur" }
    ],
    checkInTime: [
      { required: true, message: "签到时间不能为空", trigger: "blur" }
    ],
    completionTime: [
      { required: true, message: "完赛时间不能为空", trigger: "blur" }
    ],
    resultData: [
      { required: true, message: "比赛结果数据不能为空", trigger: "blur" }
    ],
    remark: [
      { required: true, message: "备注不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询报名记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listRegistrations(queryParams.value);
  registrationsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
  
  // 数据加载完成后，异步加载表单配置
  loadFormConfigs();
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  registrationsFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: RegistrationsVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加报名记录";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: RegistrationsVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getRegistrations(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改报名记录";
}

/** 提交按钮 */
const submitForm = () => {
  registrationsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRegistrations(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addRegistrations(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: RegistrationsVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除报名记录编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delRegistrations(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('event/registrations/export', {
    ...queryParams.value
  }, `registrations_${new Date().getTime()}.xlsx`)
}

/** 快速搜索 */
const handleQuickSearch = async () => {
  if (!quickSearchKeyword.value.trim()) {
    proxy?.$modal.msgWarning('请输入搜索关键词');
    return;
  }

  try {
    loading.value = true;
    const res = await searchRegistrations(quickSearchKeyword.value, {
      pageNum: 1,
      pageSize: queryParams.value.pageSize
    });
    registrationsList.value = res.rows;
    total.value = res.total;
    queryParams.value.pageNum = 1;
  } catch (error) {
    proxy?.$modal.msgError('搜索失败');
  } finally {
    loading.value = false;
  }
}

/** 清除快速搜索 */
const handleQuickSearchClear = () => {
  quickSearchKeyword.value = '';
  getList();
}

/** 支付管理 */
// const handlePaymentManage = async (row: RegistrationsVO) => {
//   const options = ['确认支付', '拒绝支付', '申请退款'];
//   const { value } = await proxy?.$modal.prompt('请选择操作并填写备注', '支付管理', {
//     confirmButtonText: '确定',
//     cancelButtonText: '取消',
//     inputPlaceholder: '请输入操作备注',
//     showCancelButton: true,
//     closeOnClickModal: false,
//     beforeClose: (action, instance, done) => {
//       if (action === 'confirm') {
//         if (!instance.inputValue) {
//           proxy?.$modal.msgError('请输入操作备注');
//           return false;
//         }
//       }
//       done();
//     }
//   });

//   if (value) {
//     try {
//       const currentStatus = row.paymentStatus;
//       let newStatus;

//       if (currentStatus === 0) { // 未支付
//         newStatus = 1; // 设为已支付
//       } else { // 已支付
//         newStatus = 0; // 设为未支付
//       }

//       await updatePaymentStatus(row.id, newStatus, value);
//       proxy?.$modal.msgSuccess('支付状态更新成功');
//       await getList();
//     } catch (error) {
//       proxy?.$modal.msgError('支付状态更新失败');
//     }
//   }
// }

/** 根据支付人姓名更新支付状态 */
const handleUpdatePaymentByName = async () => {
  const { value } = await proxy?.$modal.prompt('请输入支付人姓名', '根据支付人修改状态', {
    confirmButtonText: '查找并更新',
    cancelButtonText: '取消',
    inputPlaceholder: '请输入支付人姓名',
    showCancelButton: true
  });

  if (value) {
    try {
      await updatePaymentStatusByPayerName(value, 1, '根据支付人姓名确认支付');
      proxy?.$modal.msgSuccess('支付状态更新成功');
      await getList();
    } catch (error) {
      proxy?.$modal.msgError('支付状态更新失败，请检查支付人姓名是否正确');
    }
  }
}

/** 支付管理操作 */
const handlePaymentAction = async (command: string) => {
  if (ids.value.length === 0) {
    proxy?.$modal.msgWarning('请先选择要操作的报名记录');
    return;
  }

  const operations = {
    confirm: '确认支付',
    reject: '拒绝支付',
    refund: '退款'
  };

  const operation = operations[command];
  const { value } = await proxy?.$modal.prompt(
    `确认要批量${operation}选中的${ids.value.length}条记录吗？`,
    '批量支付操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPlaceholder: '请输入操作备注(必填)',
      showCancelButton: true
    }
  );

  if (value) {
    try {
      loading.value = true;
      // 根据不同操作调用相应API
      switch (command) {
        case 'confirm':
          await batchUpdatePaymentStatus(ids.value as string[], 1, value);
          break;
        case 'reject':
          await batchUpdatePaymentStatus(ids.value as string[], 0, value);
          break;
        case 'refund':
          // 退款逻辑需要调用退款API
          proxy?.$modal.msgWarning('退款功能正在开发中');
          return;
      }
      proxy?.$modal.msgSuccess(`批量${operation}成功`);
      await getList();
    } catch (error) {
      proxy?.$modal.msgError(`批量${operation}失败`);
    } finally {
      loading.value = false;
    }
  }
}

/** 批量操作 */
const handleBulkAction = async (command: string) => {
  if (ids.value.length === 0) {
    proxy?.$modal.msgWarning('请先选择要操作的报名记录');
    return;
  }

  const operations = {
    checkin: '签到',
    finish: '完赛',
    cancel: '取消报名'
  };

  const operation = operations[command];
  await proxy?.$modal.confirm(`确认要批量${operation}选中的${ids.value.length}条记录吗？`);

  try {
    loading.value = true;
    switch (command) {
      case 'checkin':
        // await batchCheckIn(ids.value);
        break;
      case 'finish':
        // await batchFinish(ids.value);
        break;
      case 'cancel':
        await delRegistrations(ids.value);
        break;
    }
    proxy?.$modal.msgSuccess(`批量${operation}成功`);
    await getList();
  } catch (error) {
    proxy?.$modal.msgError(`批量${operation}失败`);
  } finally {
    loading.value = false;
  }
}

/** 查看详情 */
const handleViewDetail = (row: RegistrationsVO) => {
  // 打开详情对话框或跳转详情页面
  proxy?.$router.push(`/event/registrations/detail/${row.id}`);
}

/** 支付管理 */
const handlePaymentManage = (row: RegistrationsVO) => {
  // 打开支付管理对话框
  showPaymentManageDialog(row);
}

/** 签到操作 */
const handleCheckIn = async (row: RegistrationsVO) => {
  const isCheckedIn = row.checkInStatus === 'checked_in';
  const action = isCheckedIn ? '取消签到' : '确认签到';

  await proxy?.$modal.confirm(`确认要${action}参赛者"${getParticipantName(row.registrationData)}"吗？`);

  try {
    const newStatus = isCheckedIn ? 'not_checked_in' : 'checked_in';
    // await updateCheckInStatus(row.id, newStatus);
    proxy?.$modal.msgSuccess(`${action}成功`);
    await getList();
  } catch (error) {
    proxy?.$modal.msgError(`${action}失败`);
  }
}

/** 行操作 */
const handleRowAction = async (command: string, row: RegistrationsVO) => {
  switch (command) {
    case 'finish':
      await handleFinishRace(row);
      break;
    case 'result':
      handleRecordResult(row);
      break;
    case 'refund':
      await handleRefund(row);
      break;
    case 'cancel':
      await handleDelete(row);
      break;
  }
}

/** 标记完赛 */
const handleFinishRace = async (row: RegistrationsVO) => {
  await proxy?.$modal.confirm(`确认标记参赛者"${getParticipantName(row.registrationData)}"为完赛状态吗？`);

  try {
    // await updateCompletionStatus(row.id, 'completed');
    proxy?.$modal.msgSuccess('标记完赛成功');
    await getList();
  } catch (error) {
    proxy?.$modal.msgError('标记完赛失败');
  }
}

/** 录入成绩 */
const handleRecordResult = (row: RegistrationsVO) => {
  // 打开成绩录入对话框
  showResultDialog(row);
}

/** 申请退款 */
const handleRefund = async (row: RegistrationsVO) => {
  await proxy?.$modal.confirm(`确认要为参赛者"${getParticipantName(row.registrationData)}"申请退款吗？`);

  try {
    // await requestRefund(row.id);
    proxy?.$modal.msgSuccess('退款申请提交成功');
    await getList();
  } catch (error) {
    proxy?.$modal.msgError('退款申请失败');
  }
}

/** 显示支付管理对话框 */
const showPaymentManageDialog = (row: RegistrationsVO) => {
  // 实现支付管理对话框
  console.log('打开支付管理对话框', row);
}

/** 显示成绩录入对话框 */
const showResultDialog = (row: RegistrationsVO) => {
  // 实现成绩录入对话框
  console.log('打开成绩录入对话框', row);
}

/** 显示二维码 */
const showQRCode = (url: string, title: string) => {
  // 实现二维码显示对话框
  console.log('显示二维码:', url, title);
}

/** 获取用户头像 */
const getUserAvatar = (userId: string): string => {
  // 根据用户ID获取头像，这里返回默认头像
  return `https://api.dicebear.com/6.x/initials/svg?seed=${userId}`;
}

/** 获取参赛者姓名 */
const getParticipantName = (registrationData: string): string => {
  try {
    const data = JSON.parse(registrationData || '{}');
    return data.name || data.realName || '未知';
  } catch {
    return '未知';
  }
}

/** 获取参赛者电话 */
const getParticipantPhone = (registrationData: string): string => {
  try {
    const data = JSON.parse(registrationData || '{}');
    return data.phone || data.mobile || '';
  } catch {
    return '';
  }
}

/** 获取参赛者身份证 */
const getParticipantIdCard = (registrationData: string): string => {
  try {
    const data = JSON.parse(registrationData || '{}');
    const idCard = data.idCard || data.identityCard || '';
    return idCard ? `${idCard.substring(0, 6)}****${idCard.substring(14)}` : '';
  } catch {
    return '';
  }
}

/** 获取赛事名称 */
const getEventName = (eventId: string): string => {
  // 这里应该通过eventId查询赛事名称，暂时返回ID
  return `赛事-${eventId}`;
}

/** 获取项目名称 */
const getEventItemName = (eventItemId: string): string => {
  // 这里应该通过eventItemId查询项目名称，暂时返回ID
  return `项目-${eventItemId}`;
}

/** 获取支付状态类型 */
const getPaymentStatusType = (status: number): string => {
  const statusMap = {
    0: 'danger',   // 未支付
    1: 'success',  // 已支付
    2: 'warning',  // 处理中
    3: 'info'      // 已退款
  };
  return statusMap[status] || 'info';
}

/** 获取支付状态文本 */
const getPaymentStatusText = (status: number): string => {
  const statusMap = {
    0: '未支付',
    1: '已支付',
    2: '处理中',
    3: '已退款'
  };
  return statusMap[status] || '未知';
}

/** 获取签到状态类型 */
const getCheckInStatusType = (status: number): string => {
  const statusMap = {
    0: 'info',      // 未签到
    1: 'success',   // 已签到
    2: 'warning'    // 迟到
  };
  return statusMap[status] || 'info';
}

/** 获取签到状态文本 */
const getCheckInStatusText = (status: number): string => {
  const statusMap = {
    0: '未签到',
    1: '已签到',
    2: '迟到'
  };
  return statusMap[status] || '未知';
}

/** 获取完赛状态类型 */
const getCompletionStatusType = (status: number): string => {
  const statusMap = {
    0: 'info',      // 未开始
    1: 'warning',   // 进行中
    2: 'success',   // 已完赛
    3: 'danger'     // 未完赛
  };
  return statusMap[status] || 'info';
}

/** 获取完赛状态文本 */
const getCompletionStatusText = (status: number): string => {
  const statusMap = {
    0: '未开始',
    1: '进行中',
    2: '已完赛',
    3: '未完赛'
  };
  return statusMap[status] || '未知';
}

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
// 增强版参赛者信息样式
.participant-info-enhanced {
  .participant-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f2f5;

    .el-avatar {
      margin-right: 12px;
      flex-shrink: 0;
    }

    .header-info {
      flex: 1;
      min-width: 0;

      .participant-name {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 2px;
        font-size: 14px;
      }

      .participant-id {
        font-size: 12px;
        color: #7f8c8d;
        margin-bottom: 2px;
      }

      .participant-contact {
        font-size: 12px;
        color: #409eff;
        font-family: 'Monaco', 'Consolas', monospace;
      }
    }
  }

  .registration-details {
    font-size: 12px;
    margin-bottom: 8px;
    
    :deep(.registration-data-viewer) {
      .compact-info-section {
        .compact-row {
          gap: 8px;
        }
        
        .compact-item {
          font-size: 11px;
          
          strong {
            font-weight: 500;
          }
        }
      }
    }
  }

  .view-toggle {
    text-align: right;
    padding-top: 4px;
    border-top: 1px solid #f0f2f5;

    .el-button {
      font-size: 11px;
      padding: 2px 8px;
    }
  }
}

// 原有参赛者信息样式（保留作为备份）
.participant-info {
  .participant-name {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .el-avatar {
      margin-right: 12px;
    }

    .name-section {
      .name {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 2px;
      }

      .user-id {
        font-size: 12px;
        color: #7f8c8d;
      }
    }
  }

  .contact-info {
    font-size: 12px;
    color: #7f8c8d;

    .phone {
      margin-right: 12px;
    }

    .id-card {
      font-family: monospace;
    }
  }
}

.event-info {
  .event-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
  }

  .event-item {
    font-size: 12px;
    color: #7f8c8d;
  }
}

.payment-status {
  .payment-amount {
    font-weight: 600;
    color: #e74c3c;
    margin-top: 4px;
    font-size: 13px;
  }
}

.payment-details {
  font-size: 12px;
  line-height: 1.4;

  .payment-method {
    color: #2c3e50;
    font-weight: 500;
    margin-bottom: 2px;
  }

  .payment-time {
    color: #7f8c8d;
    margin-bottom: 2px;
  }

  .transaction-id {
    color: #3498db;
    font-family: monospace;
    cursor: pointer;
  }
}

.no-payment {
  color: #bdc3c7;
  font-size: 12px;
}

.checkin-time, .completion-time {
  font-size: 11px;
  color: #7f8c8d;
  margin-top: 2px;
}

.no-qr {
  color: #bdc3c7;
  font-size: 12px;
}

:deep(.el-table) {
  .el-table__row {
    &:hover {
      background-color: #f8f9fa;
    }
  }

  .el-table__cell {
    padding: 12px 0;
  }
}

:deep(.el-button-group) {
  .el-button + .el-button {
    margin-left: 0;
  }
}

.el-dropdown {
  margin-left: 4px;
}

// 支付状态标签样式
:deep(.el-tag) {
  &.el-tag--success {
    background-color: #f0f9ff;
    border-color: #67c23a;
    color: #67c23a;
  }

  &.el-tag--warning {
    background-color: #fdf6ec;
    border-color: #e6a23c;
    color: #e6a23c;
  }

  &.el-tag--danger {
    background-color: #fef0f0;
    border-color: #f56c6c;
    color: #f56c6c;
  }
}
</style>
