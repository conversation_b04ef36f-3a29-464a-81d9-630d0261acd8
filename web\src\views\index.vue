<template>
  <div class="app-container dashboard">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="banner-text">
          <h1>赛事报名管理平台</h1>
          <p>实时掌握赛事报名动态，助力体育事业发展</p>
        </div>
        <div class="banner-icon">
          <svg viewBox="0 0 24 24" class="sports-svg">
            <path
              d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"
              fill="currentColor" />
          </svg>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row" v-loading="loading">
      <el-col :xs="24" :sm="12" :md="6" v-for="stat in stats" :key="stat.title">
        <div class="stat-card" :class="stat.type">
          <div class="stat-icon">
            <i :class="stat.icon"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(stat.value) }}</div>
            <div class="stat-title">{{ stat.title }}</div>
            <div class="stat-trend" :class="stat.trend > 0 ? 'up' : stat.trend < 0 ? 'down' : 'neutral'">
              <i
                :class="stat.trend > 0 ? 'el-icon-arrow-up' : stat.trend < 0 ? 'el-icon-arrow-down' : 'el-icon-minus'"></i>
              {{ Math.abs(stat.trend) }}%
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 赛事趋势图 -->
      <el-col :xs="24" :lg="24">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">
                <i class="el-icon-trend-charts"></i>
                赛事报名趋势
              </span>
              <el-button-group size="small">
                <el-button :type="timeRange === '7d' ? 'primary' : ''"
                  @click="timeRange = '7d'; handleTimeRangeChange()">7天</el-button>
                <el-button :type="timeRange === '30d' ? 'primary' : ''"
                  @click="timeRange = '30d'; handleTimeRangeChange()">30天</el-button>
                <el-button :type="timeRange === '90d' ? 'primary' : ''"
                  @click="timeRange = '90d'; handleTimeRangeChange()">90天</el-button>
              </el-button-group>
            </div>
          </template>
          <div class="chart-container" id="registrationTrend"></div>
        </el-card>
      </el-col>


    </el-row>

    <!-- 近期赛事列表 -->
    <el-row :gutter="20">
      <el-col :xs="24" :lg="12">
        <el-card class="recent-events-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">
                <i class="el-icon-calendar"></i>
                即将开始的赛事
              </span>
              <el-button type="text" @click="goToEvents">查看全部</el-button>
            </div>
          </template>
          <div class="events-list">
            <div v-for="event in upcomingEvents" :key="event.id" class="event-item">
              <div class="event-date">
                <div class="month">{{ formatMonth(event.startDate) }}</div>
                <div class="day">{{ formatDay(event.startDate) }}</div>
              </div>
              <div class="event-details">
                <h4>{{ event.name }}</h4>
                <p class="event-info">
                  <i class="el-icon-location"></i>{{ event.location }}
                  <i class="el-icon-user"></i>{{ event.participants }}/{{ event.maxParticipants }}人
                </p>
                <el-tag v-if="event.status === 'open'" type="success" size="small">
                  报名中
                </el-tag>
                <el-tag v-if="event.status === 'other'" type="warning" size="small">
                  即将开始
                </el-tag>
                <el-tag v-if="event.status === 'full'" type="warning" size="small">
                  报名人数已满
                </el-tag>
                <el-tag v-if="event.status === 'closed'" type="warning" size="small">
                  报名时间已过
                </el-tag>
                <el-tag v-if="event.status === 'completed'" type="warning" size="small">
                  赛事已结束
                </el-tag>
                <el-tag v-if="event.status === 'ongoing'" type="warning" size="small">
                  赛事进行中
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 热门赛事排行 -->
      <el-col :xs="24" :lg="12">
        <el-card class="popular-events-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">
                <i class="el-icon-trophy"></i>
                热门赛事排行
              </span>
            </div>
          </template>
          <div class="ranking-list">
            <div v-for="(event, index) in popularEvents" :key="event.id" class="ranking-item">
              <div class="rank-number" :class="`rank-${index + 1}`">{{ index + 1 }}</div>
              <div class="event-info">
                <h4>{{ event.name }}</h4>
                <p>报名人数: {{ event.registrations }}</p>
              </div>
              <div class="event-status">
                <el-progress :percentage="(event.registrations / event.maxParticipants) * 100" :show-text="false" />
                <span class="percentage">{{ Math.round((event.registrations / event.maxParticipants) * 100) }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>


  </div>
</template>

<script setup name="Index" lang="ts">
  import { onMounted, ref, computed, nextTick } from 'vue'
  import { useRouter } from 'vue-router'
  import * as echarts from 'echarts'
  import {
    getDashboardStatistics,
    getTrendData,
    getUpcomingEvents,
    getPopularEvents,
    getEventTypeDistribution
  } from '@/api/dashboard'
  import type {
    DashboardStatistics,
    TrendData,
    UpcomingEvent,
    PopularEvent,
    EventTypeDistribution
  } from '@/api/dashboard/types'

  const router = useRouter()

  // 响应式数据
  const timeRange = ref('30d')
  const loading = ref(false)

  // 统计数据
  const stats = ref([
    {
      title: '总赛事数',
      value: 0,
      trend: 0,
      icon: 'el-icon-trophy',
      type: 'primary'
    },
    {
      title: '总报名人数',
      value: 0,
      trend: 0,
      icon: 'el-icon-user',
      type: 'success'
    },
    {
      title: '进行中赛事',
      value: 0,
      trend: 0,
      icon: 'el-icon-time',
      type: 'warning'
    },
    {
      title: '完成赛事',
      value: 0,
      trend: 0,
      icon: 'el-icon-check',
      type: 'info'
    }
  ])

  // 趋势数据
  const trendData = ref<TrendData>({
    dates: [],
    registrations: [],
    events: []
  })

  // 即将开始的赛事
  const upcomingEvents = ref<UpcomingEvent[]>([])

  // 热门赛事排行
  const popularEvents = ref<PopularEvent[]>([])

  // 赛事类型分布数据
  const eventTypeDistribution = ref<EventTypeDistribution[]>([])

  // 快捷操作
  const quickActions = ref([
    {
      name: '创建赛事',
      icon: 'el-icon-plus',
      color: '#409EFF',
      action: () => router.push('/event/events')
    },
    {
      name: '报名管理',
      icon: 'el-icon-user',
      color: '#67C23A',
      action: () => router.push('/event/registrations')
    },
    {
      name: '成绩录入',
      icon: 'el-icon-edit',
      color: '#E6A23C',
      action: () => router.push('/event/results')
    },
    {
      name: '数据统计',
      icon: 'el-icon-data-analysis',
      color: '#F56C6C',
      action: () => router.push('/event/statistics')
    },
    {
      name: '用户管理',
      icon: 'el-icon-setting',
      color: '#909399',
      action: () => router.push('/event/eventUsers')
    },
    {
      name: '支付管理',
      icon: 'el-icon-money',
      color: '#73D13D',
      action: () => router.push('/event/payments')
    }
  ])

  // 工具函数
  const formatNumber = (num : number) : string => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return num.toString()
  }

  const formatMonth = (dateStr : string) : string => {
    const date = new Date(dateStr)
    return (date.getMonth() + 1) + '月'
  }

  const formatDay = (dateStr : string) : string => {
    const date = new Date(dateStr)
    return date.getDate().toString()
  }

  // 数据获取函数
  const loadDashboardStatistics = async () => {
    try {
      loading.value = true
      const { data } = await getDashboardStatistics(timeRange.value)

      // 更新统计数据
      stats.value[0].value = data.totalEvents.value
      stats.value[0].trend = data.totalEvents.trend

      stats.value[1].value = data.totalRegistrations.value
      stats.value[1].trend = data.totalRegistrations.trend

      stats.value[2].value = data.ongoingEvents.value
      stats.value[2].trend = data.ongoingEvents.trend

      stats.value[3].value = data.completedEvents.value
      stats.value[3].trend = data.completedEvents.trend
    } catch (error) {
      console.error('获取统计数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  const loadTrendData = async () => {
    try {
      const { data } = await getTrendData(timeRange.value)
      trendData.value = data

      // 重新初始化图表
      nextTick(() => {
        initRegistrationTrendChart()
      })
    } catch (error) {
      console.error('获取趋势数据失败:', error)
    }
  }

  const loadUpcomingEvents = async () => {
    try {
      const { data } = await getUpcomingEvents(4)
      upcomingEvents.value = data
    } catch (error) {
      console.error('获取即将开始的赛事失败:', error)
    }
  }

  const loadPopularEvents = async () => {
    try {
      const { data } = await getPopularEvents(5)
      popularEvents.value = data
    } catch (error) {
      console.error('获取热门赛事失败:', error)
    }
  }

  const loadEventTypeDistribution = async () => {
    try {
      const { data } = await getEventTypeDistribution()
      eventTypeDistribution.value = data

      // 重新初始化饼图
      nextTick(() => {
        initEventTypePieChart()
      })
    } catch (error) {
      console.error('获取赛事类型分布失败:', error)
    }
  }

  // 加载所有数据
  const loadAllData = async () => {
    await Promise.all([
      loadDashboardStatistics(),
      loadTrendData(),
      loadUpcomingEvents(),
      loadPopularEvents(),
      loadEventTypeDistribution()
    ])
  }

  // 事件处理
  const goToEvents = () => {
    router.push('/event/events')
  }

  const handleQuickAction = (action : any) => {
    if (action.action) {
      action.action()
    }
  }

  // 图表初始化
  const initRegistrationTrendChart = () => {
    const chartDom = document.getElementById('registrationTrend')
    if (!chartDom) return

    const myChart = echarts.init(chartDom)
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: trendData.value.dates
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '报名人数',
          type: 'line',
          stack: 'Total',
          smooth: true,
          lineStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ])
          },
          data: trendData.value.registrations
        },
        {
          name: '赛事数量',
          type: 'line',
          stack: 'Total',
          smooth: true,
          lineStyle: {
            color: '#67C23A'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
              { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
            ])
          },
          data: trendData.value.events
        }
      ]
    }
    myChart.setOption(option)

    // 响应式
    window.addEventListener('resize', () => {
      myChart.resize()
    })
  }

  const initEventTypePieChart = () => {
    const chartDom = document.getElementById('eventTypePie')
    if (!chartDom) return

    const myChart = echarts.init(chartDom)
    const option = {
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '赛事类型',
          type: 'pie',
          radius: '50%',
          data: eventTypeDistribution.value.map(item => ({
            value: item.value,
            name: item.name
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    myChart.setOption(option)

    // 响应式
    window.addEventListener('resize', () => {
      myChart.resize()
    })
  }

  // 监听时间范围变化
  const handleTimeRangeChange = async () => {
    await loadDashboardStatistics()
    await loadTrendData()
  }

  // 生命周期
  onMounted(() => {
    // 加载所有数据
    loadAllData()
  })
</script>

<style lang="scss" scoped>
  .dashboard {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: 100vh;
  }

  // 欢迎横幅
  .welcome-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 40px;
    margin-bottom: 30px;
    color: white;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      transform: rotate(45deg);
    }

    .banner-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      z-index: 2;

      .banner-text {
        flex: 1;

        h1 {
          font-size: 2.5rem;
          font-weight: 700;
          margin: 0 0 10px 0;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        }

        p {
          font-size: 1.1rem;
          opacity: 0.9;
          margin: 0;
          font-weight: 300;
        }
      }

      .banner-icon {
        .sports-svg {
          width: 80px;
          height: 80px;
          opacity: 0.7;
        }
      }
    }

    @media (max-width: 768px) {
      padding: 20px;

      .banner-content {
        flex-direction: column;
        text-align: center;

        .banner-text h1 {
          font-size: 1.8rem;
        }

        .banner-icon {
          margin-top: 20px;

          .sports-svg {
            width: 60px;
            height: 60px;
          }
        }
      }
    }
  }

  // 统计卡片
  .stats-row {
    margin-bottom: 30px;

    .stat-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      border-left: 4px solid #409EFF;
      transition: all 0.3s ease;
      margin-bottom: 20px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      }

      &.primary {
        border-left-color: #409EFF;
      }

      &.success {
        border-left-color: #67C23A;
      }

      &.warning {
        border-left-color: #E6A23C;
      }

      &.info {
        border-left-color: #909399;
      }

      display: flex;
      align-items: center;

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;

        i {
          font-size: 24px;
          color: white;
        }
      }

      .stat-content {
        flex: 1;

        .stat-number {
          font-size: 2rem;
          font-weight: 700;
          color: #2c3e50;
          margin-bottom: 5px;
        }

        .stat-title {
          font-size: 0.9rem;
          color: #7f8c8d;
          margin-bottom: 8px;
        }

        .stat-trend {
          font-size: 0.8rem;
          display: flex;
          align-items: center;

          &.up {
            color: #67C23A;
          }

          &.down {
            color: #F56C6C;
          }

          &.neutral {
            color: #909399;
          }

          i {
            margin-right: 4px;
          }
        }
      }
    }
  }

  // 图表区域
  .charts-row {
    margin-bottom: 30px;

    .chart-card {
      .chart-container {
        height: 400px;
        width: 100%;
      }
    }
  }

  // 卡片通用样式
  .el-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    :deep(.el-card__header) {
      border-bottom: 1px solid #f0f2f5;
      padding: 20px;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: #409EFF;
        }
      }
    }
  }

  // 赛事列表
  .recent-events-card,
  .popular-events-card {
    margin-bottom: 20px;

    .events-list,
    .ranking-list {
      max-height: 400px;
      overflow-y: auto;
    }

    .event-item {
      display: flex;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid #f0f2f5;

      &:last-child {
        border-bottom: none;
      }

      .event-date {
        width: 60px;
        text-align: center;
        margin-right: 16px;

        .month {
          font-size: 0.8rem;
          color: #909399;
          line-height: 1;
        }

        .day {
          font-size: 1.5rem;
          font-weight: 700;
          color: #409EFF;
          line-height: 1;
        }
      }

      .event-details {
        flex: 1;

        h4 {
          margin: 0 0 8px 0;
          font-size: 1rem;
          color: #2c3e50;
        }

        .event-info {
          margin: 8px 0;
          font-size: 0.85rem;
          color: #7f8c8d;

          i {
            margin-right: 4px;
            margin-left: 12px;

            &:first-child {
              margin-left: 0;
            }
          }
        }
      }
    }

    .ranking-item {
      display: flex;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid #f0f2f5;

      &:last-child {
        border-bottom: none;
      }

      .rank-number {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        margin-right: 16px;
        color: white;

        &.rank-1 {
          background: linear-gradient(135deg, #FFD700, #FFA500);
        }

        &.rank-2 {
          background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
        }

        &.rank-3 {
          background: linear-gradient(135deg, #CD7F32, #B8860B);
        }

        &:not(.rank-1):not(.rank-2):not(.rank-3) {
          background: #909399;
        }
      }

      .event-info {
        flex: 1;

        h4 {
          margin: 0 0 8px 0;
          font-size: 1rem;
          color: #2c3e50;
        }

        p {
          margin: 0;
          font-size: 0.85rem;
          color: #7f8c8d;
        }
      }

      .event-status {
        width: 100px;
        text-align: right;

        .percentage {
          font-size: 0.8rem;
          color: #7f8c8d;
          margin-top: 4px;
          display: block;
        }
      }
    }
  }

  // 快捷操作
  .quick-actions {
    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: 20px;

      .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        border-radius: 12px;
        background: #f8f9fa;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: #e9ecef;
          transform: translateY(-2px);
        }

        .action-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 12px;

          i {
            font-size: 20px;
            color: white;
          }
        }

        span {
          font-size: 0.9rem;
          color: #2c3e50;
          font-weight: 500;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .dashboard {
      padding: 10px;
    }

    .stats-row .stat-card {
      .stat-icon {
        width: 50px;
        height: 50px;
        margin-right: 15px;

        i {
          font-size: 20px;
        }
      }

      .stat-content .stat-number {
        font-size: 1.5rem;
      }
    }

    .charts-row .chart-card .chart-container {
      height: 300px;
    }

    .quick-actions .actions-grid {
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 15px;

      .action-item {
        padding: 15px;

        .action-icon {
          width: 40px;
          height: 40px;

          i {
            font-size: 16px;
          }
        }

        span {
          font-size: 0.8rem;
        }
      }
    }
  }

  // 自定义滚动条
  .events-list,
  .ranking-list {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
</style>
