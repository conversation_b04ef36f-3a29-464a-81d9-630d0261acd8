<template>
  <div class="login">
    <!-- 左侧图片区域 -->
    <div class="login-left">
      <div class="sports-content">
        <div class="sports-icon">
          <div class="icon-container">
            <div class="sports-circle">
              <!-- <svg viewBox="0 0 24 24" class="trophy-icon">
                <path d="M7,3H17A1,1 0 0,1 18,4V6A4,4 0 0,1 14,10H13V17A1,1 0 0,1 12,18H8A1,1 0 0,1 7,17V10H6A4,4 0 0,1 2,6V4A1,1 0 0,1 3,3H7M7,6V8H6A2,2 0 0,0 4,6V5H7M17,6V5H20V6A2,2 0 0,0 18,8H17V6M9,20H11V22H9V20Z" fill="currentColor"/>
              </svg> -->
              <el-image class="circle-image " src="src/assets/logo/logo.png"></el-image>
            </div>
          </div>
        </div>
        <h2 class="sports-title">星火链网·报名系统</h2>
        <!-- <p class="sports-subtitle">激发运动激情，超越自我极限</p> -->
        <div class="sports-features">
          <div class="feature-item">
            <div class="feature-icon">🏃‍♂️</div>
            <span>赛事报名/培训报名</span>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🏆</div>
            <span>成绩管理</span>
          </div>
          <div class="feature-item">
            <div class="feature-icon">⚡</div>
            <span>实时更新</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧登录区域 -->
    <div class="login-right">
      <div class="login-container">
        <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
          <div class="title-box">
            <h3 class="title">欢迎登录</h3>
            <lang-select />
          </div>
          <el-form-item v-if="tenantEnabled" prop="tenantId">
            <el-select v-model="loginForm.tenantId" filterable :placeholder="proxy.$t('login.selectPlaceholder')"
              style="width: 100%">
              <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName"
                :value="item.tenantId"></el-option>
              <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
            </el-select>
          </el-form-item>
          <el-form-item prop="username">
            <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off"
              :placeholder="proxy.$t('login.username')">
              <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input v-model="loginForm.password" type="password" size="large" auto-complete="off"
              :placeholder="proxy.$t('login.password')" @keyup.enter="handleLogin">
              <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
            </el-input>
          </el-form-item>
          <el-form-item v-if="captchaEnabled" prop="code">
            <el-input v-model="loginForm.code" size="large" auto-complete="off" :placeholder="proxy.$t('login.code')"
              style="width: 63%" @keyup.enter="handleLogin">
              <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" class="login-code-img" @click="getCode" />
            </div>
          </el-form-item>
          <el-checkbox v-model="loginForm.rememberMe"
            style="margin: 0 0 25px 0">{{ proxy.$t('login.rememberPassword') }}</el-checkbox>
          <!-- <el-form-item style="float: right">
            <el-button circle :title="proxy.$t('login.social.wechat')" @click="doSocialLogin('wechat')">
              <svg-icon icon-class="wechat" />
            </el-button>
            <el-button circle :title="proxy.$t('login.social.maxkey')" @click="doSocialLogin('maxkey')">
              <svg-icon icon-class="maxkey" />
            </el-button>
            <el-button circle :title="proxy.$t('login.social.topiam')" @click="doSocialLogin('topiam')">
              <svg-icon icon-class="topiam" />
            </el-button>
            <el-button circle :title="proxy.$t('login.social.gitee')" @click="doSocialLogin('gitee')">
              <svg-icon icon-class="gitee" />
            </el-button>
            <el-button circle :title="proxy.$t('login.social.github')" @click="doSocialLogin('github')">
              <svg-icon icon-class="github" />
            </el-button>
          </el-form-item> -->
          <el-form-item style="width: 100%">
            <el-button :loading="loading" size="large" type="primary" style="width: 100%" @click.prevent="handleLogin">
              <span v-if="!loading">{{ proxy.$t('login.login') }}</span>
              <span v-else>{{ proxy.$t('login.logging') }}</span>
            </el-button>
            <div v-if="register" style="float: right">
              <router-link class="link-type" :to="'/register'">{{ proxy.$t('login.switchRegisterPage') }}</router-link>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2025 广西星火链数字科技有限公司 Li All Rights Reserved.</span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { getCodeImg, getTenantList } from '@/api/login';
  import { authBinding } from '@/api/system/social/auth';
  import { useUserStore } from '@/store/modules/user';
  import { LoginData, TenantVO } from '@/api/types';
  import { to } from 'await-to-js';
  import { HttpStatus } from '@/enums/RespEnum';
  import { useI18n } from 'vue-i18n';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;

  const title = import.meta.env.VITE_APP_TITLE;
  const userStore = useUserStore();
  const router = useRouter();
  const { t } = useI18n();

  const loginForm = ref<LoginData>({
    tenantId: '040434',
    username: '',
    password: '',
    rememberMe: false,
    code: '',
    uuid: ''
  } as LoginData);

  // 密码复杂度验证函数
  const validatePasswordComplexity = (rule : any, value : string, callback : any) => {
    if (!value) {
      callback(new Error(t('login.rule.password.required')));
      return;
    }

    // 密码复杂度规则：至少8位，包含大写字母、小写字母、数字
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(value);
    const hasLowerCase = /[a-z]/.test(value);
    const hasNumber = /\d/.test(value);

    if (value.length < minLength || !hasUpperCase || !hasLowerCase || !hasNumber) {
      callback(new Error(t('login.rule.password.complexity')));
      return;
    }

    callback();
  };

  const loginRules : ElFormRules = {
    tenantId: [{ required: true, trigger: 'blur', message: t('login.rule.tenantId.required') }],
    username: [{ required: true, trigger: 'blur', message: t('login.rule.username.required') }],
    password: [
      { required: true, trigger: 'blur', message: t('login.rule.password.required') }
      // { validator: validatePasswordComplexity, trigger: 'blur' }
    ],
    code: [{ required: true, trigger: 'change', message: t('login.rule.code.required') }]
  };

  const codeUrl = ref('');
  const loading = ref(false);
  // 验证码开关
  const captchaEnabled = ref(true);
  // 租户开关
  const tenantEnabled = ref(true);

  // 注册开关
  const register = ref(false);
  const redirect = ref('/');
  const loginRef = ref<ElFormInstance>();
  // 租户列表
  const tenantList = ref<TenantVO[]>([]);

  watch(
    () => router.currentRoute.value,
    (newRoute : any) => {
      redirect.value = newRoute.query && newRoute.query.redirect && decodeURIComponent(newRoute.query.redirect);
    },
    { immediate: true }
  );

  const handleLogin = () => {
    loginRef.value?.validate(async (valid : boolean, fields : any) => {
      if (valid) {
        loading.value = true;
        // 勾选了需要记住密码设置在 localStorage 中设置记住用户名和密码
        if (loginForm.value.rememberMe) {
          localStorage.setItem('tenantId', String(loginForm.value.tenantId));
          localStorage.setItem('username', String(loginForm.value.username));
          localStorage.setItem('password', String(loginForm.value.password));
          localStorage.setItem('rememberMe', String(loginForm.value.rememberMe));
        } else {
          // 否则移除
          localStorage.removeItem('tenantId');
          localStorage.removeItem('username');
          localStorage.removeItem('password');
          localStorage.removeItem('rememberMe');
        }
        // 调用action的登录方法
        const [err] = await to(userStore.login(loginForm.value));
        if (!err) {
          const redirectUrl = redirect.value || '/';
          await router.push(redirectUrl);
          loading.value = false;
        } else {
          loading.value = false;
          // 重新获取验证码
          if (captchaEnabled.value) {
            await getCode();
          }
        }
      } else {
        console.log('error submit!', fields);
      }
    });
  };

  /**
   * 获取验证码
   */
  const getCode = async () => {
    const res = await getCodeImg();
    const { data } = res;
    captchaEnabled.value = data.captchaEnabled === undefined ? true : data.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = 'data:image/gif;base64,' + data.img;
      loginForm.value.uuid = data.uuid;
    }
  };

  const getLoginData = () => {
    const tenantId = localStorage.getItem('tenantId');
    const username = localStorage.getItem('username');
    const password = localStorage.getItem('password');
    const rememberMe = localStorage.getItem('rememberMe');
    loginForm.value = {
      tenantId: tenantId === null ? String(loginForm.value.tenantId) : tenantId,
      username: username === null ? String(loginForm.value.username) : username,
      password: password === null ? String(loginForm.value.password) : String(password),
      rememberMe: rememberMe === null ? false : Boolean(rememberMe)
    } as LoginData;
  };

  /**
   * 获取租户列表
   */
  const initTenantList = async () => {
    const { data } = await getTenantList(false);
    tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled;
    if (tenantEnabled.value) {
      tenantList.value = data.voList;
      if (tenantList.value != null && tenantList.value.length !== 0) {
        loginForm.value.tenantId = tenantList.value[0].tenantId;
      }
    }
  };

  /**
   * 第三方登录
   * @param type
   */
  const doSocialLogin = (type : string) => {
    authBinding(type, loginForm.value.tenantId).then((res : any) => {
      if (res.code === HttpStatus.SUCCESS) {
        // 获取授权地址跳转
        window.location.href = res.data;
      } else {
        ElMessage.error(res.msg);
      }
    });
  };

  onMounted(() => {
    getCode();
    initTenantList();
    getLoginData();
  });
</script>

<style lang="scss" scoped>
  .login {
    display: flex;
    height: 100vh;
    width: 100vw;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
    position: relative;
    overflow: hidden;
  }

  // 左侧图片区域
  .login-left {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 50%, #03a9f4 100%);
    position: relative;
    min-height: 100vh;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    }

    .sports-content {
      text-align: center;
      color: white;
      z-index: 2;
      position: relative;
      padding: 2rem;
      max-width: 500px;

      .sports-icon {
        margin-bottom: 2rem;

        .icon-container {
          display: inline-block;
          position: relative;

          .sports-circle {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

            .circle-image {
              width: 90%;
              /* 图片宽度为容器的80%，留有内边距 */
              height: 90%;
              /* 图片高度为容器的80%，留有内边距 */
              border-radius: 50%;
              /* 将图片裁剪为圆形 */
              object-fit: cover;
              /* 保持图片比例并填充容器 */
            }

            .trophy-icon {
              width: 60px;
              height: 60px;
              color: #fff;
            }
          }
        }
      }

      .sports-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
      }

      .sports-subtitle {
        font-size: 1.2rem;
        margin-bottom: 3rem;
        opacity: 0.9;
        font-weight: 300;
      }

      .sports-features {
        display: flex;
        justify-content: center;
        gap: 2rem;
        flex-wrap: wrap;

        .feature-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;
          padding: 1rem;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          min-width: 100px;

          .feature-icon {
            font-size: 1.5rem;
          }

          span {
            font-size: 0.9rem;
            font-weight: 500;
          }
        }
      }
    }
  }

  // 右侧登录区域
  .login-right {
    flex: 0 0 480px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
    position: relative;

    .login-container {
      width: 100%;
      max-width: 400px;
      padding: 2rem;
    }
  }

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;

    .title {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 600;
      color: #1976d2;
      flex: 1;
    }

    :deep(.lang-select--style) {
      line-height: 0;
      color: #1976d2;
    }
  }

  .login-form {
    width: 100%;

    .el-input {
      height: 48px;
      margin-bottom: 8px;

      input {
        height: 48px;
        border: 2px solid #e3f2fd;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:focus {
          border-color: #2196f3;
          box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }
      }
    }

    .el-select {
      width: 100%;

      .el-input__wrapper {
        border: 2px solid #e3f2fd;
        border-radius: 8px;

        &:hover {
          border-color: #90caf9;
        }

        &.is-focus {
          border-color: #2196f3;
          box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }
      }
    }

    .el-button--primary {
      background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
      border: none;
      border-radius: 8px;
      height: 48px;
      font-size: 1rem;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
      }
    }

    .el-checkbox {
      color: #666;

      :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
        background-color: #2196f3;
        border-color: #2196f3;
      }
    }

    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 0px;
      color: #1976d2;
    }
  }

  .login-code {
    width: 33%;
    height: 48px;
    float: right;

    img {
      cursor: pointer;
      vertical-align: middle;
      border-radius: 6px;
      border: 2px solid #e3f2fd;
    }
  }

  .login-code-img {
    height: 44px;
    padding-left: 12px;
  }

  .el-login-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: rgba(33, 150, 243, 0.9);
    color: #fff;
    font-family: Arial, serif;
    font-size: 12px;
    letter-spacing: 1px;
    backdrop-filter: blur(10px);
    z-index: 1000;
  }

  // 响应式设计
  @media (max-width: 1024px) {
    .login-right {
      flex: 0 0 400px;
    }

    .login-left .sports-content {
      padding: 1.5rem;

      .sports-title {
        font-size: 2rem;
      }

      .sports-features {
        gap: 1rem;
      }
    }
  }

  @media (max-width: 768px) {
    .login {
      flex-direction: column;
    }

    .login-left {
      flex: 0 0 40vh;
      min-height: 40vh;

      .sports-content {
        padding: 1rem;

        .sports-title {
          font-size: 1.5rem;
        }

        .sports-subtitle {
          font-size: 1rem;
          margin-bottom: 1.5rem;
        }

        .sports-icon .icon-container .sports-circle {
          width: 80px;
          height: 80px;
.circle-image {
              width: 90%;
              /* 图片宽度为容器的80%，留有内边距 */
              height: 90%;
              /* 图片高度为容器的80%，留有内边距 */
              border-radius: 50%;
              /* 将图片裁剪为圆形 */
              object-fit: cover;
              /* 保持图片比例并填充容器 */
            }
          .trophy-icon {
            width: 40px;
            height: 40px;
          }
        }

        .sports-features {
          flex-direction: row;
          gap: 0.5rem;

          .feature-item {
            padding: 0.5rem;
            min-width: 80px;

            .feature-icon {
              font-size: 1rem;
            }

            span {
              font-size: 0.8rem;
            }
          }
        }
      }
    }

    .login-right {
      flex: 1;
      box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);

      .login-container {
        padding: 1.5rem;
      }
    }

    .title-box {
      .title {
        font-size: 1.5rem;
      }
    }
  }

  @media (max-width: 480px) {
    .login-right .login-container {
      padding: 1rem;
    }

    .login-form {
      .el-input {
        height: 44px;

        input {
          height: 44px;
        }
      }

      .el-button--primary {
        height: 44px;
      }
    }

    .login-code {
      height: 44px;
    }

    .login-code-img {
      height: 40px;
    }
  }

  // 动画效果
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .sports-content {
    animation: fadeInLeft 0.8s ease-out;
  }

  .login-form {
    animation: fadeInUp 0.8s ease-out 0.2s both;
  }
</style>
