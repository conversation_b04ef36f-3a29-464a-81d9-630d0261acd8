<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" label-width="100px" :model="queryParams" :inline="true">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="职位" prop="position">
              <el-select v-model="queryParams.position" placeholder="请选择职位" clearable>
                <el-option v-for="dict in sh_ry_zw" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="电话" prop="phone">
              <el-input v-model="queryParams.phone" placeholder="请输入电话" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                <el-option v-for="dict in sh_kf_status" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['event:customerService:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              v-hasPermi="['event:customerService:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['event:customerService:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['event:customerService:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="customerServiceList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" width="80" align="center" />

        <el-table-column label="基本信息" align="left" min-width="200">
          <template #default="scope">
            <div class="customer-info">
              <div class="name">{{ scope.row.name }}</div>
              <div class="position">
                <dict-tag :options="sh_ry_zw" :value="scope.row.position" />
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="联系方式" align="left" min-width="250">
          <template #default="scope">
            <div class="contact-info">
              <div class="contact-item" v-if="scope.row.phone">
                <el-icon>
                  <Phone />
                </el-icon>
                <span>{{ scope.row.phone }}</span>
              </div>
              <div class="contact-item" v-if="scope.row.qq">
                <el-icon>
                  <ChatDotRound />
                </el-icon>
                <span>QQ: {{ scope.row.qq }}</span>
              </div>
              <div class="contact-item" v-if="scope.row.email">
                <el-icon>
                  <Message />
                </el-icon>
                <span>{{ scope.row.email }}</span>
              </div>
              <div class="contact-item" v-if="scope.row.wechat">
                <el-icon>
                  <WechatFilled />
                </el-icon>
                <span>微信: {{ scope.row.wechat }}</span>
              </div>
              <div class="contact-item" v-if="scope.row.otherContact">
                <el-icon>
                  <More />
                </el-icon>
                <span>{{ scope.row.otherContact }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" align="center" width="100">
          <template #default="scope">
            <dict-tag :options="sh_kf_status" :value="scope.row.status" />
          </template>
        </el-table-column>

        <el-table-column label="排序" align="center" width="80" prop="sortOrder" />

        <el-table-column label="创建时间" align="center" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="200" fixed="right">
          <template #default="scope">
            <el-button-group size="small">
              <el-tooltip content="修改" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                  v-hasPermi="['event:customerService:edit']"></el-button>
              </el-tooltip>

              <el-tooltip content="查看详情" placement="top">
                <el-button link type="info" icon="View" @click="handleView(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                  v-hasPermi="['event:customerService:remove']"></el-button>
              </el-tooltip>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改客服管理对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body
      class="customer-service-dialog">
      <el-form ref="customerServiceFormRef" :model="form" :rules="rules" label-width="100px"
        class="customer-service-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name" required>
              <el-input v-model="form.name" placeholder="请输入姓名" maxlength="50" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="position" required>
              <el-select v-model="form.position" placeholder="请选择职位" style="width: 100%">
                <el-option v-for="dict in sh_ry_zw" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="电话" prop="phone" required>
              <el-input v-model="form.phone" placeholder="请输入电话号码" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="QQ号" prop="qq">
              <el-input v-model="form.qq" placeholder="请输入QQ号码" maxlength="20" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱地址" maxlength="100" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="微信号" prop="wechat">
              <el-input v-model="form.wechat" placeholder="请输入微信号" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                <el-option v-for="dict in sh_kf_status" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" :min="0" placeholder="数字越小越靠前" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="其他联系方式" prop="otherContact">
          <el-input v-model="form.otherContact" placeholder="请输入其他联系方式" maxlength="200" show-word-limit />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" maxlength="500"
            show-word-limit />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">
            <el-icon>
              <Check />
            </el-icon>
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="客服详情" v-model="detailDialog.visible" width="600px" append-to-body
      class="customer-service-detail-dialog">
      <div class="detail-content">
        <!-- 基础信息 -->
        <div class="detail-section">
          <h4 class="section-title">
            <el-icon>
              <User />
            </el-icon>
            基础信息
          </h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="姓名" label-width="100px">
              <span class="name-text">{{ detailDialog.detail.name }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="职位" label-width="100px">
              <dict-tag :options="sh_ry_zw" :value="detailDialog.detail.position" />
            </el-descriptions-item>
            <el-descriptions-item label="状态" label-width="100px">
              <dict-tag :options="sh_kf_status" :value="detailDialog.detail.status" />
            </el-descriptions-item>
            <el-descriptions-item label="排序" label-width="100px">
              {{ detailDialog.detail.sortOrder || 0 }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 联系信息 -->
        <div class="detail-section">
          <h4 class="section-title">
            <el-icon>
              <Phone />
            </el-icon>
            联系信息
          </h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="电话" label-width="100px">
              <span class="contact-text">{{ detailDialog.detail.phone || '未填写' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="QQ号" label-width="100px">
              <span class="contact-text">{{ detailDialog.detail.qq || '未填写' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="邮箱" label-width="100px">
              <span class="contact-text">{{ detailDialog.detail.email || '未填写' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="微信号" label-width="100px">
              <span class="contact-text">{{ detailDialog.detail.wechat || '未填写' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="其他联系方式" :span="2" label-width="100px">
              <span class="contact-text">{{ detailDialog.detail.otherContact || '未填写' }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 备注信息 -->
        <div class="detail-section" v-if="detailDialog.detail.remark">
          <h4 class="section-title">
            <el-icon>
              <Document />
            </el-icon>
            备注信息
          </h4>
          <div class="remark-content">
            {{ detailDialog.detail.remark }}
          </div>
        </div>

        <!-- 创建信息 -->
        <div class="detail-section">
          <h4 class="section-title">
            <el-icon>
              <InfoFilled />
            </el-icon>
            创建信息
          </h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="创建时间" label-width="100px">
              {{ parseTime(detailDialog.detail.createTime, '{y}-{m}-{d} {h}:{i}:{s}') || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间" label-width="100px">
              {{ parseTime(detailDialog.detail.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') || '未更新' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建人" label-width="100px">
              {{ detailDialog.detail.createBy || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="更新人" label-width="100px">
              {{ detailDialog.detail.updateBy || '无' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialog.visible = false">关 闭</el-button>
          <el-button type="primary" @click="handleUpdate(detailDialog.detail)"
            v-hasPermi="['event:customerService:edit']">
            <el-icon>
              <Edit />
            </el-icon>
            编 辑
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CustomerService" lang="ts">
  import {
    listCustomerService,
    getCustomerService,
    delCustomerService,
    addCustomerService,
    updateCustomerService,
    exportCustomerService
  } from '@/api/system/customerService';
  import { CustomerServiceVO, CustomerServiceQuery, CustomerServiceForm } from '@/api/system/customerService/types';

  const { proxy } = getCurrentInstance() as ComponentInternalInstance;
  const { sh_kf_status, sh_ry_zw } = toRefs<any>(proxy?.useDict('sh_kf_status', 'sh_ry_zw'));


  const customerServiceList = ref<CustomerServiceVO[]>([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref<Array<string | number>>([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);

  const queryFormRef = ref<ElFormInstance>();
  const customerServiceFormRef = ref<ElFormInstance>();

  const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
  });

  const detailDialog = reactive({
    visible: false,
    detail: {} as CustomerServiceVO
  });

  const initFormData : CustomerServiceForm = {
    id: undefined,
    name: '',
    position: '',
    phone: '',
    qq: '',
    email: '',
    wechat: '',
    otherContact: '',
    status: 1,
    sortOrder: 0,
    remark: ''
  };

  const data = reactive<PageData<CustomerServiceForm, CustomerServiceQuery>>({
    form: { ...initFormData },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      name: undefined,
      position: undefined,
      phone: undefined,
      status: undefined
    },
    rules: {
      name: [
        { required: true, message: "姓名不能为空", trigger: "blur" },
        { min: 2, max: 50, message: "姓名长度应在2-50个字符之间", trigger: "blur" }
      ],
      position: [
        { required: true, message: "职位不能为空", trigger: "change" }
      ],
      phone: [
        { required: true, message: "电话不能为空", trigger: "blur" },
        { pattern: /^1[3456789]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
      ],
      email: [
        { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }
      ],
      status: [
        { required: true, message: "状态不能为空", trigger: "change" }
      ]
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询客服管理列表 */
  const getList = async () => {
    loading.value = true;
    try {
      const res = await listCustomerService(queryParams.value);
      customerServiceList.value = res.rows;
      total.value = res.total;
    } catch (error) {
      console.error('获取客服列表失败:', error);
      proxy?.$modal.msgError('获取客服列表失败');
    } finally {
      loading.value = false;
    }
  };

  /** 取消按钮 */
  const cancel = () => {
    reset();
    dialog.visible = false;
  };

  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    customerServiceFormRef.value?.resetFields();
  };

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  };

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
  };

  /** 多选框选中数据 */
  const handleSelectionChange = (selection : CustomerServiceVO[]) => {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
  };

  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = "添加客服";
  };

  /** 修改按钮操作 */
  const handleUpdate = async (row ?: CustomerServiceVO) => {
    reset();
    const _id = row?.id || ids.value[0];
    try {
      const res = await getCustomerService(_id);
      Object.assign(form.value, res.data);
      dialog.visible = true;
      dialog.title = "修改客服";
    } catch (error) {
      console.error('获取客服详情失败:', error);
      proxy?.$modal.msgError('获取客服详情失败');
    }
  };

  /** 提交按钮 */
  const submitForm = () => {
    customerServiceFormRef.value?.validate(async (valid : boolean) => {
      if (valid) {
        buttonLoading.value = true;
        try {
          if (form.value.id) {
            await updateCustomerService(form.value);
            proxy?.$modal.msgSuccess("修改成功");
          } else {
            await addCustomerService(form.value);
            proxy?.$modal.msgSuccess("新增成功");
          }
          dialog.visible = false;
          await getList();
        } catch (error) {
          console.error('操作失败:', error);
          proxy?.$modal.msgError("操作失败");
        } finally {
          buttonLoading.value = false;
        }
      }
    });
  };

  /** 删除按钮操作 */
  const handleDelete = async (row ?: CustomerServiceVO) => {
    const _ids = row?.id || ids.value;
    try {
      await proxy?.$modal.confirm('是否确认删除客服编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
      await delCustomerService(_ids);
      proxy?.$modal.msgSuccess("删除成功");
      await getList();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error);
        proxy?.$modal.msgError('删除失败');
      }
    }
  };

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy?.download('system/customerService/export', {
      ...queryParams.value
    }, `客服管理_${new Date().getTime()}.xlsx`);
  };

  /** 查看详情 */
  const handleView = async (row : CustomerServiceVO) => {
    try {
      const res = await getCustomerService(row.id);
      detailDialog.detail = res.data;
      detailDialog.visible = true;
    } catch (error) {
      console.error('获取详情失败:', error);
      proxy?.$modal.msgError('获取详情失败');
    }
  };

  /** 获取职位标签类型 */
  const getPositionType = (position : string) : string => {
    const typeMap : Record<string, string> = {
      '客服': 'primary',
      '运营': 'success',
      '技术': 'warning'
    };
    return typeMap[position] || 'info';
  };

  onMounted(() => {
    getList();
  });
</script>

<style lang="scss" scoped>
  .customer-info {
    text-align: left;

    .name {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 4px;
      font-size: 14px;
    }

    .position {
      .el-tag {
        margin-top: 2px;
      }
    }
  }

  .contact-info {
    text-align: left;

    .contact-item {
      display: flex;
      align-items: center;
      margin-bottom: 3px;
      font-size: 12px;
      color: #606266;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }

      .el-icon {
        margin-right: 6px;
        color: #409eff;
        font-size: 14px;
        flex-shrink: 0;
      }

      span {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  :deep(.customer-service-dialog) {
    .el-dialog__body {
      padding: 20px 30px;
    }

    .customer-service-form {
      .el-form-item {
        margin-bottom: 20px;
      }
    }
  }

  :deep(.customer-service-detail-dialog) {
    .el-dialog__body {
      padding: 20px 30px;
      max-height: 70vh;
      overflow-y: auto;
    }

    .detail-section {
      margin-bottom: 25px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0 0 15px 0;
        padding-bottom: 8px;
        border-bottom: 2px solid #409eff;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .remark-content {
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e1e6f0;
        color: #606266;
        line-height: 1.6;
      }

      .name-text {
        font-weight: 600;
        color: #2c3e50;
      }

      .contact-text {
        font-family: 'Monaco', 'Consolas', monospace;
        color: #409eff;
      }
    }
  }

  .dialog-footer {
    .el-button {
      min-width: 100px;
      margin-left: 10px;

      &:first-child {
        margin-left: 0;
      }
    }

    .el-button--primary {
      background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
      border: none;
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);

      &:hover {
        background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%);
        box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {

    :deep(.customer-service-dialog),
    :deep(.customer-service-detail-dialog) {
      .el-dialog {
        width: 95% !important;
        margin: 20px auto;
      }

      .el-dialog__body {
        padding: 15px 20px;
      }
    }

    .customer-info .name {
      font-size: 13px;
    }

    .contact-info .contact-item {
      font-size: 11px;
    }
  }
</style>
