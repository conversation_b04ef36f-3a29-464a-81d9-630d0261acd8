# 奖状/证书模板管理功能设计文档

## 1. 项目概述

### 1.1 功能描述
为事项管理系统增加奖状/证书模板管理功能，支持：
- 奖状/证书模板管理（新增、编辑、删除、启用/停用、设为默认）
- 模板参数配置（表单化配置参数用于内容替换）
- 批量生成证书/奖状
- 证书下载和批量下载
- 生成记录管理

### 1.2 技术栈
- **前端**: Vue 3 + TypeScript + Element Plus
- **后端**: Spring Boot + MyBatis Plus
- **数据库**: MySQL 8.0+
- **文件存储**: OSS对象存储

## 2. 数据库设计

### 2.1 表结构设计

#### 2.1.1 奖状/证书模板表 (sh_certificate_template)
```sql
CREATE TABLE `sh_certificate_template` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `event_id` bigint NOT NULL COMMENT '所属赛事/会议ID',
  `template_image` varchar(500) NOT NULL COMMENT '模板图片OSS ID',
  `template_config` text COMMENT '模板配置JSON（参数配置）',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '启用状态：1-启用，0-停用',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认：1-是，0-否',
  `template_type` tinyint DEFAULT 1 COMMENT '模板类型：1-奖状，2-证书',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  -- 标准字段
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`),
  -- 索引
  KEY `idx_event_id` (`event_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_is_enabled` (`is_enabled`),
  KEY `idx_is_default` (`is_default`),
  UNIQUE KEY `uk_event_default` (`event_id`, `is_default`, `del_flag`) COMMENT '每个事项只能有一个默认模板'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='奖状/证书模板表';
```

#### 2.1.2 证书/奖状生成记录表 (sh_certificate_generation)
```sql
CREATE TABLE `sh_certificate_generation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  `event_id` bigint NOT NULL COMMENT '事项ID',
  `template_id` bigint NOT NULL COMMENT '模板ID',
  `registration_id` bigint NOT NULL COMMENT '报名ID',
  `participant_name` varchar(100) NOT NULL COMMENT '参赛者姓名',
  `participant_phone` varchar(20) DEFAULT NULL COMMENT '参赛者手机号',
  `certificate_url` varchar(500) DEFAULT NULL COMMENT '证书文件URL（OSS ID）',
  `generation_status` tinyint DEFAULT 0 COMMENT '生成状态：0-待生成，1-生成中，2-生成成功，3-生成失败',
  `generation_params` text COMMENT '生成参数JSON',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误信息',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `download_count` int DEFAULT 0 COMMENT '下载次数',
  `last_download_time` datetime DEFAULT NULL COMMENT '最后下载时间',
  -- 标准字段
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`),
  -- 索引
  KEY `idx_event_id` (`event_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_registration_id` (`registration_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_generation_status` (`generation_status`),
  KEY `idx_participant_name` (`participant_name`),
  UNIQUE KEY `uk_event_registration_template` (`event_id`, `registration_id`, `template_id`, `del_flag`) COMMENT '同一报名记录使用同一模板只能生成一次证书'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='证书/奖状生成记录表';
```

#### 2.1.3 证书模板参数配置表 (sh_certificate_template_params)
```sql
CREATE TABLE `sh_certificate_template_params` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  `template_id` bigint NOT NULL COMMENT '模板ID',
  `param_key` varchar(100) NOT NULL COMMENT '参数键',
  `param_label` varchar(100) NOT NULL COMMENT '参数标签',
  `param_type` varchar(50) DEFAULT 'text' COMMENT '参数类型：text-文本，date-日期，number-数字，select-选择',
  `param_options` text COMMENT '参数选项（JSON格式，用于select类型）',
  `default_value` varchar(500) DEFAULT NULL COMMENT '默认值',
  `is_required` tinyint(1) DEFAULT 1 COMMENT '是否必填：1-必填，0-非必填',
  `position_x` int DEFAULT 0 COMMENT 'X坐标位置',
  `position_y` int DEFAULT 0 COMMENT 'Y坐标位置',
  `font_size` int DEFAULT 14 COMMENT '字体大小',
  `font_color` varchar(20) DEFAULT '#000000' COMMENT '字体颜色',
  `font_family` varchar(100) DEFAULT '微软雅黑' COMMENT '字体',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  -- 标准字段
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  UNIQUE KEY `uk_template_param` (`template_id`, `param_key`, `del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='证书模板参数配置表';
```

#### 2.1.4 批量生成任务表 (sh_certificate_batch_task)
```sql
CREATE TABLE `sh_certificate_batch_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  `task_name` varchar(200) NOT NULL COMMENT '任务名称',
  `event_id` bigint NOT NULL COMMENT '事项ID',
  `template_id` bigint NOT NULL COMMENT '模板ID',
  `total_count` int DEFAULT 0 COMMENT '总数量',
  `success_count` int DEFAULT 0 COMMENT '成功数量',
  `failed_count` int DEFAULT 0 COMMENT '失败数量',
  `task_status` tinyint DEFAULT 0 COMMENT '任务状态：0-待执行，1-执行中，2-已完成，3-失败',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `error_message` text COMMENT '错误信息',
  `generation_params` text COMMENT '生成参数JSON',
  `selected_registrations` text COMMENT '选中的报名ID列表（JSON数组）',
  `excluded_registrations` text COMMENT '排除的报名ID列表（JSON数组）',
  -- 标准字段
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`),
  KEY `idx_event_id` (`event_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='证书批量生成任务表';
```

### 2.2 数据字典

#### 2.2.1 模板类型 (template_type)
- `1`: 奖状
- `2`: 证书

#### 2.2.2 生成状态 (generation_status)
- `0`: 待生成
- `1`: 生成中
- `2`: 生成成功
- `3`: 生成失败

#### 2.2.3 任务状态 (task_status)
- `0`: 待执行
- `1`: 执行中
- `2`: 已完成
- `3`: 失败

#### 2.2.4 参数类型 (param_type)
- `text`: 文本
- `date`: 日期
- `number`: 数字
- `select`: 选择

## 3. API接口设计

### 3.1 证书模板管理接口

#### 3.1.1 查询模板列表
```
GET /event/certificate/template/list
参数: eventId, templateName, isEnabled, templateType, pageNum, pageSize
返回: PageRes<CertificateTemplateVO[]>
```

#### 3.1.2 获取模板详情
```
GET /event/certificate/template/{id}
返回: CertificateTemplateVO
```

#### 3.1.3 新增模板
```
POST /event/certificate/template
参数: CertificateTemplateForm
```

#### 3.1.4 更新模板
```
PUT /event/certificate/template
参数: CertificateTemplateForm
```

#### 3.1.5 删除模板
```
DELETE /event/certificate/template/{ids}
```

#### 3.1.6 设置默认模板
```
PUT /event/certificate/template/{templateId}/setDefault
```

#### 3.1.7 更新模板状态
```
PUT /event/certificate/template/{templateId}/status
参数: { isEnabled: number }
```

### 3.2 模板参数配置接口

#### 3.2.1 获取模板参数
```
GET /event/certificate/template/{templateId}/params
返回: CertificateTemplateParamsVO[]
```

#### 3.2.2 保存模板参数
```
POST /event/certificate/template/{templateId}/params
参数: CertificateTemplateParamsForm[]
```

### 3.3 证书生成接口

#### 3.3.1 批量生成证书
```
POST /event/certificate/generation/batch/generate
参数: BatchGenerateCertificatesRequest
```

#### 3.3.2 查询生成记录列表
```
GET /event/certificate/generation/list
参数: eventId, templateId, generationStatus, participantName, pageNum, pageSize
返回: PageRes<CertificateGenerationVO[]>
```

#### 3.3.3 下载证书
```
GET /event/certificate/generation/{id}/download
返回: 文件流
```

#### 3.3.4 批量下载证书
```
POST /event/certificate/generation/batch/download
参数: { eventId: number, templateId?: number }
返回: 压缩文件流
```

## 4. 前端界面设计

### 4.1 主界面入口
- 在事项列表的操作列中添加"奖状证书管理"按钮
- 图标使用 Medal，黄色样式

### 4.2 奖状/证书管理对话框
- 使用 Tabs 标签页布局
- 包含两个标签页：模板管理、证书生成

#### 4.2.1 模板管理标签页
**功能组件：**
- 搜索区域：模板名称、模板类型、启用状态
- 操作按钮：新增模板、批量导入
- 模板列表表格：
  - 模板信息（名称、类型、默认标识）
  - 模板预览（缩略图）
  - 启用状态（开关控件）
  - 排序
  - 创建时间
  - 操作按钮：查看、编辑、参数配置、设为默认、复制、删除

#### 4.2.2 证书生成标签页
**功能组件：**
- 证书配置区域：选择模板、批量生成按钮
- 生成记录区域：
  - 搜索：参赛者姓名、生成状态
  - 记录表格：参赛者信息、模板、状态、文件大小、下载次数、生成时间、操作
- 批量操作区域：批量下载、重新生成失败证书、批量删除

### 4.3 子对话框

#### 4.3.1 模板编辑对话框
- 基本信息：模板名称、模板类型、排序、启用状态、设为默认
- 模板图片上传
- 备注信息

#### 4.3.2 模板参数配置对话框
- 参数列表表格：参数键、标签、类型、默认值、位置、字体、必填、排序、操作
- 添加参数、预览效果按钮

#### 4.3.3 批量生成证书对话框
- 选择生成对象：报名人员列表（支持全选、清空、条件筛选、排除功能）
- 生成参数配置：根据模板参数动态生成表单

## 5. 业务流程

### 5.1 模板管理流程
1. 管理员进入事项管理，点击"奖状证书管理"
2. 在模板管理标签页中，可以新增/编辑模板
3. 为模板配置参数（位置、字体等）
4. 启用模板并可设为默认模板

### 5.2 证书生成流程
1. 在证书生成标签页选择模板
2. 点击批量生成，选择要生成证书的报名人员
3. 配置生成参数（如获奖等级、颁发日期等）
4. 提交批量生成任务
5. 系统后台异步生成证书文件
6. 生成完成后可下载单个或批量下载

### 5.3 证书下载流程
1. 在生成记录中找到已生成成功的证书
2. 点击下载按钮下载单个证书（以参赛者姓名命名）
3. 或使用批量下载功能下载所有证书（打包为ZIP文件）

## 6. 技术实现要点

### 6.1 前端实现要点
- 使用 Vue 3 Composition API + TypeScript
- 采用 Element Plus 组件库
- 响应式设计，支持移动端适配
- 图片上传使用 ImageUpload 组件
- 表格分页、搜索、筛选功能完整
- 批量操作支持进度提示

### 6.2 后端实现要点
- 使用 Spring Boot + MyBatis Plus
- 异步任务处理证书生成
- OSS 文件存储管理
- 图片处理和文字渲染技术
- 支持大量数据的批量操作
- 完善的权限控制和数据校验

### 6.3 文件存储设计
- 模板图片存储在 OSS
- 生成的证书文件存储在 OSS
- 支持文件过期清理机制
- 下载链接支持临时访问令牌

## 7. 测试用例

### 7.1 模板管理测试
- 新增模板（正常、异常情况）
- 编辑模板信息
- 参数配置功能
- 启用/停用模板
- 设置默认模板
- 删除模板

### 7.2 证书生成测试
- 单个证书生成
- 批量证书生成
- 异常情况处理（模板不存在、参数错误等）
- 任务状态跟踪

### 7.3 下载功能测试
- 单个证书下载
- 批量证书下载
- 文件名正确性
- 下载次数统计

## 8. 部署说明

### 8.1 数据库部署
1. 执行 SQL 脚本创建相关表
2. 插入基础数据和配置

### 8.2 前端部署
1. 安装依赖包
2. 配置 API 接口地址
3. 构建生产版本

### 8.3 后端部署
1. 配置 OSS 存储参数
2. 配置异步任务线程池
3. 部署应用服务

## 9. 维护说明

### 9.1 监控指标
- 证书生成成功率
- 生成任务执行时间
- 文件存储使用量
- 下载统计数据

### 9.2 日常维护
- 定期清理过期文件
- 监控存储使用情况
- 备份重要模板数据
- 性能优化调整

## 10. 扩展规划

### 10.1 功能扩展
- 支持更多模板格式（PDF、Word等）
- 证书验证码功能
- 证书上链存证
- 模板在线设计器
- 批量导入报名数据生成证书

### 10.2 性能优化
- 证书生成并发优化
- 缓存策略优化
- CDN 加速下载
- 分布式文件存储